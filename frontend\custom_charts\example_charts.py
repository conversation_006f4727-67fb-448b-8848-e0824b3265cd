"""
自定义图表示例 - 演示图表扩展功能
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from datetime import datetime


def render_3d_scatter_chart(data, height=400, **kwargs):
    """渲染3D散点图 - 展示产量、工期、成本的三维关系"""
    
    if not data:
        st.info("暂无数据")
        return
    
    # 数据处理
    df = pd.DataFrame(data)
    df['start_time'] = pd.to_datetime(df['start_time'])
    df['end_time'] = pd.to_datetime(df['end_time'])
    df['duration'] = (df['end_time'] - df['start_time']).dt.total_seconds() / 3600
    
    # 模拟成本数据（如果没有的话）
    if 'cost' not in df.columns:
        df['cost'] = df['quantity'] * np.random.uniform(50, 100, len(df))
    
    # 创建3D散点图
    fig = px.scatter_3d(
        df,
        x='quantity',
        y='duration', 
        z='cost',
        color='equipment_name',
        size='quantity',
        hover_data=['product_name', 'order_id'],
        title="生产计划3D分析 - 产量×工期×成本",
        labels={
            'quantity': '产量 (件)',
            'duration': '工期 (小时)',
            'cost': '成本 (元)'
        }
    )
    
    fig.update_layout(
        height=height,
        scene=dict(
            xaxis_title="产量 (件)",
            yaxis_title="工期 (小时)",
            zaxis_title="成本 (元)"
        )
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 统计信息
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("平均产量", f"{df['quantity'].mean():.0f} 件")
    
    with col2:
        st.metric("平均工期", f"{df['duration'].mean():.1f} 小时")
    
    with col3:
        st.metric("平均成本", f"¥{df['cost'].mean():.0f}")
    
    with col4:
        efficiency = df['quantity'].sum() / df['duration'].sum()
        st.metric("生产效率", f"{efficiency:.1f} 件/小时")


def render_heatmap_chart(data, height=400, **kwargs):
    """渲染热力图 - 设备×时间负载分布"""
    
    if not data:
        st.info("暂无数据")
        return
    
    # 创建设备-时间热力图
    df = pd.DataFrame(data)
    df['start_time'] = pd.to_datetime(df['start_time'])
    df['hour'] = df['start_time'].dt.hour
    df['date'] = df['start_time'].dt.date
    
    # 聚合数据 - 按设备和小时统计产量
    heatmap_data = df.groupby(['equipment_name', 'hour'])['quantity'].sum().reset_index()
    heatmap_pivot = heatmap_data.pivot(index='equipment_name', columns='hour', values='quantity').fillna(0)
    
    # 创建热力图
    fig = px.imshow(
        heatmap_pivot,
        title="设备-时间负载热力图",
        labels={'x': '小时', 'y': '设备', 'color': '产量'},
        color_continuous_scale='Viridis',
        aspect='auto'
    )
    
    fig.update_layout(
        height=height,
        xaxis_title="时间 (小时)",
        yaxis_title="设备"
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 负载分析
    col1, col2 = st.columns(2)
    
    with col1:
        # 设备总负载
        equipment_load = heatmap_pivot.sum(axis=1).sort_values(ascending=False)
        
        fig_load = px.bar(
            x=equipment_load.values,
            y=equipment_load.index,
            orientation='h',
            title="设备总负载排名",
            labels={'x': '总产量', 'y': '设备'}
        )
        
        fig_load.update_layout(height=300)
        st.plotly_chart(fig_load, use_container_width=True)
    
    with col2:
        # 时间段负载
        hour_load = heatmap_pivot.sum(axis=0)
        
        fig_time = px.line(
            x=hour_load.index,
            y=hour_load.values,
            title="时间段负载分布",
            markers=True,
            labels={'x': '小时', 'y': '总产量'}
        )
        
        fig_time.update_layout(height=300)
        st.plotly_chart(fig_time, use_container_width=True)


def render_correlation_matrix(data, height=400, **kwargs):
    """渲染相关性矩阵 - 分析各指标间的相关性"""
    
    if not data:
        st.info("暂无数据")
        return
    
    # 数据处理
    df = pd.DataFrame(data)
    df['start_time'] = pd.to_datetime(df['start_time'])
    df['end_time'] = pd.to_datetime(df['end_time'])
    df['duration'] = (df['end_time'] - df['start_time']).dt.total_seconds() / 3600
    
    # 添加模拟指标
    df['cost'] = df['quantity'] * np.random.uniform(50, 100, len(df))
    df['quality'] = np.random.uniform(90, 100, len(df))
    df['efficiency'] = df['quantity'] / df['duration']
    
    # 选择数值列
    numeric_cols = ['quantity', 'duration', 'cost', 'quality', 'efficiency']
    correlation_data = df[numeric_cols].corr()
    
    # 创建相关性热力图
    fig = px.imshow(
        correlation_data,
        title="生产指标相关性矩阵",
        color_continuous_scale='RdBu',
        zmin=-1,
        zmax=1,
        text_auto=True
    )
    
    fig.update_layout(
        height=height,
        xaxis_title="指标",
        yaxis_title="指标"
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 相关性分析
    st.markdown("##### 🔍 相关性分析")
    
    # 找出强相关性
    strong_correlations = []
    for i in range(len(correlation_data.columns)):
        for j in range(i+1, len(correlation_data.columns)):
            corr_value = correlation_data.iloc[i, j]
            if abs(corr_value) > 0.5:
                strong_correlations.append({
                    "指标1": correlation_data.columns[i],
                    "指标2": correlation_data.columns[j],
                    "相关系数": f"{corr_value:.3f}",
                    "相关性": "强正相关" if corr_value > 0.5 else "强负相关"
                })
    
    if strong_correlations:
        df_corr = pd.DataFrame(strong_correlations)
        st.dataframe(df_corr, use_container_width=True, hide_index=True)
    else:
        st.info("未发现强相关性（|r| > 0.5）")


def render_efficiency_radar(data, height=400, **kwargs):
    """渲染效率雷达图 - 多维度效率分析"""
    
    if not data:
        st.info("暂无数据")
        return
    
    # 数据处理
    df = pd.DataFrame(data)
    df['start_time'] = pd.to_datetime(df['start_time'])
    df['end_time'] = pd.to_datetime(df['end_time'])
    df['duration'] = (df['end_time'] - df['start_time']).dt.total_seconds() / 3600
    
    # 按设备计算效率指标
    equipment_metrics = []
    
    for equipment in df['equipment_name'].unique():
        eq_data = df[df['equipment_name'] == equipment]
        
        # 计算各项效率指标（0-100分）
        time_efficiency = min(100, (24 / eq_data['duration'].mean()) * 100)  # 时间效率
        quantity_efficiency = min(100, (eq_data['quantity'].mean() / 200) * 100)  # 产量效率
        utilization = min(100, len(eq_data) * 20)  # 利用率
        consistency = max(0, 100 - eq_data['duration'].std() * 10)  # 一致性
        
        equipment_metrics.append({
            'equipment': equipment,
            'time_efficiency': time_efficiency,
            'quantity_efficiency': quantity_efficiency,
            'utilization': utilization,
            'consistency': consistency
        })
    
    # 创建雷达图
    fig = go.Figure()
    
    categories = ['时间效率', '产量效率', '设备利用率', '执行一致性']
    
    for metric in equipment_metrics:
        values = [
            metric['time_efficiency'],
            metric['quantity_efficiency'], 
            metric['utilization'],
            metric['consistency']
        ]
        
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories,
            fill='toself',
            name=metric['equipment'],
            line=dict(width=2)
        ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100]
            )
        ),
        title="设备效率雷达图",
        height=height,
        showlegend=True
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 效率排名
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("##### 🏆 综合效率排名")
        
        # 计算综合得分
        for metric in equipment_metrics:
            metric['total_score'] = (
                metric['time_efficiency'] * 0.3 +
                metric['quantity_efficiency'] * 0.3 +
                metric['utilization'] * 0.2 +
                metric['consistency'] * 0.2
            )
        
        # 排序
        equipment_metrics.sort(key=lambda x: x['total_score'], reverse=True)
        
        for i, metric in enumerate(equipment_metrics):
            rank_emoji = ["🥇", "🥈", "🥉"][i] if i < 3 else f"{i+1}."
            st.write(f"{rank_emoji} {metric['equipment']}: {metric['total_score']:.1f}分")
    
    with col2:
        st.markdown("##### 📊 效率指标说明")
        st.write("**时间效率**: 完成任务的速度")
        st.write("**产量效率**: 单位时间产出")
        st.write("**设备利用率**: 设备使用频率")
        st.write("**执行一致性**: 性能稳定性")


def register_charts(registry):
    """注册自定义图表到系统"""
    
    # 注册3D散点图
    registry.register_chart(
        chart_id="3d_scatter",
        chart_name="3D散点图",
        render_func=render_3d_scatter_chart,
        description="三维散点分析，展示产量、工期、成本的关系",
        category="高级分析"
    )
    
    # 注册热力图
    registry.register_chart(
        chart_id="heatmap",
        chart_name="热力图",
        render_func=render_heatmap_chart,
        description="设备-时间负载热力图，显示负载分布模式",
        category="高级分析"
    )
    
    # 注册相关性矩阵
    registry.register_chart(
        chart_id="correlation",
        chart_name="相关性矩阵",
        render_func=render_correlation_matrix,
        description="分析各生产指标之间的相关性",
        category="高级分析"
    )
    
    # 注册效率雷达图
    registry.register_chart(
        chart_id="efficiency_radar",
        chart_name="效率雷达图",
        render_func=render_efficiency_radar,
        description="多维度设备效率分析雷达图",
        category="高级分析"
    )
