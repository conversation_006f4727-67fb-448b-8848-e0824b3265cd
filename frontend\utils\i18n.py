"""
国际化(i18n)支持模块
提供中英文界面翻译功能
"""

import streamlit as st
from typing import Dict, Any


class I18nManager:
    """国际化管理器"""

    def __init__(self):
        self.translations = {
            "zh": {
                # 通用
                "language": "语言",
                "chinese": "中文",
                "english": "English",
                "save": "保存",
                "cancel": "取消",
                "confirm": "确认",
                "delete": "删除",
                "edit": "编辑",
                "view": "查看",
                "create": "创建",
                "update": "更新",
                "search": "搜索",
                "filter": "筛选",
                "export": "导出",
                "import": "导入",
                "refresh": "刷新",
                "loading": "加载中...",
                "success": "成功",
                "error": "错误",
                "warning": "警告",
                "info": "信息",

                # 导航菜单
                "dashboard": "综合仪表板",
                "data_upload": "数据上传",
                "production_planning": "生产规划",
                "equipment_management": "设备管理",
                "plan_monitoring": "计划监控",
                "data_analysis": "数据分析",
                "ai_assistant": "智能助手",
                "user_management": "用户管理",
                "system_config": "系统配置",
                "algorithm_learning": "算法学习中心",
                "pci_management": "PCI管理",
                "data_integration": "数据集成演示",
                "data_source_management": "数据源管理",
                "algorithm_planning": "算法计划生成",
                "planning_extension": "规划引擎扩展",

                # 智能助手
                "ai_assistant": "智能助手",
                "chat_with_ai": "与AI对话",
                "send_message": "发送消息",
                "clear_chat": "清空对话",
                "chat_history": "对话历史",
                "ai_response": "AI回复",
                "user_message": "用户消息",
                "conversation_id": "对话ID",
                "model_selection": "模型选择",
                "temperature": "温度参数",
                "include_context": "包含数据上下文",
                "language_detection": "语言检测",
                "auto_detect": "自动检测",
                "assistant_config": "助手配置",
                "parameter_settings": "参数设置",
                "creativity": "创造性",
                "creativity_help": "控制回答的创造性，值越高越有创意",
                "max_response_length": "最大回复长度",
                "max_length_help": "控制AI回复的最大长度",
                "language_mode": "语言模式",

                # 扩展功能
                "extension_overview": "扩展概览",
                "logistics_extension": "物流扩展",
                "visual_config": "可视化配置",
                "template_management": "模板管理",
                "extension_effects": "扩展效果",
                "development_guide": "开发指南",
                "plugin_management": "插件管理",
                "register_plugin": "注册插件",
                "enable_plugin": "启用插件",
                "disable_plugin": "禁用插件",
                "plugin_status": "插件状态",
                "plugin_description": "插件描述",
                "plugin_version": "插件版本",

                # 配置类型
                "logistics_config": "物流配置",
                "environmental_config": "环保配置",
                "cost_config": "成本配置",
                "hr_config": "人力配置",
                "quality_config": "质量配置",
                "transport_config": "运输配置",
                "warehouse_config": "仓储配置",
                "delivery_config": "配送配置",

                # 模板操作
                "view_templates": "查看模板",
                "create_template": "创建模板",
                "edit_template": "编辑模板",
                "delete_template": "删除模板",
                "apply_template": "应用模板",
                "export_template": "导出模板",
                "template_name": "模板名称",
                "template_description": "模板描述",
                "template_content": "模板内容",
                "default_template": "默认模板",
                "visual_editing": "可视化编辑",
                "json_editing": "JSON编辑",

                # 状态和指标
                "running": "运行中",
                "stopped": "已停止",
                "maintenance": "维护中",
                "normal": "正常",
                "warning_status": "警告",
                "error_status": "错误",
                "utilization_rate": "利用率",
                "efficiency": "效率",
                "quality_rate": "合格率",
                "on_time_rate": "准时率",
                "cost_control": "成本控制",
                "performance": "性能",

                # 消息提示
                "config_saved": "配置已保存！",
                "config_load_failed": "配置加载失败",
                "template_created": "模板创建成功！",
                "template_applied": "模板应用成功！",
                "plugin_registered": "插件注册成功！",
                "plugin_enabled": "插件已启用",
                "plugin_disabled": "插件已禁用",
                "service_unavailable": "服务不可用",
                "operation_successful": "操作成功",
                "operation_failed": "操作失败",

                # 帮助文本
                "select_config_type": "选择要配置的扩展类型",
                "select_template_operation": "选择要执行的模板管理操作",
                "enter_template_name": "请输入模板名称",
                "enter_template_description": "请输入模板描述",
                "json_format_error": "JSON格式错误",
                "json_format_correct": "JSON格式正确",
                "fill_required_fields": "请填写必填字段",
                "confirm_delete_operation": "删除操作不可恢复，请谨慎操作",

                # 测试页面
                "interface_test": "界面测试",
                "llm_test": "LLM测试",
                "function_demo": "功能演示",
                "test_translation": "测试翻译",
                "test_llm_dialogue": "测试LLM对话",
                "interface_translation_test": "界面翻译测试",
                "llm_dialogue_test": "LLM对话测试",
                "test_message": "测试消息",
                "test_language": "测试语言",
                "test_llm_response": "测试LLM回复",
                "generating_response": "正在生成回复...",
                "response_generated": "回复生成成功",
                "detected_language": "检测语言",
                "test_failed": "测试失败",
                "please_enter_message": "请输入测试消息",
                "select_scenario": "选择场景",
                "demo_scenario": "演示场景",
                "demo_message": "演示消息",
                "generating_demo_response": "正在生成演示回复...",
                "demo_failed": "演示失败",
                "supported_languages": "支持语言",
                "response_templates": "回复模板",
                "ui_translations": "界面翻译",
                "inventory_management": "库存管理",

                # 认证配置
                "auth_overview": "认证概览",
                "ldap_config": "LDAP配置",
                "sso_config": "SSO配置",
                "auth_test": "认证测试",
            },

            "en": {
                # 通用
                "language": "Language",
                "chinese": "中文",
                "english": "English",
                "save": "Save",
                "cancel": "Cancel",
                "confirm": "Confirm",
                "delete": "Delete",
                "edit": "Edit",
                "view": "View",
                "create": "Create",
                "update": "Update",
                "search": "Search",
                "filter": "Filter",
                "export": "Export",
                "import": "Import",
                "refresh": "Refresh",
                "loading": "Loading...",
                "success": "Success",
                "error": "Error",
                "warning": "Warning",
                "info": "Info",

                # 导航菜单
                "dashboard": "Dashboard",
                "data_upload": "Data Upload",
                "production_planning": "Production Planning",
                "equipment_management": "Equipment Management",
                "plan_monitoring": "Plan Monitoring",
                "data_analysis": "Data Analysis",
                "ai_assistant": "AI Assistant",
                "user_management": "User Management",
                "system_config": "System Configuration",
                "algorithm_learning": "Algorithm Learning Center",
                "pci_management": "PCI Management",
                "data_integration": "Data Integration Demo",
                "data_source_management": "Data Source Management",
                "algorithm_planning": "Algorithm Planning",
                "planning_extension": "Planning Engine Extension",

                # 智能助手
                "ai_assistant": "AI Assistant",
                "chat_with_ai": "Chat with AI",
                "send_message": "Send Message",
                "clear_chat": "Clear Chat",
                "chat_history": "Chat History",
                "ai_response": "AI Response",
                "user_message": "User Message",
                "conversation_id": "Conversation ID",
                "model_selection": "Model Selection",
                "temperature": "Temperature",
                "include_context": "Include Data Context",
                "language_detection": "Language Detection",
                "auto_detect": "Auto Detect",
                "assistant_config": "Assistant Configuration",
                "parameter_settings": "Parameter Settings",
                "creativity": "Creativity",
                "creativity_help": "Control response creativity, higher values are more creative",
                "max_response_length": "Max Response Length",
                "max_length_help": "Control the maximum length of AI responses",
                "language_mode": "Language Mode",

                # 扩展功能
                "extension_overview": "Extension Overview",
                "logistics_extension": "Logistics Extension",
                "visual_config": "Visual Configuration",
                "template_management": "Template Management",
                "extension_effects": "Extension Effects",
                "development_guide": "Development Guide",
                "plugin_management": "Plugin Management",
                "register_plugin": "Register Plugin",
                "enable_plugin": "Enable Plugin",
                "disable_plugin": "Disable Plugin",
                "plugin_status": "Plugin Status",
                "plugin_description": "Plugin Description",
                "plugin_version": "Plugin Version",

                # 配置类型
                "logistics_config": "Logistics Configuration",
                "environmental_config": "Environmental Configuration",
                "cost_config": "Cost Configuration",
                "hr_config": "HR Configuration",
                "quality_config": "Quality Configuration",
                "transport_config": "Transport Configuration",
                "warehouse_config": "Warehouse Configuration",
                "delivery_config": "Delivery Configuration",

                # 模板操作
                "view_templates": "View Templates",
                "create_template": "Create Template",
                "edit_template": "Edit Template",
                "delete_template": "Delete Template",
                "apply_template": "Apply Template",
                "export_template": "Export Template",
                "template_name": "Template Name",
                "template_description": "Template Description",
                "template_content": "Template Content",
                "default_template": "Default Template",
                "visual_editing": "Visual Editing",
                "json_editing": "JSON Editing",

                # 状态和指标
                "running": "Running",
                "stopped": "Stopped",
                "maintenance": "Maintenance",
                "normal": "Normal",
                "warning_status": "Warning",
                "error_status": "Error",
                "utilization_rate": "Utilization Rate",
                "efficiency": "Efficiency",
                "quality_rate": "Quality Rate",
                "on_time_rate": "On-time Rate",
                "cost_control": "Cost Control",
                "performance": "Performance",

                # 消息提示
                "config_saved": "Configuration saved!",
                "config_load_failed": "Failed to load configuration",
                "template_created": "Template created successfully!",
                "template_applied": "Template applied successfully!",
                "plugin_registered": "Plugin registered successfully!",
                "plugin_enabled": "Plugin enabled",
                "plugin_disabled": "Plugin disabled",
                "service_unavailable": "Service unavailable",
                "operation_successful": "Operation successful",
                "operation_failed": "Operation failed",

                # 帮助文本
                "select_config_type": "Select the extension type to configure",
                "select_template_operation": "Select the template management operation to perform",
                "enter_template_name": "Please enter template name",
                "enter_template_description": "Please enter template description",
                "json_format_error": "JSON format error",
                "json_format_correct": "JSON format correct",
                "fill_required_fields": "Please fill in required fields",
                "confirm_delete_operation": "Delete operation cannot be undone, please proceed with caution",

                # 测试页面
                "interface_test": "Interface Test",
                "llm_test": "LLM Test",
                "function_demo": "Function Demo",
                "test_translation": "Test Translation",
                "test_llm_dialogue": "Test LLM Dialogue",
                "interface_translation_test": "Interface Translation Test",
                "llm_dialogue_test": "LLM Dialogue Test",
                "test_message": "Test Message",
                "test_language": "Test Language",
                "test_llm_response": "Test LLM Response",
                "generating_response": "Generating response...",
                "response_generated": "Response generated successfully",
                "detected_language": "Detected Language",
                "test_failed": "Test failed",
                "please_enter_message": "Please enter test message",
                "select_scenario": "Select Scenario",
                "demo_scenario": "Demo Scenario",
                "demo_message": "Demo Message",
                "generating_demo_response": "Generating demo response...",
                "demo_failed": "Demo failed",
                "supported_languages": "Supported Languages",
                "response_templates": "Response Templates",
                "ui_translations": "UI Translations",
                "inventory_management": "Inventory Management",

                # 认证配置
                "auth_overview": "Authentication Overview",
                "ldap_config": "LDAP Configuration",
                "sso_config": "SSO Configuration",
                "auth_test": "Authentication Test",
            }
        }

        # 初始化语言设置
        if "language" not in st.session_state:
            st.session_state.language = "zh"

    def get_current_language(self) -> str:
        """获取当前语言"""
        return st.session_state.get("language", "zh")

    def set_language(self, language: str):
        """设置语言"""
        if language in self.translations:
            st.session_state.language = language

    def t(self, key: str, **kwargs) -> str:
        """翻译文本"""
        current_lang = self.get_current_language()

        # 获取翻译文本
        translation = self.translations.get(current_lang, {}).get(key, key)

        # 如果当前语言没有翻译，尝试使用中文
        if translation == key and current_lang != "zh":
            translation = self.translations.get("zh", {}).get(key, key)

        # 格式化参数
        if kwargs:
            try:
                translation = translation.format(**kwargs)
            except:
                pass

        return translation

    def language_selector(self, key: str = "language_selector"):
        """语言选择器组件"""
        current_lang = self.get_current_language()

        # 语言选项
        language_options = {
            "zh": "🇨🇳 中文",
            "en": "🇺🇸 English"
        }

        # 当前选择的显示文本
        current_display = language_options[current_lang]

        # 选择框
        selected_display = st.selectbox(
            self.t("language"),
            options=list(language_options.values()),
            index=list(language_options.keys()).index(current_lang),
            key=key
        )

        # 获取选择的语言代码
        selected_lang = None
        for lang_code, display_text in language_options.items():
            if display_text == selected_display:
                selected_lang = lang_code
                break

        # 如果语言改变，更新设置
        if selected_lang and selected_lang != current_lang:
            self.set_language(selected_lang)
            st.rerun()

        return selected_lang


# 全局国际化管理器实例
i18n = I18nManager()
