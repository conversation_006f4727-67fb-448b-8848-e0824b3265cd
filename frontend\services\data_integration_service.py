"""
数据集成服务
统一整合所有数据源，为LLM和算法提供完整的数据上下文
"""

import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import sqlite3
import os

from .equipment_data_service import EquipmentDataService
from .pci_service import PCIService
from .learning_engine import LearningEngine


class DataIntegrationService:
    """数据集成服务类 - 支持插件化扩展"""

    def __init__(self):
        # 核心数据服务
        self.equipment_service = EquipmentDataService()
        self.pci_service = PCIService()
        self.learning_engine = LearningEngine()
        self.upload_db_path = "upload_data.db"

        # 插件化数据源管理
        self.data_source_plugins = {}
        self.custom_data_sources = {}

        # 初始化
        self._init_upload_database()
        self._register_core_data_sources()
        self._load_custom_data_sources()

    def _init_upload_database(self):
        """初始化上传数据数据库"""
        conn = sqlite3.connect(self.upload_db_path)
        cursor = conn.cursor()

        # 创建上传文件记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS uploaded_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                file_type TEXT NOT NULL,
                file_size INTEGER,
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_time TIMESTAMP,
                status TEXT DEFAULT 'uploaded',
                description TEXT,
                extracted_data TEXT,
                user_id TEXT
            )
        ''')

        # 创建提取的表格数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS extracted_tables (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id INTEGER,
                table_name TEXT,
                table_data TEXT,
                row_count INTEGER,
                column_count INTEGER,
                data_type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (file_id) REFERENCES uploaded_files (id)
            )
        ''')

        # 创建用户输入数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_inputs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                input_type TEXT NOT NULL,
                input_data TEXT NOT NULL,
                source_page TEXT,
                user_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建数据源配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_source_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_name TEXT UNIQUE NOT NULL,
                source_type TEXT NOT NULL,
                config_data TEXT,
                enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

    def _register_core_data_sources(self):
        """注册核心数据源"""
        # 这些是系统内置的核心数据源
        self.data_source_plugins["equipment"] = {
            "name": "设备数据",
            "type": "core",
            "description": "生产线和储罐设备状态数据",
            "handler": self._get_equipment_context,
            "enabled": True
        }

        self.data_source_plugins["pci"] = {
            "name": "PCI数据",
            "type": "core",
            "description": "FS库存和FIFO消耗数据",
            "handler": self._get_pci_context,
            "enabled": True
        }

        self.data_source_plugins["uploaded_files"] = {
            "name": "上传文件",
            "type": "core",
            "description": "Excel、CSV、邮件等上传文件数据",
            "handler": self._get_uploaded_files_context,
            "enabled": True
        }

        self.data_source_plugins["user_inputs"] = {
            "name": "用户输入",
            "type": "core",
            "description": "用户手动输入和配置数据",
            "handler": self._get_user_inputs_context,
            "enabled": True
        }

        self.data_source_plugins["learning"] = {
            "name": "学习引擎",
            "type": "core",
            "description": "算法学习和性能数据",
            "handler": self._get_learning_context,
            "enabled": True
        }

    def _load_custom_data_sources(self):
        """加载自定义数据源配置"""
        conn = sqlite3.connect(self.upload_db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT source_name, source_type, config_data, enabled
            FROM data_source_config
            WHERE enabled = 1
        ''')

        for row in cursor.fetchall():
            source_name, source_type, config_data, enabled = row
            try:
                config = json.loads(config_data) if config_data else {}
                self.custom_data_sources[source_name] = {
                    "type": source_type,
                    "config": config,
                    "enabled": bool(enabled)
                }
            except Exception as e:
                print(f"加载自定义数据源 {source_name} 失败: {e}")

        conn.close()

    def register_data_source_plugin(self, plugin: Any):
        """注册新的数据源插件"""
        self.data_source_plugins[plugin.name] = {
            "name": plugin.name,
            "type": "plugin",
            "description": plugin.description,
            "handler": plugin.get_data_context,
            "constraints_handler": plugin.get_constraints,
            "recommendations_handler": plugin.get_recommendations,
            "enabled": plugin.enabled,
            "plugin_instance": plugin
        }

    def add_custom_data_source(self, source_name: str, source_type: str,
                              config: Dict[str, Any], enabled: bool = True):
        """添加自定义数据源"""
        # 验证输入参数
        if not source_name or not source_name.strip():
            raise ValueError("数据源名称不能为空")

        if source_type not in ["database", "api", "file", "sensor"]:
            raise ValueError(f"不支持的数据源类型: {source_type}")

        # 验证配置
        validation_result = self._validate_data_source_config(source_type, config)
        if not validation_result["valid"]:
            raise ValueError(f"配置验证失败: {validation_result['error']}")

        try:
            conn = sqlite3.connect(self.upload_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO data_source_config
                (source_name, source_type, config_data, enabled, updated_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                source_name,
                source_type,
                json.dumps(config),
                int(enabled),
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

            # 更新内存中的配置
            self.custom_data_sources[source_name] = {
                "type": source_type,
                "config": config,
                "enabled": enabled
            }
        except Exception as e:
            raise Exception(f"添加数据源失败: {str(e)}")

    def _validate_data_source_config(self, source_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证数据源配置"""
        if source_type == "database":
            required_fields = ["connection"]
            if "connection" in config:
                conn_config = config["connection"]
                required_conn_fields = ["type", "host", "database"]
                for field in required_conn_fields:
                    if not conn_config.get(field):
                        return {"valid": False, "error": f"数据库配置缺少必需字段: {field}"}

        elif source_type == "api":
            if not config.get("endpoint"):
                return {"valid": False, "error": "API配置缺少端点地址"}

        elif source_type == "file":
            if not config.get("file_path"):
                return {"valid": False, "error": "文件配置缺少文件路径"}

        elif source_type == "sensor":
            if not config.get("address"):
                return {"valid": False, "error": "传感器配置缺少设备地址"}

        return {"valid": True, "error": None}

    def get_available_data_sources(self) -> List[Dict[str, Any]]:
        """获取所有可用的数据源"""
        sources = []

        # 核心数据源
        for name, plugin in self.data_source_plugins.items():
            if plugin.get("enabled", True):
                sources.append({
                    "name": name,
                    "display_name": plugin["name"],
                    "type": plugin["type"],
                    "description": plugin["description"],
                    "enabled": plugin["enabled"]
                })

        # 自定义数据源
        for name, source in self.custom_data_sources.items():
            if source.get("enabled", True):
                sources.append({
                    "name": name,
                    "display_name": name,
                    "type": source["type"],
                    "description": f"自定义{source['type']}数据源",
                    "enabled": source["enabled"]
                })

        return sources

    def get_comprehensive_data_context(self) -> Dict[str, Any]:
        """获取综合数据上下文，供LLM使用 - 支持插件化扩展"""
        context = {
            "timestamp": datetime.now().isoformat(),
            "data_sources": {},
            "summary": {},
            "constraints": [],
            "recommendations": []
        }

        # 获取所有启用的数据源数据
        for source_name, plugin in self.data_source_plugins.items():
            if plugin.get("enabled", True):
                try:
                    context["data_sources"][source_name] = plugin["handler"]()
                except Exception as e:
                    print(f"获取数据源 {source_name} 数据失败: {e}")
                    context["data_sources"][source_name] = {"error": str(e)}

        # 获取自定义数据源数据
        for source_name, source_config in self.custom_data_sources.items():
            if source_config.get("enabled", True):
                try:
                    custom_data = self._get_custom_data_source_context(source_name, source_config)
                    context["data_sources"][source_name] = custom_data
                except Exception as e:
                    print(f"获取自定义数据源 {source_name} 数据失败: {e}")
                    context["data_sources"][source_name] = {"error": str(e)}

        # 生成数据摘要
        context["summary"] = self._generate_data_summary(context["data_sources"])

        # 识别约束条件（包括插件约束）
        context["constraints"] = self._identify_constraints(context["data_sources"])

        # 生成建议（包括插件建议）
        context["recommendations"] = self._generate_recommendations(context["data_sources"])

        return context

    def _get_custom_data_source_context(self, source_name: str, source_config: Dict[str, Any]) -> Dict[str, Any]:
        """获取自定义数据源上下文"""
        source_type = source_config.get("type", "unknown")
        config = source_config.get("config", {})

        # 根据数据源类型处理
        if source_type == "database":
            return self._get_database_source_context(source_name, config)
        elif source_type == "api":
            return self._get_api_source_context(source_name, config)
        elif source_type == "file":
            return self._get_file_source_context(source_name, config)
        elif source_type == "sensor":
            return self._get_sensor_source_context(source_name, config)
        else:
            return {"type": source_type, "config": config, "data": "未实现的数据源类型"}

    def _get_database_source_context(self, source_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取数据库数据源上下文"""
        # 这里可以连接外部数据库获取数据
        return {
            "type": "database",
            "source_name": source_name,
            "connection_info": config.get("connection", {}),
            "tables": config.get("tables", []),
            "last_sync": datetime.now().isoformat(),
            "status": "connected"
        }

    def _get_api_source_context(self, source_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取API数据源上下文"""
        # 这里可以调用外部API获取数据
        return {
            "type": "api",
            "source_name": source_name,
            "endpoint": config.get("endpoint", ""),
            "last_call": datetime.now().isoformat(),
            "status": "available"
        }

    def _get_file_source_context(self, source_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取文件数据源上下文"""
        # 这里可以读取指定文件获取数据
        return {
            "type": "file",
            "source_name": source_name,
            "file_path": config.get("file_path", ""),
            "last_modified": datetime.now().isoformat(),
            "status": "accessible"
        }

    def _get_sensor_source_context(self, source_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取传感器数据源上下文"""
        # 这里可以连接IoT传感器获取数据
        return {
            "type": "sensor",
            "source_name": source_name,
            "sensor_id": config.get("sensor_id", ""),
            "last_reading": datetime.now().isoformat(),
            "status": "online"
        }

    def _get_equipment_context(self) -> Dict[str, Any]:
        """获取设备数据上下文"""
        equipment_data = self.equipment_service.get_equipment_for_planning()

        return {
            "available_equipment": equipment_data.get("available_equipment", []),
            "unavailable_equipment": equipment_data.get("unavailable_equipment", []),
            "capacity_summary": equipment_data.get("capacity_summary", {}),
            "constraints": equipment_data.get("constraints", []),
            "status_summary": self.equipment_service.get_equipment_status_summary()
        }

    def _get_pci_context(self) -> Dict[str, Any]:
        """获取PCI数据上下文"""
        # 获取FIFO数据
        fifo_data = self.pci_service.get_fs_data_with_fifo(180)

        # 获取查询历史
        query_history = self.pci_service.get_query_history(10)

        # 分析PCI数据
        pci_analysis = self._analyze_pci_data(fifo_data)

        return {
            "fs_inventory": fifo_data,
            "recent_queries": query_history,
            "analysis": pci_analysis,
            "consumption_priorities": [
                {
                    "fs_id": item["fs_id"],
                    "priority": item["consumption_priority"],
                    "reason": self._get_consumption_reason(item)
                }
                for item in fifo_data[:10]  # 前10个优先级最高的
            ]
        }

    def _get_uploaded_files_context(self) -> Dict[str, Any]:
        """获取上传文件数据上下文"""
        conn = sqlite3.connect(self.upload_db_path)
        cursor = conn.cursor()

        # 获取最近上传的文件
        cursor.execute('''
            SELECT * FROM uploaded_files
            WHERE status = 'processed'
            ORDER BY processed_time DESC
            LIMIT 20
        ''')

        files = []
        for row in cursor.fetchall():
            file_data = {
                "id": row[0],
                "filename": row[1],
                "file_type": row[2],
                "upload_time": row[4],
                "description": row[7]
            }

            # 获取提取的表格数据
            cursor.execute('''
                SELECT table_name, table_data, row_count, column_count, data_type
                FROM extracted_tables WHERE file_id = ?
            ''', (row[0],))

            tables = []
            for table_row in cursor.fetchall():
                table_data = json.loads(table_row[1]) if table_row[1] else []
                tables.append({
                    "name": table_row[0],
                    "data": table_data,
                    "rows": table_row[2],
                    "columns": table_row[3],
                    "type": table_row[4]
                })

            file_data["tables"] = tables
            files.append(file_data)

        conn.close()

        return {
            "recent_files": files,
            "total_files": len(files),
            "data_types": list(set([f["file_type"] for f in files]))
        }

    def _get_user_inputs_context(self) -> Dict[str, Any]:
        """获取用户输入数据上下文"""
        conn = sqlite3.connect(self.upload_db_path)
        cursor = conn.cursor()

        # 获取最近的用户输入
        cursor.execute('''
            SELECT input_type, input_data, source_page, created_at
            FROM user_inputs
            ORDER BY created_at DESC
            LIMIT 50
        ''')

        inputs = []
        for row in cursor.fetchall():
            try:
                input_data = json.loads(row[1])
            except:
                input_data = row[1]

            inputs.append({
                "type": row[0],
                "data": input_data,
                "source": row[2],
                "timestamp": row[3]
            })

        conn.close()

        # 按类型分组
        grouped_inputs = {}
        for inp in inputs:
            input_type = inp["type"]
            if input_type not in grouped_inputs:
                grouped_inputs[input_type] = []
            grouped_inputs[input_type].append(inp)

        return {
            "recent_inputs": inputs,
            "grouped_by_type": grouped_inputs,
            "input_types": list(grouped_inputs.keys())
        }

    def _get_learning_context(self) -> Dict[str, Any]:
        """获取学习引擎上下文"""
        learning_status = self.learning_engine.get_learning_status()

        return {
            "status": learning_status,
            "models_available": learning_status.get("models_loaded", []),
            "learning_enabled": learning_status.get("enabled", False),
            "sample_count": learning_status.get("total_samples", 0)
        }

    def _analyze_pci_data(self, fifo_data: List[Dict]) -> Dict[str, Any]:
        """分析PCI数据"""
        if not fifo_data:
            return {"total_items": 0, "alerts": []}

        total_quantity = sum(item.get("quantity", 0) for item in fifo_data)
        old_items = [item for item in fifo_data if item.get("days_old", 0) > 180]
        high_priority = [item for item in fifo_data if item.get("consumption_priority", 0) > 80]

        alerts = []
        if len(old_items) > 0:
            alerts.append(f"有{len(old_items)}个物料超过180天，需要优先消耗")

        if len(high_priority) > 10:
            alerts.append(f"有{len(high_priority)}个高优先级消耗物料")

        return {
            "total_items": len(fifo_data),
            "total_quantity": total_quantity,
            "old_items_count": len(old_items),
            "high_priority_count": len(high_priority),
            "alerts": alerts,
            "avg_age": sum(item.get("days_old", 0) for item in fifo_data) / len(fifo_data)
        }

    def _get_consumption_reason(self, item: Dict[str, Any]) -> str:
        """获取消耗原因"""
        priority = item.get("consumption_priority", 0)
        days_old = item.get("days_old", 0)

        if days_old > 180:
            return f"库龄{days_old:.0f}天，超过FIFO阈值"
        elif priority > 80:
            return "高优先级消耗"
        elif item.get("urgency_level") == "high":
            return "紧急程度高"
        else:
            return "正常消耗顺序"

    def _generate_data_summary(self, data_sources: Dict[str, Any]) -> Dict[str, Any]:
        """生成数据摘要"""
        equipment_data = data_sources.get("equipment", {})
        pci_data = data_sources.get("pci", {})
        files_data = data_sources.get("uploaded_files", {})

        return {
            "equipment_status": {
                "total": len(equipment_data.get("available_equipment", [])) + len(equipment_data.get("unavailable_equipment", [])),
                "available": len(equipment_data.get("available_equipment", [])),
                "unavailable": len(equipment_data.get("unavailable_equipment", []))
            },
            "pci_status": {
                "total_fs_items": pci_data.get("analysis", {}).get("total_items", 0),
                "old_items": pci_data.get("analysis", {}).get("old_items_count", 0),
                "high_priority": pci_data.get("analysis", {}).get("high_priority_count", 0)
            },
            "data_files": {
                "recent_files": files_data.get("total_files", 0),
                "file_types": files_data.get("data_types", [])
            }
        }

    def _identify_constraints(self, data_sources: Dict[str, Any]) -> List[Dict[str, Any]]:
        """识别约束条件"""
        constraints = []

        # 设备约束
        equipment_constraints = data_sources.get("equipment", {}).get("constraints", [])
        constraints.extend(equipment_constraints)

        # PCI约束
        pci_analysis = data_sources.get("pci", {}).get("analysis", {})
        if pci_analysis.get("old_items_count", 0) > 0:
            constraints.append({
                "type": "material_constraint",
                "description": f"有{pci_analysis['old_items_count']}个物料超过FIFO阈值，需要优先消耗",
                "impact": "影响物料使用顺序和生产计划"
            })

        return constraints

    def _generate_recommendations(self, data_sources: Dict[str, Any]) -> List[str]:
        """生成建议"""
        recommendations = []

        # 设备建议
        equipment_data = data_sources.get("equipment", {})
        if equipment_data.get("capacity_summary", {}).get("production_lines", {}).get("utilization_rate", 0) > 90:
            recommendations.append("生产线利用率很高，建议优化排程避免过载")

        # PCI建议
        pci_analysis = data_sources.get("pci", {}).get("analysis", {})
        if pci_analysis.get("old_items_count", 0) > 0:
            recommendations.append("建议优先安排老旧物料的生产任务")

        # 学习建议
        learning_data = data_sources.get("learning", {})
        if learning_data.get("sample_count", 0) > 50 and learning_data.get("learning_enabled", False):
            recommendations.append("数据样本充足，建议启用算法学习优化")

        return recommendations

    def record_user_input(self, input_type: str, input_data: Any, source_page: str, user_id: str = None):
        """记录用户输入数据"""
        try:
            conn = sqlite3.connect(self.upload_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO user_inputs (input_type, input_data, source_page, user_id)
                VALUES (?, ?, ?, ?)
            ''', (
                input_type,
                json.dumps(input_data) if isinstance(input_data, (dict, list)) else str(input_data),
                source_page,
                user_id
            ))

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"记录用户输入失败: {e}")
            # 确保连接关闭
            try:
                conn.close()
            except:
                pass

    def get_llm_prompt_context(self) -> str:
        """获取LLM提示上下文"""
        data_context = self.get_comprehensive_data_context()

        prompt = f"""
当前系统数据概览（{data_context['timestamp']}）：

设备状态：
- 总设备数：{data_context['summary']['equipment_status']['total']}台
- 可用设备：{data_context['summary']['equipment_status']['available']}台
- 不可用设备：{data_context['summary']['equipment_status']['unavailable']}台

PCI库存：
- FS物料总数：{data_context['summary']['pci_status']['total_fs_items']}个
- 超龄物料：{data_context['summary']['pci_status']['old_items']}个
- 高优先级：{data_context['summary']['pci_status']['high_priority']}个

数据文件：
- 最近处理文件：{data_context['summary']['data_files']['recent_files']}个
- 文件类型：{', '.join(data_context['summary']['data_files']['file_types']) if data_context['summary']['data_files']['file_types'] else '无'}

约束条件：
"""

        for constraint in data_context['constraints']:
            prompt += f"- {constraint.get('description', constraint.get('reason', '未知约束'))}\n"

        prompt += "\n建议：\n"
        for recommendation in data_context['recommendations']:
            prompt += f"- {recommendation}\n"

        return prompt

    def get_algorithm_input_data(self) -> Dict[str, Any]:
        """获取算法输入数据"""
        data_context = self.get_comprehensive_data_context()

        return {
            "equipment": {
                "available_capacity": data_context['data_sources']['equipment']['capacity_summary'],
                "constraints": data_context['data_sources']['equipment']['constraints']
            },
            "materials": {
                "fs_inventory": data_context['data_sources']['pci']['fs_inventory'],
                "consumption_priorities": data_context['data_sources']['pci']['consumption_priorities']
            },
            "historical_data": {
                "learning_status": data_context['data_sources']['learning']['status'],
                "uploaded_data": data_context['data_sources']['uploaded_files']['recent_files']
            },
            "constraints": data_context['constraints'],
            "optimization_targets": {
                "delivery": 0.4,
                "efficiency": 0.3,
                "cost": 0.3
            }
        }


# 全局数据集成服务实例
data_integration_service = DataIntegrationService()
