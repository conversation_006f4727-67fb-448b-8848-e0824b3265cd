# 📚 Smart Planning 系统文档中心

欢迎来到Smart Planning智能生产管理系统的文档中心！这里包含了系统的完整文档，帮助您快速了解和使用系统。

## 📋 文档目录

### 🏗️ 系统架构文档
- [**系统架构总览**](./architecture/system_architecture.md) - 系统整体架构设计

### 📖 用户指南
- [**快速开始指南**](./user_guide/quick_start.md) - 系统快速上手指南
- [**功能使用指南**](./user_guide/feature_guide.md) - 详细功能使用说明

### 🔧 技术文档
- [**开发指南**](./technical/development_guide.md) - 开发环境搭建和开发指南
- [**API文档**](./technical/api_documentation.md) - 系统API接口文档
- [**扩展开发指南**](./technical/extension_development.md) - 插件和扩展开发指南
- [**系统概览**](./technical/system_overview.md) - 技术架构概览
- [**技术设计**](./technical/technical_design.md) - 详细技术设计
- [**前端开发指南**](./technical/frontend_development.md) - 前端开发说明
- [**需求分析**](./technical/requirements_analysis.md) - 系统需求分析
- [**技术评估**](./technical/technical_evaluation.md) - 技术方案评估

### 🗄️ 数据库文档
- [**数据库配置指南**](./technical/database_configuration_guide.md) - 数据库配置说明
- [**多数据库配置**](./technical/multi_database_configuration.md) - 多数据库支持
- [**数据库系统检查**](./technical/database_system_review.md) - 数据库系统检查报告
- [**配置架构检查**](./technical/configuration_architecture_review.md) - 配置架构检查
- [**配置文件检查**](./technical/configuration_files_review.md) - 配置文件检查报告

### 📧 邮件功能文档
- [**邮件配置指南**](./technical/email_configuration_guide.md) - 邮件服务器配置和使用指南
- [**邮件系统集成检查**](./technical/email_system_integration_review.md) - 邮件功能完整性检查报告
- [**系统名称替换报告**](./technical/system_rename_report.md) - 系统名称替换完成报告

### 📊 使用示例
- [**数据配置示例**](./examples/data_configuration_examples.md) - 数据源、筛选、查询配置完整示例 ⭐ **新增**
- [**数据库查询示例**](./examples/database_query_examples.md) - 数据库查询使用示例
- [**邮件功能使用示例**](./examples/email_usage_examples.md) - 邮件功能使用示例

## 🎯 快速导航

### 新用户推荐阅读顺序
1. [系统架构总览](./architecture/system_architecture.md) - 了解系统整体设计
2. [快速开始指南](./user_guide/quick_start.md) - 快速上手使用
3. [功能使用指南](./user_guide/feature_guide.md) - 深入了解功能

### 开发者推荐阅读顺序
1. [系统架构总览](./architecture/system_architecture.md) - 了解技术架构
2. [开发指南](./technical/development_guide.md) - 搭建开发环境
3. [API文档](./technical/api_documentation.md) - 了解接口设计
4. [扩展开发指南](./technical/extension_development.md) - 开发自定义功能

### 系统管理员推荐阅读顺序
1. [系统架构总览](./architecture/system_architecture.md) - 了解系统架构
2. [数据库配置指南](./technical/database_configuration_guide.md) - 配置数据库
3. [多数据库配置](./technical/multi_database_configuration.md) - 多数据库支持
4. [配置文件检查](./technical/configuration_files_review.md) - 配置检查

## 📝 文档更新记录

### 最新更新 (2024-01-XX)
- ✅ 完成数据库多类型支持配置
- ✅ 添加前端界面数据库配置功能
- ✅ 完善系统配置架构检查
- ✅ 更新文档与实际系统架构一致性

### 文档维护
- 📅 **更新频率**: 随系统更新同步更新
- 👥 **维护团队**: Smart Planning开发团队
- 📧 **反馈渠道**: 通过系统内智能助手反馈

## 🔍 文档搜索

如果您在寻找特定信息，可以：

1. **使用目录导航** - 根据文档分类快速定位
2. **查看快速导航** - 根据角色选择推荐阅读路径
3. **使用智能助手** - 在系统内询问相关问题

## 💡 使用建议

### 📖 阅读建议
- 建议按照推荐顺序阅读相关文档
- 实践操作时结合文档说明进行
- 遇到问题时优先查阅相关技术文档

### 🔄 反馈建议
- 发现文档错误或不清楚的地方，请及时反馈
- 建议新增文档内容，请通过智能助手提出
- 分享使用经验，帮助完善最佳实践指南

## 🎉 开始使用

准备好开始使用Smart Planning了吗？

- 🚀 [**立即开始**](./user_guide/quick_start.md) - 快速上手指南
- 🏗️ [**了解架构**](./architecture/system_architecture.md) - 深入了解系统
- 💬 [**获取帮助**](../frontend/pages/07_智能助手.py) - 使用智能助手

---

**Smart Planning - 智能生产管理系统**
*让生产管理更智能，让工厂运营更高效！*
