"""
数据库配置存储服务
支持将前端配置的数据库信息持久化存储
"""

import logging
import json
from typing import Dict, List, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.core.database import get_main_db
from app.core.dynamic_database import dynamic_db_manager, DatabaseConfig, DatabaseEngine

logger = logging.getLogger(__name__)


class DatabaseConfigStorageService:
    """数据库配置存储服务"""
    
    async def save_config_to_database(self, config: DatabaseConfig) -> bool:
        """将配置保存到主数据库"""
        try:
            async for session in get_main_db():
                # 检查配置是否已存在
                check_query = text("""
                    SELECT id FROM system_config 
                    WHERE config_key = :config_key
                """)
                
                result = await session.execute(
                    check_query, 
                    {"config_key": f"database_config_{config.name}"}
                )
                existing = result.fetchone()
                
                # 准备配置数据
                config_data = {
                    "name": config.name,
                    "engine_type": config.engine_type.value,
                    "host": config.host,
                    "port": config.port,
                    "username": config.username,
                    "password": config.password,  # 注意：生产环境应该加密存储
                    "database": config.database,
                    "description": config.description,
                    "connection_url": config.connection_url,
                    "extra_params": config.extra_params
                }
                
                if existing:
                    # 更新现有配置
                    update_query = text("""
                        UPDATE system_config 
                        SET config_value = :config_value,
                            description = :description,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE config_key = :config_key
                    """)
                    
                    await session.execute(update_query, {
                        "config_key": f"database_config_{config.name}",
                        "config_value": json.dumps(config_data),
                        "description": f"数据库配置: {config.description}"
                    })
                else:
                    # 插入新配置
                    insert_query = text("""
                        INSERT INTO system_config (config_key, config_value, config_type, description, is_public)
                        VALUES (:config_key, :config_value, 'json', :description, false)
                    """)
                    
                    await session.execute(insert_query, {
                        "config_key": f"database_config_{config.name}",
                        "config_value": json.dumps(config_data),
                        "description": f"数据库配置: {config.description}"
                    })
                
                await session.commit()
                logger.info(f"✅ 数据库配置 {config.name} 保存成功")
                return True
                
        except Exception as e:
            logger.error(f"❌ 保存数据库配置失败 {config.name}: {str(e)}")
            return False
    
    async def load_configs_from_database(self) -> List[DatabaseConfig]:
        """从主数据库加载配置"""
        try:
            configs = []
            
            async for session in get_main_db():
                query = text("""
                    SELECT config_key, config_value 
                    FROM system_config 
                    WHERE config_key LIKE 'database_config_%'
                """)
                
                result = await session.execute(query)
                rows = result.fetchall()
                
                for row in rows:
                    try:
                        config_data = json.loads(row.config_value)
                        
                        config = DatabaseConfig(
                            name=config_data["name"],
                            engine_type=DatabaseEngine(config_data["engine_type"]),
                            host=config_data["host"],
                            port=config_data["port"],
                            username=config_data["username"],
                            password=config_data["password"],
                            database=config_data["database"],
                            description=config_data.get("description", ""),
                            connection_url=config_data.get("connection_url"),
                            extra_params=config_data.get("extra_params", {})
                        )
                        
                        configs.append(config)
                        
                    except Exception as e:
                        logger.error(f"解析数据库配置失败 {row.config_key}: {str(e)}")
                        continue
            
            logger.info(f"✅ 从数据库加载了 {len(configs)} 个数据库配置")
            return configs
            
        except Exception as e:
            logger.error(f"❌ 从数据库加载配置失败: {str(e)}")
            return []
    
    async def delete_config_from_database(self, config_name: str) -> bool:
        """从主数据库删除配置"""
        try:
            async for session in get_main_db():
                delete_query = text("""
                    DELETE FROM system_config 
                    WHERE config_key = :config_key
                """)
                
                result = await session.execute(
                    delete_query, 
                    {"config_key": f"database_config_{config_name}"}
                )
                
                await session.commit()
                
                if result.rowcount > 0:
                    logger.info(f"✅ 数据库配置 {config_name} 删除成功")
                    return True
                else:
                    logger.warning(f"⚠️ 数据库配置 {config_name} 不存在")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 删除数据库配置失败 {config_name}: {str(e)}")
            return False
    
    async def initialize_from_stored_configs(self):
        """从存储的配置初始化动态数据库管理器"""
        try:
            stored_configs = await self.load_configs_from_database()
            
            for config in stored_configs:
                # 添加到动态数据库管理器
                success = dynamic_db_manager.add_database_config(config)
                if success:
                    logger.info(f"✅ 初始化数据库配置: {config.name}")
                else:
                    logger.error(f"❌ 初始化数据库配置失败: {config.name}")
            
            logger.info(f"✅ 数据库配置初始化完成，共加载 {len(stored_configs)} 个配置")
            
        except Exception as e:
            logger.error(f"❌ 数据库配置初始化失败: {str(e)}")


class DatabaseConfigTemplateService:
    """数据库配置模板服务"""
    
    def get_config_templates(self) -> Dict[str, Dict[str, Any]]:
        """获取数据库配置模板"""
        return {
            "mysql_local": {
                "name": "mysql_local",
                "engine_type": "mysql",
                "host": "localhost",
                "port": 3306,
                "username": "root",
                "password": "",
                "database": "test",
                "description": "本地MySQL数据库",
                "extra_params": {"charset": "utf8mb4"}
            },
            "oracle_local": {
                "name": "oracle_local",
                "engine_type": "oracle",
                "host": "localhost",
                "port": 1521,
                "username": "system",
                "password": "",
                "database": "ORCL",
                "description": "本地Oracle数据库",
                "extra_params": {"service_name": "ORCL"}
            },
            "postgresql_local": {
                "name": "postgresql_local",
                "engine_type": "postgresql",
                "host": "localhost",
                "port": 5432,
                "username": "postgres",
                "password": "",
                "database": "postgres",
                "description": "本地PostgreSQL数据库",
                "extra_params": {}
            },
            "sqlserver_local": {
                "name": "sqlserver_local",
                "engine_type": "sqlserver",
                "host": "localhost",
                "port": 1433,
                "username": "sa",
                "password": "",
                "database": "master",
                "description": "本地SQL Server数据库",
                "extra_params": {}
            },
            "inventory_mysql": {
                "name": "inventory_db",
                "engine_type": "mysql",
                "host": "*************",
                "port": 3306,
                "username": "inventory_user",
                "password": "",
                "database": "inventory_system",
                "description": "库存管理系统数据库",
                "extra_params": {"charset": "utf8mb4"}
            },
            "pci_oracle": {
                "name": "pci_db",
                "engine_type": "oracle",
                "host": "*************",
                "port": 1521,
                "username": "pci_user",
                "password": "",
                "database": "PCIDB",
                "description": "PCI数据管理系统",
                "extra_params": {"service_name": "PCIDB"}
            }
        }
    
    def apply_template(self, template_name: str, custom_values: Dict[str, Any] = None) -> Dict[str, Any]:
        """应用配置模板"""
        templates = self.get_config_templates()
        
        if template_name not in templates:
            raise ValueError(f"模板 {template_name} 不存在")
        
        template = templates[template_name].copy()
        
        if custom_values:
            template.update(custom_values)
        
        return template


class DatabaseConfigValidationService:
    """数据库配置验证服务"""
    
    def validate_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证数据库配置"""
        errors = []
        warnings = []
        
        # 必填字段检查
        required_fields = ["name", "engine_type", "host", "username", "password", "database"]
        for field in required_fields:
            if not config_data.get(field):
                errors.append(f"字段 {field} 是必填的")
        
        # 端口号检查
        port = config_data.get("port")
        if port and not (1 <= port <= 65535):
            errors.append("端口号必须在1-65535之间")
        
        # 数据库名称检查
        name = config_data.get("name", "")
        if name:
            if not name.replace("_", "").replace("-", "").isalnum():
                errors.append("数据库名称只能包含字母、数字、下划线和连字符")
            
            if name in ["main", "system", "admin"]:
                warnings.append(f"数据库名称 {name} 可能与系统保留名称冲突")
        
        # 引擎类型检查
        engine_type = config_data.get("engine_type")
        if engine_type:
            try:
                DatabaseEngine(engine_type)
            except ValueError:
                errors.append(f"不支持的数据库引擎类型: {engine_type}")
        
        # 连接字符串检查
        connection_url = config_data.get("connection_url")
        if connection_url:
            if not any(connection_url.startswith(prefix) for prefix in ["mysql://", "oracle://", "postgresql://", "mssql://", "sqlite://"]):
                warnings.append("连接字符串格式可能不正确")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }


# 全局服务实例
database_config_storage_service = DatabaseConfigStorageService()
database_config_template_service = DatabaseConfigTemplateService()
database_config_validation_service = DatabaseConfigValidationService()
