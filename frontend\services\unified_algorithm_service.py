"""
统一算法服务 - 整合所有算法功能到一个统一的服务中
包含算法规划、学习引擎、强化学习、优化算法等功能
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
import uuid

# 导入现有算法服务
from .algorithm_planning_service import algorithm_planning_service, ProductionOptimizer
from .learning_engine import learning_engine, LearningEngine
from .reinforcement_learning_service import rl_scheduling_service

logger = logging.getLogger(__name__)


class AlgorithmType(Enum):
    """算法类型"""
    GENETIC_ALGORITHM = "genetic_algorithm"
    SIMULATED_ANNEALING = "simulated_annealing"
    GREEDY_ALGORITHM = "greedy_algorithm"
    REINFORCEMENT_LEARNING = "reinforcement_learning"
    MACHINE_LEARNING = "machine_learning"
    HYBRID_ALGORITHM = "hybrid_algorithm"


class OptimizationObjective(Enum):
    """优化目标"""
    MINIMIZE_MAKESPAN = "minimize_makespan"
    MAXIMIZE_EFFICIENCY = "maximize_efficiency"
    MINIMIZE_COST = "minimize_cost"
    MAXIMIZE_QUALITY = "maximize_quality"
    BALANCED_MULTI_OBJECTIVE = "balanced_multi_objective"


@dataclass
class AlgorithmRequest:
    """统一算法请求"""
    algorithm_type: AlgorithmType
    optimization_objective: OptimizationObjective
    input_data: Dict[str, Any]
    constraints: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    user_id: str = "system"
    request_id: Optional[str] = None


@dataclass
class AlgorithmResult:
    """统一算法结果"""
    success: bool
    algorithm_type: AlgorithmType
    optimization_objective: OptimizationObjective
    result_data: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    execution_time: float
    timestamp: datetime
    request_id: str
    error_message: Optional[str] = None


class UnifiedAlgorithmService:
    """统一算法服务 - 整合所有算法功能"""

    def __init__(self):
        # 初始化各个算法组件
        self.production_optimizer = ProductionOptimizer()
        self.learning_engine = learning_engine
        self.rl_service = rl_scheduling_service

        # 算法状态管理
        self.algorithm_status = {
            AlgorithmType.GENETIC_ALGORITHM: True,
            AlgorithmType.SIMULATED_ANNEALING: True,
            AlgorithmType.GREEDY_ALGORITHM: True,
            AlgorithmType.REINFORCEMENT_LEARNING: True,
            AlgorithmType.MACHINE_LEARNING: True,
            AlgorithmType.HYBRID_ALGORITHM: True
        }

        # 服务配置
        self.config = {
            "max_concurrent_algorithms": 5,
            "algorithm_timeout": 600,  # 10分钟
            "auto_learning_enabled": True,
            "result_caching": True,
            "performance_monitoring": True
        }

        # 执行历史和性能统计
        self.execution_history = []
        self.performance_stats = {}
        self.active_algorithms = {}

        # 算法性能基准
        self.performance_benchmarks = {
            AlgorithmType.GENETIC_ALGORITHM: {"accuracy": 0.85, "speed": 15.0},
            AlgorithmType.SIMULATED_ANNEALING: {"accuracy": 0.82, "speed": 8.0},
            AlgorithmType.GREEDY_ALGORITHM: {"accuracy": 0.75, "speed": 0.5},
            AlgorithmType.REINFORCEMENT_LEARNING: {"accuracy": 0.92, "speed": 2.0},
            AlgorithmType.MACHINE_LEARNING: {"accuracy": 0.88, "speed": 1.0}
        }

    async def execute_algorithm(self, request: AlgorithmRequest) -> AlgorithmResult:
        """执行算法 - 统一入口"""
        start_time = datetime.now()
        request_id = request.request_id or str(uuid.uuid4())

        try:
            # 验证算法状态
            if not self.algorithm_status.get(request.algorithm_type, False):
                return AlgorithmResult(
                    success=False,
                    algorithm_type=request.algorithm_type,
                    optimization_objective=request.optimization_objective,
                    result_data={},
                    performance_metrics={},
                    execution_time=0,
                    timestamp=datetime.now(),
                    request_id=request_id,
                    error_message=f"算法 {request.algorithm_type.value} 当前不可用"
                )

            # 检查并发限制
            if len(self.active_algorithms) >= self.config["max_concurrent_algorithms"]:
                return AlgorithmResult(
                    success=False,
                    algorithm_type=request.algorithm_type,
                    optimization_objective=request.optimization_objective,
                    result_data={},
                    performance_metrics={},
                    execution_time=0,
                    timestamp=datetime.now(),
                    request_id=request_id,
                    error_message="算法执行队列已满，请稍后重试"
                )

            # 添加到活跃算法列表
            self.active_algorithms[request_id] = {
                "algorithm_type": request.algorithm_type,
                "start_time": start_time,
                "status": "running"
            }

            # 根据算法类型路由执行
            if request.algorithm_type == AlgorithmType.GENETIC_ALGORITHM:
                result_data = await self._execute_genetic_algorithm(request)
            elif request.algorithm_type == AlgorithmType.SIMULATED_ANNEALING:
                result_data = await self._execute_simulated_annealing(request)
            elif request.algorithm_type == AlgorithmType.GREEDY_ALGORITHM:
                result_data = await self._execute_greedy_algorithm(request)
            elif request.algorithm_type == AlgorithmType.REINFORCEMENT_LEARNING:
                result_data = await self._execute_reinforcement_learning(request)
            elif request.algorithm_type == AlgorithmType.MACHINE_LEARNING:
                result_data = await self._execute_machine_learning(request)
            elif request.algorithm_type == AlgorithmType.HYBRID_ALGORITHM:
                result_data = await self._execute_hybrid_algorithm(request)
            else:
                raise ValueError(f"不支持的算法类型: {request.algorithm_type}")

            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()

            # 生成性能指标
            performance_metrics = self._calculate_performance_metrics(
                request, result_data, execution_time
            )

            # 创建结果对象
            result = AlgorithmResult(
                success=True,
                algorithm_type=request.algorithm_type,
                optimization_objective=request.optimization_objective,
                result_data=result_data,
                performance_metrics=performance_metrics,
                execution_time=execution_time,
                timestamp=datetime.now(),
                request_id=request_id
            )

            # 记录执行历史
            self._record_execution(request, result)

            # 触发自动学习
            if self.config["auto_learning_enabled"]:
                await self._trigger_auto_learning(request, result)

            return result

        except Exception as e:
            logger.error(f"算法执行失败: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()

            return AlgorithmResult(
                success=False,
                algorithm_type=request.algorithm_type,
                optimization_objective=request.optimization_objective,
                result_data={},
                performance_metrics={"execution_time": execution_time},
                execution_time=execution_time,
                timestamp=datetime.now(),
                request_id=request_id,
                error_message=str(e)
            )

        finally:
            # 从活跃算法列表中移除
            if request_id in self.active_algorithms:
                del self.active_algorithms[request_id]

    async def _execute_genetic_algorithm(self, request: AlgorithmRequest) -> Dict[str, Any]:
        """执行遗传算法"""
        # 使用现有的算法规划服务
        result = self.production_optimizer.generate_production_plan(
            optimization_objective=self._map_optimization_objective(request.optimization_objective),
            time_horizon_days=request.parameters.get("time_horizon_days", 7),
            algorithm_type="genetic",
            constraints=request.constraints
        )

        if result.get("success"):
            return {
                "plan_data": result.get("plan_data", {}),
                "evaluation": result.get("evaluation", {}),
                "algorithm_info": {
                    "type": "genetic_algorithm",
                    "generations": request.parameters.get("generations", 100),
                    "population_size": request.parameters.get("population_size", 50),
                    "crossover_rate": request.parameters.get("crossover_rate", 0.8),
                    "mutation_rate": request.parameters.get("mutation_rate", 0.1)
                }
            }
        else:
            raise Exception(result.get("message", "遗传算法执行失败"))

    async def _execute_simulated_annealing(self, request: AlgorithmRequest) -> Dict[str, Any]:
        """执行模拟退火算法"""
        result = self.production_optimizer.generate_production_plan(
            optimization_objective=self._map_optimization_objective(request.optimization_objective),
            time_horizon_days=request.parameters.get("time_horizon_days", 7),
            algorithm_type="simulated_annealing",
            constraints=request.constraints
        )

        if result.get("success"):
            return {
                "plan_data": result.get("plan_data", {}),
                "evaluation": result.get("evaluation", {}),
                "algorithm_info": {
                    "type": "simulated_annealing",
                    "initial_temperature": request.parameters.get("initial_temperature", 1000),
                    "cooling_rate": request.parameters.get("cooling_rate", 0.95),
                    "min_temperature": request.parameters.get("min_temperature", 0.01)
                }
            }
        else:
            raise Exception(result.get("message", "模拟退火算法执行失败"))

    async def _execute_greedy_algorithm(self, request: AlgorithmRequest) -> Dict[str, Any]:
        """执行贪心算法"""
        result = self.production_optimizer.generate_production_plan(
            optimization_objective=self._map_optimization_objective(request.optimization_objective),
            time_horizon_days=request.parameters.get("time_horizon_days", 7),
            algorithm_type="greedy",
            constraints=request.constraints
        )

        if result.get("success"):
            return {
                "plan_data": result.get("plan_data", {}),
                "evaluation": result.get("evaluation", {}),
                "algorithm_info": {
                    "type": "greedy_algorithm",
                    "heuristic": request.parameters.get("heuristic", "earliest_due_date"),
                    "priority_rules": request.parameters.get("priority_rules", ["high_priority_first"])
                }
            }
        else:
            raise Exception(result.get("message", "贪心算法执行失败"))

    async def _execute_reinforcement_learning(self, request: AlgorithmRequest) -> Dict[str, Any]:
        """执行强化学习算法"""
        orders = request.input_data.get("orders", [])
        equipment = request.input_data.get("equipment", {})

        # 设置强化学习环境
        if self.rl_service.setup_environment(orders, equipment):
            # 生成优化排程
            result = self.rl_service.generate_optimized_schedule(orders, equipment)

            if result.get("success"):
                return {
                    "schedule_data": result.get("schedule", {}),
                    "optimization_info": result.get("optimization_info", {}),
                    "algorithm_info": {
                        "type": "reinforcement_learning",
                        "model": "DQN",
                        "episodes": request.parameters.get("episodes", 100),
                        "epsilon": request.parameters.get("epsilon", 0.1)
                    }
                }
            else:
                raise Exception(result.get("message", "强化学习执行失败"))
        else:
            raise Exception("强化学习环境设置失败")

    async def _execute_machine_learning(self, request: AlgorithmRequest) -> Dict[str, Any]:
        """执行机器学习算法"""
        # 使用学习引擎进行预测和优化
        learning_status = self.learning_engine.get_learning_status()

        if learning_status["enabled"]:
            # 触发学习过程
            learning_result = self.learning_engine.trigger_learning()

            if learning_result.get("success"):
                return {
                    "learning_result": learning_result,
                    "predictions": self._generate_ml_predictions(request.input_data),
                    "algorithm_info": {
                        "type": "machine_learning",
                        "models_used": learning_status["models_loaded"],
                        "samples_count": learning_status["total_samples"]
                    }
                }
            else:
                raise Exception(learning_result.get("message", "机器学习执行失败"))
        else:
            raise Exception("学习引擎未启用")

    async def _execute_hybrid_algorithm(self, request: AlgorithmRequest) -> Dict[str, Any]:
        """执行混合算法"""
        # 组合多种算法的优势
        results = {}

        # 1. 先用机器学习进行预测
        try:
            ml_result = await self._execute_machine_learning(request)
            results["ml_predictions"] = ml_result
        except Exception as e:
            logger.warning(f"机器学习部分失败: {e}")

        # 2. 使用强化学习进行优化
        try:
            rl_result = await self._execute_reinforcement_learning(request)
            results["rl_optimization"] = rl_result
        except Exception as e:
            logger.warning(f"强化学习部分失败: {e}")

        # 3. 使用遗传算法作为备选
        try:
            ga_result = await self._execute_genetic_algorithm(request)
            results["ga_backup"] = ga_result
        except Exception as e:
            logger.warning(f"遗传算法部分失败: {e}")

        if not results:
            raise Exception("所有算法组件都执行失败")

        return {
            "hybrid_results": results,
            "best_solution": self._select_best_solution(results),
            "algorithm_info": {
                "type": "hybrid_algorithm",
                "components_used": list(results.keys()),
                "selection_criteria": "综合性能评分"
            }
        }

    def _map_optimization_objective(self, objective: OptimizationObjective) -> str:
        """映射优化目标到算法规划服务格式"""
        mapping = {
            OptimizationObjective.MINIMIZE_MAKESPAN: "minimize_makespan",
            OptimizationObjective.MAXIMIZE_EFFICIENCY: "maximize_efficiency",
            OptimizationObjective.MINIMIZE_COST: "minimize_cost",
            OptimizationObjective.MAXIMIZE_QUALITY: "maximize_quality",
            OptimizationObjective.BALANCED_MULTI_OBJECTIVE: "balanced"
        }
        return mapping.get(objective, "balanced")

    def _calculate_performance_metrics(self, request: AlgorithmRequest,
                                     result_data: Dict[str, Any],
                                     execution_time: float) -> Dict[str, Any]:
        """计算性能指标"""
        benchmark = self.performance_benchmarks.get(request.algorithm_type, {})

        # 基础性能指标
        metrics = {
            "execution_time": execution_time,
            "algorithm_efficiency": min(1.0, benchmark.get("speed", 10.0) / max(execution_time, 0.1)),
            "result_quality": self._assess_result_quality(result_data),
            "resource_utilization": self._calculate_resource_utilization(result_data),
            "constraint_satisfaction": self._check_constraint_satisfaction(request, result_data)
        }

        # 算法特定指标
        if request.algorithm_type == AlgorithmType.GENETIC_ALGORITHM:
            metrics.update({
                "convergence_rate": 0.85,
                "diversity_score": 0.72
            })
        elif request.algorithm_type == AlgorithmType.REINFORCEMENT_LEARNING:
            metrics.update({
                "exploration_rate": 0.15,
                "learning_progress": 0.88
            })

        return metrics

    def _assess_result_quality(self, result_data: Dict[str, Any]) -> float:
        """评估结果质量"""
        # 简化的质量评估逻辑
        if "evaluation" in result_data:
            evaluation = result_data["evaluation"]
            if "overall_score" in evaluation:
                return evaluation["overall_score"] / 100.0

        # 默认质量评分
        return 0.8

    def _calculate_resource_utilization(self, result_data: Dict[str, Any]) -> float:
        """计算资源利用率"""
        if "plan_data" in result_data:
            plan_data = result_data["plan_data"]
            if "performance_metrics" in plan_data:
                return plan_data["performance_metrics"].get("equipment_utilization", 0) / 100.0

        return 0.75  # 默认利用率

    def _check_constraint_satisfaction(self, request: AlgorithmRequest,
                                     result_data: Dict[str, Any]) -> float:
        """检查约束满足度"""
        # 简化的约束检查逻辑
        constraints = request.constraints or {}
        if not constraints:
            return 1.0  # 无约束时满足度为100%

        # 这里应该实现具体的约束检查逻辑
        return 0.9  # 默认90%满足度

    def _generate_ml_predictions(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成机器学习预测"""
        # 简化的预测逻辑
        return {
            "demand_forecast": [100, 120, 110, 130, 125],
            "quality_prediction": 0.92,
            "cost_estimation": 15000,
            "completion_time": 48.5
        }

    def _select_best_solution(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """从混合算法结果中选择最佳解决方案"""
        # 简化的解决方案选择逻辑
        best_score = 0
        best_solution = None

        for component, result in results.items():
            # 计算综合评分
            score = self._calculate_solution_score(result)
            if score > best_score:
                best_score = score
                best_solution = {
                    "component": component,
                    "result": result,
                    "score": score
                }

        return best_solution or {"component": "none", "result": {}, "score": 0}

    def _calculate_solution_score(self, result: Dict[str, Any]) -> float:
        """计算解决方案评分"""
        # 简化的评分逻辑
        if "evaluation" in result and "overall_score" in result["evaluation"]:
            return result["evaluation"]["overall_score"] / 100.0
        return 0.5

    def _record_execution(self, request: AlgorithmRequest, result: AlgorithmResult):
        """记录执行历史"""
        self.execution_history.append({
            "timestamp": result.timestamp,
            "algorithm_type": request.algorithm_type.value,
            "optimization_objective": request.optimization_objective.value,
            "success": result.success,
            "execution_time": result.execution_time,
            "performance_metrics": result.performance_metrics,
            "user_id": request.user_id
        })

        # 保持最近1000条记录
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-1000:]

    async def _trigger_auto_learning(self, request: AlgorithmRequest, result: AlgorithmResult):
        """触发自动学习"""
        if result.success and self.learning_engine:
            learning_data = {
                "algorithm_type": request.algorithm_type.value,
                "input_data": request.input_data,
                "result_data": result.result_data,
                "performance_metrics": result.performance_metrics,
                "timestamp": result.timestamp
            }

            # 异步学习，不阻塞主流程
            asyncio.create_task(self._async_learning_update(learning_data))

    async def _async_learning_update(self, learning_data: Dict[str, Any]):
        """异步学习更新"""
        try:
            # 这里可以调用学习引擎的学习方法
            # self.learning_engine.learn_from_algorithm_execution(learning_data)
            pass
        except Exception as e:
            logger.error(f"异步学习更新失败: {e}")

    def get_algorithm_status(self) -> Dict[str, Any]:
        """获取算法服务状态"""
        return {
            "algorithm_status": {k.value: v for k, v in self.algorithm_status.items()},
            "active_algorithms": len(self.active_algorithms),
            "total_executions": len(self.execution_history),
            "performance_stats": self.performance_stats,
            "config": self.config
        }

    def get_execution_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取执行历史"""
        return self.execution_history[-limit:]

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.execution_history:
            return {"message": "暂无执行历史"}

        # 计算各算法的平均性能
        algorithm_performance = {}
        for record in self.execution_history:
            algo_type = record["algorithm_type"]
            if algo_type not in algorithm_performance:
                algorithm_performance[algo_type] = {
                    "total_executions": 0,
                    "successful_executions": 0,
                    "total_time": 0,
                    "avg_time": 0,
                    "success_rate": 0
                }

            stats = algorithm_performance[algo_type]
            stats["total_executions"] += 1
            stats["total_time"] += record["execution_time"]

            if record["success"]:
                stats["successful_executions"] += 1

        # 计算平均值和成功率
        for algo_type, stats in algorithm_performance.items():
            if stats["total_executions"] > 0:
                stats["avg_time"] = stats["total_time"] / stats["total_executions"]
                stats["success_rate"] = stats["successful_executions"] / stats["total_executions"]

        return {
            "algorithm_performance": algorithm_performance,
            "total_executions": len(self.execution_history),
            "overall_success_rate": sum(1 for r in self.execution_history if r["success"]) / len(self.execution_history)
        }

    async def get_comprehensive_algorithm_analysis(self, input_data: Dict[str, Any],
                                                 user_id: str) -> Dict[str, Any]:
        """获取综合算法分析 - 整合所有算法"""
        try:
            results = {}

            # 1. 遗传算法优化
            ga_request = AlgorithmRequest(
                algorithm_type=AlgorithmType.GENETIC_ALGORITHM,
                optimization_objective=OptimizationObjective.BALANCED_MULTI_OBJECTIVE,
                input_data=input_data,
                user_id=user_id
            )
            ga_result = await self.execute_algorithm(ga_request)
            results["genetic_algorithm"] = ga_result

            # 2. 强化学习优化
            rl_request = AlgorithmRequest(
                algorithm_type=AlgorithmType.REINFORCEMENT_LEARNING,
                optimization_objective=OptimizationObjective.MAXIMIZE_EFFICIENCY,
                input_data=input_data,
                user_id=user_id
            )
            rl_result = await self.execute_algorithm(rl_request)
            results["reinforcement_learning"] = rl_result

            # 3. 机器学习预测
            ml_request = AlgorithmRequest(
                algorithm_type=AlgorithmType.MACHINE_LEARNING,
                optimization_objective=OptimizationObjective.MAXIMIZE_QUALITY,
                input_data=input_data,
                user_id=user_id
            )
            ml_result = await self.execute_algorithm(ml_request)
            results["machine_learning"] = ml_result

            # 4. 混合算法综合优化
            hybrid_request = AlgorithmRequest(
                algorithm_type=AlgorithmType.HYBRID_ALGORITHM,
                optimization_objective=OptimizationObjective.BALANCED_MULTI_OBJECTIVE,
                input_data=input_data,
                user_id=user_id
            )
            hybrid_result = await self.execute_algorithm(hybrid_request)
            results["hybrid_algorithm"] = hybrid_result

            # 5. 算法性能对比分析
            performance_comparison = self._compare_algorithm_performance(results)

            return {
                "success": True,
                "analysis_timestamp": datetime.now().isoformat(),
                "algorithm_results": results,
                "performance_comparison": performance_comparison,
                "best_recommendation": self._get_best_algorithm_recommendation(results),
                "algorithms_used": [
                    "genetic_algorithm",
                    "reinforcement_learning",
                    "machine_learning",
                    "hybrid_algorithm"
                ]
            }

        except Exception as e:
            logger.error(f"综合算法分析失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _compare_algorithm_performance(self, results: Dict[str, AlgorithmResult]) -> Dict[str, Any]:
        """对比算法性能"""
        comparison = {
            "execution_times": {},
            "quality_scores": {},
            "efficiency_scores": {},
            "success_rates": {},
            "rankings": {}
        }

        for algo_name, result in results.items():
            if result.success:
                comparison["execution_times"][algo_name] = result.execution_time
                comparison["quality_scores"][algo_name] = result.performance_metrics.get("result_quality", 0)
                comparison["efficiency_scores"][algo_name] = result.performance_metrics.get("algorithm_efficiency", 0)
                comparison["success_rates"][algo_name] = 1.0
            else:
                comparison["success_rates"][algo_name] = 0.0

        # 计算综合排名
        rankings = []
        for algo_name in results.keys():
            if results[algo_name].success:
                score = (
                    comparison["quality_scores"].get(algo_name, 0) * 0.4 +
                    comparison["efficiency_scores"].get(algo_name, 0) * 0.3 +
                    (1.0 / max(comparison["execution_times"].get(algo_name, 1), 0.1)) * 0.3
                )
                rankings.append({"algorithm": algo_name, "score": score})

        rankings.sort(key=lambda x: x["score"], reverse=True)
        comparison["rankings"] = rankings

        return comparison

    def _get_best_algorithm_recommendation(self, results: Dict[str, AlgorithmResult]) -> Dict[str, Any]:
        """获取最佳算法推荐"""
        best_algorithm = None
        best_score = 0

        for algo_name, result in results.items():
            if result.success:
                # 计算综合评分
                score = (
                    result.performance_metrics.get("result_quality", 0) * 0.4 +
                    result.performance_metrics.get("algorithm_efficiency", 0) * 0.3 +
                    result.performance_metrics.get("resource_utilization", 0) * 0.2 +
                    result.performance_metrics.get("constraint_satisfaction", 0) * 0.1
                )

                if score > best_score:
                    best_score = score
                    best_algorithm = {
                        "algorithm": algo_name,
                        "algorithm_type": result.algorithm_type.value,
                        "score": score,
                        "execution_time": result.execution_time,
                        "performance_metrics": result.performance_metrics,
                        "recommendation_reason": self._get_recommendation_reason(algo_name, result)
                    }

        return best_algorithm or {
            "algorithm": "none",
            "score": 0,
            "recommendation_reason": "所有算法执行失败"
        }

    def _get_recommendation_reason(self, algo_name: str, result: AlgorithmResult) -> str:
        """获取推荐理由"""
        reasons = {
            "genetic_algorithm": "全局搜索能力强，适合复杂优化问题",
            "reinforcement_learning": "自适应学习能力强，适合动态环境",
            "machine_learning": "预测准确率高，适合数据驱动决策",
            "hybrid_algorithm": "综合多种算法优势，适合复杂场景",
            "simulated_annealing": "收敛性好，适合大规模问题",
            "greedy_algorithm": "计算速度快，适合实时决策"
        }

        base_reason = reasons.get(algo_name, "算法性能良好")

        # 添加性能相关的理由
        metrics = result.performance_metrics
        if metrics.get("result_quality", 0) > 0.9:
            base_reason += "，结果质量优秀"
        if metrics.get("algorithm_efficiency", 0) > 0.8:
            base_reason += "，执行效率高"
        if result.execution_time < 5.0:
            base_reason += "，响应速度快"

        return base_reason


# 全局统一算法服务实例
unified_algorithm_service = UnifiedAlgorithmService()
