"""
用户管理API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.logging_config import business_logger
from app.schemas.user import (
    UserResponse, UserCreate, UserUpdate, UserListResponse, 
    UserProfileUpdate, UserStatsResponse
)
from app.services.user_service import UserService
from app.api.dependencies import (
    get_current_user, get_current_superuser, require_permission,
    get_pagination_params, PaginationParams
)

router = APIRouter()


@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户信息"""
    try:
        user_service = UserService(db)
        user = await user_service.get_by_id(current_user["id"])
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )


@router.put("/me", response_model=UserResponse, summary="更新当前用户资料")
async def update_current_user_profile(
    user_data: UserProfileUpdate,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新当前用户资料"""
    try:
        user_service = UserService(db)
        
        # 转换为UserUpdate格式
        update_data = UserUpdate(**user_data.dict(exclude_unset=True))
        
        user = await user_service.update_user(current_user["id"], update_data)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="profile_update",
            details=user_data.dict(exclude_unset=True)
        )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="profile_update_error",
            error_message=str(e),
            details={"user_id": current_user["id"]}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户资料失败"
        )


@router.get("", response_model=UserListResponse, summary="获取用户列表")
async def get_users(
    pagination: PaginationParams = Depends(get_pagination_params),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    current_user: dict = Depends(require_permission("user.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取用户列表"""
    try:
        user_service = UserService(db)
        users, total = await user_service.get_users(
            page=pagination.page,
            page_size=pagination.page_size,
            search=search,
            is_active=is_active
        )
        
        total_pages = (total + pagination.page_size - 1) // pagination.page_size
        
        return {
            "users": users,
            "total": total,
            "page": pagination.page,
            "page_size": pagination.page_size,
            "total_pages": total_pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )


@router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    user_id: str,
    current_user: dict = Depends(require_permission("user.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取用户详情"""
    try:
        user_service = UserService(db)
        user = await user_service.get_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户详情失败"
        )


@router.post("", response_model=UserResponse, summary="创建用户")
async def create_user(
    user_data: UserCreate,
    current_user: dict = Depends(require_permission("user.create")),
    db: AsyncSession = Depends(get_db)
):
    """创建用户"""
    try:
        user_service = UserService(db)
        
        # 检查用户名是否已存在
        existing_user = await user_service.get_by_username(user_data.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 检查邮箱是否已存在
        existing_email = await user_service.get_by_email(user_data.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
        
        # 创建用户（这里需要实现UserCreate到UserRegister的转换）
        from app.schemas.auth import UserRegister
        register_data = UserRegister(
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            password=user_data.password,
            confirm_password=user_data.password,  # 管理员创建时直接确认
            phone=user_data.phone,
            department=user_data.department,
            position=user_data.position
        )
        
        user = await user_service.create_user(register_data)
        
        # 分配角色
        if user_data.role_ids:
            update_data = UserUpdate(role_ids=user_data.role_ids)
            user = await user_service.update_user(user.id, update_data)
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="user_create",
            details={
                "created_user_id": user.id,
                "username": user.username,
                "email": user.email
            }
        )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="user_create_error",
            error_message=str(e),
            details={"username": user_data.username}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建用户失败"
        )


@router.put("/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    user_id: str,
    user_data: UserUpdate,
    current_user: dict = Depends(require_permission("user.update")),
    db: AsyncSession = Depends(get_db)
):
    """更新用户"""
    try:
        user_service = UserService(db)
        
        # 检查用户是否存在
        existing_user = await user_service.get_by_id(user_id)
        if not existing_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查邮箱是否被其他用户使用
        if user_data.email:
            email_user = await user_service.get_by_email(user_data.email)
            if email_user and email_user.id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已被其他用户使用"
                )
        
        user = await user_service.update_user(user_id, user_data)
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="user_update",
            details={
                "updated_user_id": user_id,
                "changes": user_data.dict(exclude_unset=True)
            }
        )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="user_update_error",
            error_message=str(e),
            details={"user_id": user_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户失败"
        )


@router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: str,
    current_user: dict = Depends(require_permission("user.delete")),
    db: AsyncSession = Depends(get_db)
):
    """删除用户"""
    try:
        # 不能删除自己
        if user_id == current_user["id"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除自己的账户"
            )
        
        user_service = UserService(db)
        
        # 检查用户是否存在
        existing_user = await user_service.get_by_id(user_id)
        if not existing_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        success = await user_service.delete_user(user_id)
        
        if success:
            business_logger.log_user_action(
                user_id=current_user["id"],
                action="user_delete",
                details={
                    "deleted_user_id": user_id,
                    "username": existing_user.username
                }
            )
            
            return {"success": True, "message": "用户删除成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="用户删除失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="user_delete_error",
            error_message=str(e),
            details={"user_id": user_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )


@router.get("/stats/overview", response_model=UserStatsResponse, summary="获取用户统计")
async def get_user_stats(
    current_user: dict = Depends(require_permission("user.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取用户统计信息"""
    try:
        user_service = UserService(db)
        
        # 这里需要实现统计逻辑
        # 暂时返回模拟数据
        stats = {
            "total_users": 0,
            "active_users": 0,
            "inactive_users": 0,
            "new_users_today": 0,
            "new_users_this_week": 0,
            "new_users_this_month": 0,
            "login_stats": {}
        }
        
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户统计失败"
        )
