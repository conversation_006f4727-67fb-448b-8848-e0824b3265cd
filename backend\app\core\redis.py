"""
Redis配置和连接管理
"""

import redis.asyncio as redis
import json
import logging
from typing import Any, Optional, Union
from datetime import timedelta

from app.core.config import settings

logger = logging.getLogger(__name__)


class RedisManager:
    """Redis管理器"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self._connected = False
    
    async def connect(self):
        """连接Redis"""
        try:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 测试连接
            await self.redis_client.ping()
            self._connected = True
            logger.info("✅ Redis连接成功")
            
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {str(e)}")
            self._connected = False
            raise
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self._connected = False
            logger.info("✅ Redis连接已关闭")
    
    async def health_check(self) -> bool:
        """Redis健康检查"""
        try:
            if not self._connected or not self.redis_client:
                return False
            
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis健康检查失败: {str(e)}")
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            if not self._connected:
                return None
            
            value = await self.redis_client.get(key)
            if value is None:
                return None
            
            # 尝试解析JSON
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value
                
        except Exception as e:
            logger.error(f"Redis获取数据失败 {key}: {str(e)}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """设置缓存值"""
        try:
            if not self._connected:
                return False
            
            # 序列化值
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value, ensure_ascii=False)
            else:
                serialized_value = str(value)
            
            # 设置TTL
            if ttl is None:
                ttl = settings.CACHE_TTL
            
            await self.redis_client.set(key, serialized_value, ex=ttl)
            return True
            
        except Exception as e:
            logger.error(f"Redis设置数据失败 {key}: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            if not self._connected:
                return False
            
            result = await self.redis_client.delete(key)
            return result > 0
            
        except Exception as e:
            logger.error(f"Redis删除数据失败 {key}: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            if not self._connected:
                return False
            
            result = await self.redis_client.exists(key)
            return result > 0
            
        except Exception as e:
            logger.error(f"Redis检查键存在失败 {key}: {str(e)}")
            return False
    
    async def expire(self, key: str, ttl: Union[int, timedelta]) -> bool:
        """设置键过期时间"""
        try:
            if not self._connected:
                return False
            
            result = await self.redis_client.expire(key, ttl)
            return result
            
        except Exception as e:
            logger.error(f"Redis设置过期时间失败 {key}: {str(e)}")
            return False
    
    async def incr(self, key: str, amount: int = 1) -> Optional[int]:
        """递增计数器"""
        try:
            if not self._connected:
                return None
            
            result = await self.redis_client.incr(key, amount)
            return result
            
        except Exception as e:
            logger.error(f"Redis递增失败 {key}: {str(e)}")
            return None
    
    async def get_info(self) -> dict:
        """获取Redis信息"""
        try:
            if not self._connected:
                return {}
            
            info = await self.redis_client.info()
            return {
                "redis_version": info.get("redis_version"),
                "used_memory": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "total_commands_processed": info.get("total_commands_processed"),
                "keyspace_hits": info.get("keyspace_hits"),
                "keyspace_misses": info.get("keyspace_misses")
            }
            
        except Exception as e:
            logger.error(f"获取Redis信息失败: {str(e)}")
            return {}
    
    async def clear_cache(self, pattern: str = "*") -> int:
        """清理缓存"""
        try:
            if not self._connected:
                return 0
            
            keys = await self.redis_client.keys(pattern)
            if keys:
                result = await self.redis_client.delete(*keys)
                logger.info(f"清理了 {result} 个缓存键")
                return result
            return 0
            
        except Exception as e:
            logger.error(f"清理缓存失败: {str(e)}")
            return 0


# 全局Redis管理器实例
redis_manager = RedisManager()


async def get_redis() -> RedisManager:
    """获取Redis管理器实例"""
    if not redis_manager._connected:
        await redis_manager.connect()
    return redis_manager


# 缓存装饰器
def cache_result(key_prefix: str, ttl: Optional[int] = None):
    """缓存结果装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = await redis_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            await redis_manager.set(cache_key, result, ttl or settings.CACHE_TTL)
            
            return result
        return wrapper
    return decorator
