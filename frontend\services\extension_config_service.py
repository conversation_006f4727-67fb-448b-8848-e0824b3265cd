"""
扩展配置服务
管理规划引擎扩展的配置参数和持久化
"""

import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional


class ExtensionConfigService:
    """扩展配置服务"""

    def __init__(self):
        self.config_db_path = "extension_configs.db"
        self._init_config_database()

    def _init_config_database(self):
        """初始化配置数据库"""
        conn = sqlite3.connect(self.config_db_path)
        cursor = conn.cursor()

        # 扩展配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS extension_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                extension_type TEXT NOT NULL,
                config_name TEXT NOT NULL,
                config_data TEXT NOT NULL,
                version TEXT DEFAULT '1.0.0',
                created_by TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                UNIQUE(extension_type, config_name)
            )
        ''')

        # 配置历史表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS config_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_id INTEGER,
                old_config_data TEXT,
                new_config_data TEXT,
                change_reason TEXT,
                changed_by TEXT,
                changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (config_id) REFERENCES extension_configs (id)
            )
        ''')

        # 配置模板表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS config_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                extension_type TEXT NOT NULL,
                template_name TEXT NOT NULL,
                template_data TEXT NOT NULL,
                description TEXT,
                is_default BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(extension_type, template_name)
            )
        ''')

        conn.commit()

        # 插入默认配置模板
        self._insert_default_templates(cursor)
        conn.commit()
        conn.close()

    def _insert_default_templates(self, cursor):
        """插入默认配置模板"""

        # 物流配置模板
        logistics_template = {
            "transport": {
                "road_weight": 0.6,
                "rail_weight": 0.3,
                "air_weight": 0.1,
                "max_cost": 200,
                "max_lead_time": 24,
                "min_capacity": 50
            },
            "warehouse": {
                "safety_stock_ratio": 0.15,
                "max_utilization": 0.85,
                "storage_cost_limit": 15,
                "handling_cost_limit": 25
            },
            "delivery": {
                "urgent_hours": 4,
                "standard_days": 2,
                "min_on_time_rate": 0.95,
                "max_damage_rate": 0.01
            }
        }

        # 环保配置模板
        environmental_template = {
            "carbon_emission": {
                "monthly_limit": 1000,
                "daily_limit": 35,
                "electricity_factor": 0.5703,
                "gas_factor": 2.162
            },
            "energy_consumption": {
                "monthly_limit": 50000,
                "peak_power_limit": 1000,
                "efficiency_target": 0.15,
                "renewable_target": 0.3
            },
            "waste_management": {
                "max_waste_ratio": 0.05,
                "recycling_target": 0.8,
                "disposal_cost": 500,
                "recycling_revenue": 200
            }
        }

        # 成本配置模板
        cost_template = {
            "cost_structure": {
                "material_weight": 0.4,
                "labor_weight": 0.25,
                "energy_weight": 0.15,
                "overhead_weight": 0.1,
                "depreciation_weight": 0.08,
                "maintenance_weight": 0.05
            },
            "cost_control": {
                "monthly_budget": 2000000,
                "variance_limit": 0.1,
                "target_profit_margin": 0.2,
                "min_order_profit": 1000
            }
        }

        # 人力资源配置模板
        hr_template = {
            "personnel": {
                "skill_levels": ["初级", "中级", "高级"],
                "worker_types": ["操作工", "技术员"],
                "standard_hours": 8,
                "max_overtime": 2,
                "base_rate": 50,
                "overtime_multiplier": 1.5
            },
            "scheduling": {
                "shift_pattern": "两班制",
                "shift_duration": 8,
                "min_rest_hours": 12,
                "max_consecutive_days": 6
            }
        }

        # 质量配置模板
        quality_template = {
            "quality_standards": {
                "target_pass_rate": 0.98,
                "max_defect_rate": 0.01,
                "inspection_frequency": "每班次",
                "sample_size_ratio": 0.05
            },
            "quality_costs": {
                "training_ratio": 0.03,
                "maintenance_ratio": 0.05,
                "rework_multiplier": 2.0,
                "scrap_multiplier": 3.0
            }
        }

        templates = [
            ("logistics", "默认物流配置", json.dumps(logistics_template), "标准物流配置模板", True),
            ("environmental", "默认环保配置", json.dumps(environmental_template), "标准环保配置模板", True),
            ("cost", "默认成本配置", json.dumps(cost_template), "标准成本配置模板", True),
            ("hr", "默认人力配置", json.dumps(hr_template), "标准人力资源配置模板", True),
            ("quality", "默认质量配置", json.dumps(quality_template), "标准质量配置模板", True)
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO config_templates
            (extension_type, template_name, template_data, description, is_default)
            VALUES (?, ?, ?, ?, ?)
        ''', templates)

    def save_config(self, extension_type: str, config_name: str, config_data: Dict[str, Any],
                   created_by: str = "system", change_reason: str = "用户配置") -> bool:
        """保存扩展配置"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            config_json = json.dumps(config_data, ensure_ascii=False, indent=2)

            # 检查是否已存在配置
            cursor.execute('''
                SELECT id, config_data FROM extension_configs
                WHERE extension_type = ? AND config_name = ?
            ''', (extension_type, config_name))

            existing = cursor.fetchone()

            if existing:
                # 更新现有配置
                config_id, old_config = existing

                # 记录配置历史
                cursor.execute('''
                    INSERT INTO config_history
                    (config_id, old_config_data, new_config_data, change_reason, changed_by)
                    VALUES (?, ?, ?, ?, ?)
                ''', (config_id, old_config, config_json, change_reason, created_by))

                # 更新配置
                cursor.execute('''
                    UPDATE extension_configs
                    SET config_data = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (config_json, config_id))

            else:
                # 创建新配置
                cursor.execute('''
                    INSERT INTO extension_configs
                    (extension_type, config_name, config_data, created_by)
                    VALUES (?, ?, ?, ?)
                ''', (extension_type, config_name, config_json, created_by))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"保存配置失败: {e}")
            return False

    def load_config(self, extension_type: str, config_name: str = "default") -> Optional[Dict[str, Any]]:
        """加载扩展配置"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT config_data FROM extension_configs
                WHERE extension_type = ? AND config_name = ? AND is_active = 1
            ''', (extension_type, config_name))

            result = cursor.fetchone()
            conn.close()

            if result:
                return json.loads(result[0])
            else:
                # 如果没有找到配置，尝试加载默认模板
                return self.load_template(extension_type, "默认" + extension_type + "配置")

        except Exception as e:
            print(f"加载配置失败: {e}")
            return None

    def load_template(self, extension_type: str, template_name: str) -> Optional[Dict[str, Any]]:
        """加载配置模板"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT template_data FROM config_templates
                WHERE extension_type = ? AND template_name = ?
            ''', (extension_type, template_name))

            result = cursor.fetchone()
            conn.close()

            if result:
                return json.loads(result[0])
            return None

        except Exception as e:
            print(f"加载模板失败: {e}")
            return None

    def save_template(self, extension_type: str, template_name: str, template_data: Dict[str, Any],
                     description: str = "", is_default: bool = False) -> bool:
        """保存配置模板"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            template_json = json.dumps(template_data, ensure_ascii=False, indent=2)

            # 如果设为默认模板，先取消其他默认模板
            if is_default:
                cursor.execute('''
                    UPDATE config_templates
                    SET is_default = 0
                    WHERE extension_type = ?
                ''', (extension_type,))

            # 插入或更新模板
            cursor.execute('''
                INSERT OR REPLACE INTO config_templates
                (extension_type, template_name, template_data, description, is_default)
                VALUES (?, ?, ?, ?, ?)
            ''', (extension_type, template_name, template_json, description, is_default))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"保存模板失败: {e}")
            return False

    def get_config_list(self, extension_type: str) -> List[Dict[str, Any]]:
        """获取配置列表"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT config_name, version, created_by, created_at, updated_at, is_active
                FROM extension_configs
                WHERE extension_type = ?
                ORDER BY updated_at DESC
            ''', (extension_type,))

            configs = []
            for row in cursor.fetchall():
                configs.append({
                    "config_name": row[0],
                    "version": row[1],
                    "created_by": row[2],
                    "created_at": row[3],
                    "updated_at": row[4],
                    "is_active": bool(row[5])
                })

            conn.close()
            return configs

        except Exception as e:
            print(f"获取配置列表失败: {e}")
            return []

    def get_template_list(self, extension_type: str) -> List[Dict[str, Any]]:
        """获取模板列表"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT template_name, description, is_default, created_at
                FROM config_templates
                WHERE extension_type = ?
                ORDER BY is_default DESC, created_at DESC
            ''', (extension_type,))

            templates = []
            for row in cursor.fetchall():
                templates.append({
                    "template_name": row[0],
                    "description": row[1],
                    "is_default": bool(row[2]),
                    "created_at": row[3]
                })

            conn.close()
            return templates

        except Exception as e:
            print(f"获取模板列表失败: {e}")
            return []

    def delete_config(self, extension_type: str, config_name: str) -> bool:
        """删除配置（软删除）"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE extension_configs
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE extension_type = ? AND config_name = ?
            ''', (extension_type, config_name))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"删除配置失败: {e}")
            return False

    def get_config_history(self, extension_type: str, config_name: str) -> List[Dict[str, Any]]:
        """获取配置历史"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT h.old_config_data, h.new_config_data, h.change_reason,
                       h.changed_by, h.changed_at
                FROM config_history h
                JOIN extension_configs c ON h.config_id = c.id
                WHERE c.extension_type = ? AND c.config_name = ?
                ORDER BY h.changed_at DESC
            ''', (extension_type, config_name))

            history = []
            for row in cursor.fetchall():
                history.append({
                    "old_config": json.loads(row[0]) if row[0] else None,
                    "new_config": json.loads(row[1]) if row[1] else None,
                    "change_reason": row[2],
                    "changed_by": row[3],
                    "changed_at": row[4]
                })

            conn.close()
            return history

        except Exception as e:
            print(f"获取配置历史失败: {e}")
            return []


# 全局配置服务实例
extension_config_service = ExtensionConfigService()
