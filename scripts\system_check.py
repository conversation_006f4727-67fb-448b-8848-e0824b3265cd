#!/usr/bin/env python3
"""
Smart Planning 系统完整性检查脚本
检查系统功能完整性、架构正确性、代码清晰度和文档完善性
"""

import os
import sys
import json
import asyncio
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "backend"))
sys.path.append(str(project_root / "frontend"))


class SystemChecker:
    """系统检查器"""
    
    def __init__(self):
        self.project_root = project_root
        self.issues = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0
    
    def log_success(self, message: str):
        """记录成功"""
        print(f"✅ {message}")
        self.success_count += 1
        self.total_checks += 1
    
    def log_warning(self, message: str):
        """记录警告"""
        print(f"⚠️  {message}")
        self.warnings.append(message)
        self.total_checks += 1
    
    def log_error(self, message: str):
        """记录错误"""
        print(f"❌ {message}")
        self.issues.append(message)
        self.total_checks += 1
    
    def check_file_exists(self, file_path: str, description: str = None) -> bool:
        """检查文件是否存在"""
        full_path = self.project_root / file_path
        if full_path.exists():
            self.log_success(f"{description or file_path} 存在")
            return True
        else:
            self.log_error(f"{description or file_path} 不存在")
            return False
    
    def check_core_files(self):
        """检查核心文件"""
        print("\n🔍 检查核心文件...")
        
        core_files = [
            ("README.md", "项目说明文档"),
            (".env.example", "环境配置示例"),
            ("docker-compose.yml", "Docker编排配置"),
            ("deploy.sh", "Linux部署脚本"),
            ("deploy.bat", "Windows部署脚本"),
            ("scripts/mysql/init.sql", "数据库初始化脚本"),
        ]
        
        for file_path, description in core_files:
            self.check_file_exists(file_path, description)
    
    def check_backend_structure(self):
        """检查后端结构"""
        print("\n🔍 检查后端结构...")
        
        backend_files = [
            ("backend/Dockerfile", "后端Docker文件"),
            ("backend/requirements.txt", "后端依赖文件"),
            ("backend/app/main.py", "后端主应用"),
            ("backend/app/core/config.py", "核心配置"),
            ("backend/app/core/database.py", "数据库配置"),
            ("backend/app/api/v1/endpoints/system.py", "系统API端点"),
            ("backend/scripts/init_database.py", "数据库初始化脚本"),
            ("backend/scripts/init_data.py", "数据初始化脚本"),
            ("backend/scripts/start.sh", "启动脚本"),
        ]
        
        for file_path, description in backend_files:
            self.check_file_exists(file_path, description)
    
    def check_frontend_structure(self):
        """检查前端结构"""
        print("\n🔍 检查前端结构...")
        
        frontend_files = [
            ("frontend/Dockerfile", "前端Docker文件"),
            ("frontend/requirements.txt", "前端依赖文件"),
            ("frontend/main.py", "前端主应用"),
            ("frontend/config/settings.py", "前端配置"),
            ("frontend/config/theme.py", "主题配置"),
        ]
        
        for file_path, description in frontend_files:
            self.check_file_exists(file_path, description)
        
        # 检查页面文件
        pages_dir = self.project_root / "frontend" / "pages"
        if pages_dir.exists():
            page_files = list(pages_dir.glob("*.py"))
            if len(page_files) >= 15:
                self.log_success(f"前端页面文件完整 ({len(page_files)} 个页面)")
            else:
                self.log_warning(f"前端页面文件可能不完整 (只有 {len(page_files)} 个页面)")
        else:
            self.log_error("前端页面目录不存在")
    
    def check_documentation(self):
        """检查文档"""
        print("\n🔍 检查文档...")
        
        doc_files = [
            ("docs/README.md", "文档说明"),
            ("docs/architecture/system_architecture.md", "系统架构文档"),
            ("docs/user_guide/quick_start.md", "快速开始指南"),
            ("docs/user_guide/feature_guide.md", "功能使用指南"),
            ("docs/technical/development_guide.md", "开发指南"),
            ("docs/technical/extension_development.md", "扩展开发指南"),
        ]
        
        for file_path, description in doc_files:
            self.check_file_exists(file_path, description)
    
    def check_configuration_consistency(self):
        """检查配置一致性"""
        print("\n🔍 检查配置一致性...")
        
        try:
            # 检查.env.example文件内容
            env_file = self.project_root / ".env.example"
            if env_file.exists():
                content = env_file.read_text(encoding='utf-8')
                
                # 检查关键配置项
                required_configs = [
                    "DATABASE_URL",
                    "REDIS_URL", 
                    "SECRET_KEY",
                    "DEFAULT_LLM_SERVICE",
                    "SMTP_HOST"
                ]
                
                missing_configs = []
                for config in required_configs:
                    if config not in content:
                        missing_configs.append(config)
                
                if missing_configs:
                    self.log_warning(f"环境配置文件缺少配置项: {', '.join(missing_configs)}")
                else:
                    self.log_success("环境配置文件配置项完整")
                
                # 检查数据库配置是否使用MySQL
                if "mysql+aiomysql" in content:
                    self.log_success("数据库配置使用MySQL")
                else:
                    self.log_warning("数据库配置可能不是MySQL")
            
        except Exception as e:
            self.log_error(f"检查配置一致性失败: {str(e)}")
    
    def check_naming_consistency(self):
        """检查命名一致性"""
        print("\n🔍 检查命名一致性...")
        
        try:
            # 检查README.md中的项目名称
            readme_file = self.project_root / "README.md"
            if readme_file.exists():
                content = readme_file.read_text(encoding='utf-8')
                if "Smart Planning" in content and "Smart APS" not in content:
                    self.log_success("README.md 项目名称一致")
                else:
                    self.log_warning("README.md 项目名称可能不一致")
            
            # 检查docker-compose.yml中的服务名称
            compose_file = self.project_root / "docker-compose.yml"
            if compose_file.exists():
                content = compose_file.read_text(encoding='utf-8')
                if "smart_planning" in content:
                    self.log_success("Docker Compose 服务名称一致")
                else:
                    self.log_warning("Docker Compose 服务名称可能不一致")
            
        except Exception as e:
            self.log_error(f"检查命名一致性失败: {str(e)}")
    
    def check_dependencies(self):
        """检查依赖文件"""
        print("\n🔍 检查依赖文件...")
        
        # 检查后端依赖
        backend_req = self.project_root / "backend" / "requirements.txt"
        if backend_req.exists():
            content = backend_req.read_text()
            required_packages = ["fastapi", "sqlalchemy", "mysql", "redis", "streamlit"]
            missing_packages = []
            
            for package in required_packages:
                if package.lower() not in content.lower():
                    missing_packages.append(package)
            
            if missing_packages:
                self.log_warning(f"后端可能缺少依赖包: {', '.join(missing_packages)}")
            else:
                self.log_success("后端依赖包检查通过")
        
        # 检查前端依赖
        frontend_req = self.project_root / "frontend" / "requirements.txt"
        if frontend_req.exists():
            content = frontend_req.read_text()
            if "streamlit" in content.lower():
                self.log_success("前端依赖包检查通过")
            else:
                self.log_warning("前端缺少Streamlit依赖")
    
    def generate_report(self):
        """生成检查报告"""
        print("\n" + "="*60)
        print("📊 系统检查报告")
        print("="*60)
        
        success_rate = (self.success_count / self.total_checks * 100) if self.total_checks > 0 else 0
        
        print(f"✅ 成功检查: {self.success_count}/{self.total_checks} ({success_rate:.1f}%)")
        print(f"⚠️  警告数量: {len(self.warnings)}")
        print(f"❌ 错误数量: {len(self.issues)}")
        
        if self.warnings:
            print("\n⚠️  警告列表:")
            for warning in self.warnings:
                print(f"   - {warning}")
        
        if self.issues:
            print("\n❌ 错误列表:")
            for issue in self.issues:
                print(f"   - {issue}")
        
        if success_rate >= 90:
            print("\n🎉 系统检查优秀！可以上线部署。")
            return True
        elif success_rate >= 80:
            print("\n👍 系统检查良好，建议修复警告后上线。")
            return True
        else:
            print("\n⚠️  系统检查发现问题，建议修复后再上线。")
            return False
    
    def run_all_checks(self):
        """运行所有检查"""
        print("🚀 开始 Smart Planning 系统完整性检查...")
        
        self.check_core_files()
        self.check_backend_structure()
        self.check_frontend_structure()
        self.check_documentation()
        self.check_configuration_consistency()
        self.check_naming_consistency()
        self.check_dependencies()
        
        return self.generate_report()


def main():
    """主函数"""
    checker = SystemChecker()
    success = checker.run_all_checks()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
