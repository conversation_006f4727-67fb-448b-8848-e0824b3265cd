"""
认证相关工具函数
"""

import streamlit as st
from typing import Optional
import time

from config.settings import AppConfig, MESSAGES


def check_authentication() -> bool:
    """检查用户认证状态"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    
    if not st.session_state.authenticated:
        return False
    
    # 检查令牌是否存在
    if 'access_token' not in st.session_state:
        st.session_state.authenticated = False
        return False
    
    # 检查令牌是否过期（简单检查）
    if 'token_expires_at' in st.session_state:
        if time.time() > st.session_state.token_expires_at:
            # 尝试刷新令牌
            if not refresh_access_token():
                st.session_state.authenticated = False
                return False
    
    return True


def refresh_access_token() -> bool:
    """刷新访问令牌"""
    if 'refresh_token' not in st.session_state:
        return False
    
    try:
        api_client = st.session_state.get('api_client')
        if not api_client:
            return False
        
        result = api_client.refresh_token(st.session_state.refresh_token)
        
        if result.get('success'):
            # 更新令牌信息
            st.session_state.access_token = result['data']['access_token']
            st.session_state.token_expires_at = time.time() + result['data']['expires_in']
            return True
        else:
            return False
            
    except Exception:
        return False


def show_login_page():
    """显示登录页面"""
    st.title("🏭 Smart APS")
    st.markdown("### 智能工厂生产管理规划系统")
    
    # 居中显示登录表单
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("#### 用户登录")
        
        with st.form("login_form", clear_on_submit=False):
            username = st.text_input(
                "用户名",
                placeholder="请输入用户名或邮箱",
                help="支持用户名或邮箱登录"
            )
            
            password = st.text_input(
                "密码",
                type="password",
                placeholder="请输入密码"
            )
            
            col_login, col_demo = st.columns(2)
            
            with col_login:
                login_clicked = st.form_submit_button(
                    "登录",
                    use_container_width=True,
                    type="primary"
                )
            
            with col_demo:
                demo_clicked = st.form_submit_button(
                    "演示账户",
                    use_container_width=True,
                    help="使用演示账户快速体验"
                )
            
            if login_clicked or demo_clicked:
                if demo_clicked:
                    # 使用演示账户
                    username = "admin"
                    password = "admin123456"
                
                if not username or not password:
                    st.error("请输入用户名和密码")
                else:
                    # 显示登录进度
                    with st.spinner("正在登录..."):
                        result = authenticate_user(username, password)
                    
                    if result:
                        st.success(MESSAGES["login_success"])
                        time.sleep(1)  # 短暂延迟以显示成功消息
                        st.rerun()
                    else:
                        st.error(MESSAGES["login_failed"])
        
        # 系统信息
        st.markdown("---")
        st.markdown("#### 系统信息")
        
        col_info1, col_info2 = st.columns(2)
        
        with col_info1:
            st.markdown(f"""
            **版本**: {AppConfig.VERSION}  
            **环境**: {'开发' if AppConfig.DEBUG else '生产'}  
            **API**: {AppConfig.API_BASE_URL}
            """)
        
        with col_info2:
            st.markdown(f"""
            **LLM**: {AppConfig.DEFAULT_LLM_SERVICE.title()}  
            **支持**: 20-30人团队  
            **特性**: 智能规划优化
            """)
        
        # 功能特性
        st.markdown("---")
        st.markdown("#### 🚀 主要功能")
        
        features = [
            "📁 **智能文件上传** - 支持邮件、Excel自动解析",
            "📋 **生产规划优化** - MILP算法 + LLM智能决策",
            "📈 **实时计划监控** - 交互式甘特图和仪表盘",
            "📊 **数据分析可视化** - 丰富的图表和报表",
            "🤖 **AI智能助手** - 本地Ollama + Azure OpenAI",
            "⚙️ **灵活配置管理** - 可视化配置，无需编程"
        ]
        
        for feature in features:
            st.markdown(feature)
        
        # 快速开始提示
        if AppConfig.DEBUG:
            st.markdown("---")
            st.info("""
            💡 **快速开始**  
            演示账户: admin / admin123456  
            或创建新账户开始体验系统功能
            """)


def authenticate_user(username: str, password: str) -> bool:
    """认证用户"""
    try:
        api_client = st.session_state.get('api_client')
        if not api_client:
            st.error("API客户端未初始化")
            return False
        
        # 调用登录API
        result = api_client.login(username, password)
        
        if result.get('success'):
            # 保存认证信息
            data = result.get('data', {})
            
            st.session_state.authenticated = True
            st.session_state.access_token = data.get('access_token')
            st.session_state.refresh_token = data.get('refresh_token')
            st.session_state.user_info = data.get('user_info', {})
            st.session_state.token_expires_at = time.time() + data.get('expires_in', 3600)
            
            return True
        else:
            error_message = result.get('message', '登录失败')
            st.error(error_message)
            return False
            
    except Exception as e:
        st.error(f"登录过程中发生错误: {str(e)}")
        return False


def logout_user():
    """用户登出"""
    try:
        api_client = st.session_state.get('api_client')
        if api_client:
            # 调用登出API
            api_client.logout()
        
        # 清除本地会话状态
        keys_to_clear = [
            'authenticated', 'access_token', 'refresh_token', 
            'user_info', 'token_expires_at'
        ]
        
        for key in keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]
        
        st.success(MESSAGES["logout_success"])
        time.sleep(1)
        st.rerun()
        
    except Exception as e:
        st.error(f"登出过程中发生错误: {str(e)}")


def get_current_user() -> Optional[dict]:
    """获取当前用户信息"""
    return st.session_state.get('user_info')


def has_permission(permission: str) -> bool:
    """检查用户是否有指定权限"""
    user_info = get_current_user()
    if not user_info:
        return False
    
    # 超级用户拥有所有权限
    if user_info.get('is_superuser', False):
        return True
    
    # 检查用户权限列表
    permissions = user_info.get('permissions', [])
    return permission in permissions


def require_permission(permission: str) -> bool:
    """要求用户具有指定权限"""
    if not has_permission(permission):
        st.error(MESSAGES["permission_denied"])
        st.info(f"需要权限: {permission}")
        return False
    return True


def get_user_permissions() -> list:
    """获取用户权限列表"""
    user_info = get_current_user()
    if not user_info:
        return []
    
    return user_info.get('permissions', [])
