#!/usr/bin/env python3
"""
最终系统名称检查和替换脚本
检查并替换所有剩余的"Smart APS"引用为"Smart Planning"
"""

import os
import re
from pathlib import Path
from typing import List, <PERSON><PERSON>

def find_smart_aps_references(directory: Path) -> List[Tuple[str, int, str]]:
    """查找所有包含Smart APS引用的文件和行"""
    references = []
    
    # 要检查的文件扩展名
    extensions = {'.py', '.md', '.yml', '.yaml', '.json', '.txt', '.sql', '.sh', '.bat', '.env'}
    
    # 要排除的目录
    exclude_dirs = {'.git', '__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv'}
    
    # 搜索模式
    patterns = [
        r'Smart\s*APS',
        r'smart[-_]aps',
        r'smartaps',
        r'SMART[-_]APS',
        r'SmartAPS'
    ]
    
    for root, dirs, files in os.walk(directory):
        # 排除特定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            file_path = Path(root) / file
            
            # 只检查指定扩展名的文件
            if file_path.suffix.lower() not in extensions:
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    
                for line_num, line in enumerate(lines, 1):
                    for pattern in patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            references.append((str(file_path), line_num, line.strip()))
                            break
                            
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
                
    return references

def generate_replacement_suggestions(references: List[Tuple[str, int, str]]) -> List[str]:
    """生成替换建议"""
    suggestions = []
    
    for file_path, line_num, line in references:
        # 生成替换建议
        original_line = line
        
        # 替换各种变体
        replacements = [
            (r'Smart\s*APS', 'Smart Planning'),
            (r'smart[-_]aps', 'smart-planning'),
            (r'smartaps', 'smartplanning'),
            (r'SMART[-_]APS', 'SMART-PLANNING'),
            (r'SmartAPS', 'SmartPlanning')
        ]
        
        new_line = original_line
        for pattern, replacement in replacements:
            new_line = re.sub(pattern, replacement, new_line, flags=re.IGNORECASE)
        
        if new_line != original_line:
            suggestions.append(f"""
文件: {file_path}
行号: {line_num}
原内容: {original_line}
建议替换为: {new_line}
""")
    
    return suggestions

def main():
    """主函数"""
    print("🔍 开始检查系统中的Smart APS引用...")
    
    # 项目根目录
    project_root = Path(__file__).parent.parent
    
    # 查找所有引用
    references = find_smart_aps_references(project_root)
    
    if not references:
        print("✅ 未发现任何Smart APS引用，系统名称已完全统一为Smart Planning！")
        return
    
    print(f"⚠️  发现 {len(references)} 处Smart APS引用需要替换：")
    print("=" * 60)
    
    # 按文件分组显示
    current_file = None
    for file_path, line_num, line in references:
        if file_path != current_file:
            current_file = file_path
            print(f"\n📁 文件: {file_path}")
            print("-" * 40)
        
        print(f"  行 {line_num}: {line}")
    
    print("\n" + "=" * 60)
    print("📝 替换建议:")
    
    suggestions = generate_replacement_suggestions(references)
    for suggestion in suggestions:
        print(suggestion)
    
    print("\n🔧 手动替换指南:")
    print("1. 使用IDE的全局搜索替换功能")
    print("2. 搜索模式: Smart APS|smart-aps|smartaps|SmartAPS")
    print("3. 替换为对应的Smart Planning变体")
    print("4. 注意保持原有的大小写和连接符格式")

if __name__ == "__main__":
    main()
