"""
API依赖项
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from app.core.database import get_db
from app.core.security import token_manager
from app.services.user_service import UserService
from app.services.auth_service import AuthService

logger = logging.getLogger(__name__)

# HTTP Bearer认证
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """获取当前用户"""
    try:
        # 验证令牌
        payload = token_manager.verify_token(credentials.credentials, "access")
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的访问令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌载荷无效",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 验证会话是否有效
        auth_service = AuthService(db)
        session = await auth_service.get_session_by_token(credentials.credentials)
        if not session or not session.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="会话已失效",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 获取用户信息
        user_service = UserService(db)
        user = await user_service.get_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账户已被禁用",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 返回用户信息
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "is_superuser": user.is_superuser,
            "is_active": user.is_active,
            "permissions": list(user.get_permissions())
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取当前用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: dict = Depends(get_current_user)
) -> dict:
    """获取当前活跃用户"""
    if not current_user.get("is_active"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    return current_user


async def get_current_superuser(
    current_user: dict = Depends(get_current_user)
) -> dict:
    """获取当前超级用户"""
    if not current_user.get("is_superuser"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要超级用户权限"
        )
    return current_user


def require_permission(permission: str):
    """权限检查装饰器"""
    def permission_checker(current_user: dict = Depends(get_current_user)) -> dict:
        # 超级用户拥有所有权限
        if current_user.get("is_superuser"):
            return current_user
        
        # 检查用户权限
        user_permissions = current_user.get("permissions", [])
        if permission not in user_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要权限: {permission}"
            )
        
        return current_user
    
    return permission_checker


def require_any_permission(*permissions: str):
    """要求任一权限的装饰器"""
    def permission_checker(current_user: dict = Depends(get_current_user)) -> dict:
        # 超级用户拥有所有权限
        if current_user.get("is_superuser"):
            return current_user
        
        # 检查用户是否拥有任一权限
        user_permissions = current_user.get("permissions", [])
        if not any(perm in user_permissions for perm in permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要以下任一权限: {', '.join(permissions)}"
            )
        
        return current_user
    
    return permission_checker


def require_all_permissions(*permissions: str):
    """要求所有权限的装饰器"""
    def permission_checker(current_user: dict = Depends(get_current_user)) -> dict:
        # 超级用户拥有所有权限
        if current_user.get("is_superuser"):
            return current_user
        
        # 检查用户是否拥有所有权限
        user_permissions = current_user.get("permissions", [])
        missing_permissions = [perm for perm in permissions if perm not in user_permissions]
        
        if missing_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，缺少权限: {', '.join(missing_permissions)}"
            )
        
        return current_user
    
    return permission_checker


async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> Optional[dict]:
    """获取可选用户（用于公开API）"""
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials, db)
    except HTTPException:
        return None


class PaginationParams:
    """分页参数"""
    def __init__(
        self,
        page: int = 1,
        page_size: int = 20,
        max_page_size: int = 100
    ):
        if page < 1:
            page = 1
        if page_size < 1:
            page_size = 20
        if page_size > max_page_size:
            page_size = max_page_size
        
        self.page = page
        self.page_size = page_size
        self.offset = (page - 1) * page_size


def get_pagination_params(
    page: int = 1,
    page_size: int = 20
) -> PaginationParams:
    """获取分页参数"""
    return PaginationParams(page, page_size)


class SearchParams:
    """搜索参数"""
    def __init__(
        self,
        search: Optional[str] = None,
        sort_by: Optional[str] = None,
        sort_order: str = "desc"
    ):
        self.search = search.strip() if search else None
        self.sort_by = sort_by
        self.sort_order = sort_order.lower() if sort_order.lower() in ["asc", "desc"] else "desc"


def get_search_params(
    search: Optional[str] = None,
    sort_by: Optional[str] = None,
    sort_order: str = "desc"
) -> SearchParams:
    """获取搜索参数"""
    return SearchParams(search, sort_by, sort_order)
