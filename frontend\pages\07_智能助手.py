"""
智能助手页面
"""

import streamlit as st
import pandas as pd
import json
import uuid
from datetime import datetime, timedelta
import time

from config.settings import AppConfig
from config.theme import apply_plotly_theme
from utils.auth import check_authentication, require_permission
from utils.api_client import APIClient
from utils.i18n import i18n
from services.multilingual_llm_service import multilingual_llm_service
from services.data_integration_service import data_integration_service
from services.unified_ai_service import unified_ai_service, AIRequest, AIServiceType

# 页面配置
st.set_page_config(
    page_title="智能助手 - Smart Planning",
    page_icon="🤖",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 检查权限
if not require_permission("llm.chat"):
    st.error("权限不足")
    st.stop()

# 初始化API客户端
if 'api_client' not in st.session_state:
    st.session_state.api_client = APIClient(AppConfig.API_BASE_URL)

api_client = st.session_state.api_client

# 初始化会话状态
if 'chat_messages' not in st.session_state:
    st.session_state.chat_messages = []

if 'current_session_id' not in st.session_state:
    st.session_state.current_session_id = str(uuid.uuid4())

if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []

if 'llm_config' not in st.session_state:
    st.session_state.llm_config = None

if 'available_models' not in st.session_state:
    st.session_state.available_models = []

# 页面标题
st.title(f"🤖 {i18n.t('ai_assistant')}")
if i18n.get_current_language() == "en":
    st.markdown("### Your professional production management AI assistant, providing intelligent analysis and optimization recommendations")
else:
    st.markdown("### 您的专业生产管理AI助手，为您提供智能分析和优化建议")

# 侧边栏 - 配置和历史
with st.sidebar:
    # 语言选择器
    i18n.language_selector("ai_language_selector")

    st.markdown("---")
    st.markdown(f"### ⚙️ {i18n.t('assistant_config', default='助手配置')}")

    # 获取可用模型
    if st.button(f"🔄 {i18n.t('refresh', default='刷新')} {i18n.t('model_selection', default='模型')}", use_container_width=True):
        load_available_models()

    # 模型选择
    if st.session_state.available_models:
        selected_model = st.selectbox(
            i18n.t("model_selection", default="选择模型"),
            options=[model['name'] for model in st.session_state.available_models],
            help=i18n.t("select_ai_model", default="选择要使用的AI模型")
        )
    else:
        selected_model = st.selectbox(i18n.t("model_selection", default="选择模型"), ["gpt-3.5-turbo", "llama2", "qwen"])

    # 语言检测设置
    st.markdown(f"#### 🌐 {i18n.t('language_detection', default='语言检测')}")

    language_mode = st.radio(
        i18n.t("language_mode", default="语言模式"),
        [i18n.t("auto_detect", default="自动检测"), i18n.t("chinese", default="中文"), i18n.t("english", default="English")],
        horizontal=True
    )

    # 转换为语言代码
    if language_mode == i18n.t("chinese", default="中文"):
        selected_language = "zh"
    elif language_mode == i18n.t("english", default="English"):
        selected_language = "en"
    else:
        selected_language = None  # 自动检测

    # 参数设置
    st.markdown(f"#### 🎛️ {i18n.t('parameter_settings', default='参数设置')}")

    temperature = st.slider(
        i18n.t("creativity", default="创造性"),
        min_value=0.0,
        max_value=2.0,
        value=0.7,
        step=0.1,
        help=i18n.t("creativity_help", default="控制回答的创造性，值越高越有创意")
    )

    max_tokens = st.slider(
        i18n.t("max_response_length", default="最大回复长度"),
        min_value=100,
        max_value=2000,
        value=1000,
        step=100,
        help=i18n.t("max_length_help", default="控制AI回复的最大长度")
    )

    st.markdown("---")

    # 快速操作
    st.markdown("### ⚡ 快速操作")

    if st.button("🆕 新建对话", use_container_width=True):
        start_new_conversation()

    if st.button("📋 查看历史", use_container_width=True):
        st.session_state.show_history = True
        load_chat_history()

    if st.button("🗑️ 清空当前对话", use_container_width=True):
        clear_current_conversation()

    st.markdown("---")

    # 专业模板
    st.markdown("### 📝 专业模板")

    # 静态模板
    static_templates = {
        "生产规划分析": "请帮我分析当前的生产规划数据，识别潜在的瓶颈和优化机会。",
        "设备故障诊断": "我的设备出现了异常，请帮我分析可能的原因和解决方案。",
        "质量问题分析": "产品质量出现波动，请帮我分析原因并提供改进建议。",
        "成本优化建议": "请分析我的生产成本构成，提供降本增效的建议。",
        "效率提升方案": "请帮我分析生产效率数据，提供提升方案。"
    }

    # 动态数据模板
    dynamic_templates = {
        "PCI性能分析": "PCI Performance Analysis",
        "实时设备状态": "Real-time Equipment Status",
        "生产KPI分析": "Production KPI Analysis",
        "库存优化分析": "Inventory Optimization"
    }

    template_type = st.radio(
        "模板类型",
        ["静态模板", "动态数据模板"],
        horizontal=True
    )

    if template_type == "静态模板":
        selected_template = st.selectbox("选择模板", ["自定义"] + list(static_templates.keys()))

        if selected_template != "自定义":
            if st.button("📤 使用模板", use_container_width=True):
                st.session_state.template_message = static_templates[selected_template]
                st.rerun()

    else:  # 动态数据模板
        selected_dynamic_template = st.selectbox("选择数据模板", ["请选择"] + list(dynamic_templates.keys()))

        if selected_dynamic_template != "请选择":
            if st.button("🔍 获取数据并分析", use_container_width=True):
                st.session_state.selected_dynamic_template = selected_dynamic_template
                st.session_state.show_dynamic_template = True
                st.rerun()

    st.markdown("---")

    # 高级功能
    st.markdown("### 🚀 高级功能")

    if st.button("📊 数据分析", use_container_width=True):
        st.session_state.show_data_analysis = True
        st.rerun()

    if st.button("💡 优化建议", use_container_width=True):
        st.session_state.show_optimization = True
        st.rerun()

    if st.button("📈 趋势预测", use_container_width=True):
        st.session_state.template_message = "请基于历史数据帮我预测未来的生产趋势和可能的风险点。"
        st.rerun()

    if st.button("📊 查看数据上下文", use_container_width=True):
        st.session_state.show_data_context = True
        st.rerun()

    st.markdown("---")

    # AI增强功能
    st.markdown("### 🧠 AI增强功能")

    if st.button("🔮 需求预测", use_container_width=True):
        st.session_state.show_ai_prediction = True
        st.rerun()

    if st.button("🔍 异常检测", use_container_width=True):
        st.session_state.show_ai_anomaly = True
        st.rerun()

    if st.button("⚡ 智能优化", use_container_width=True):
        st.session_state.show_ai_optimization = True
        st.rerun()

    if st.button("📊 综合AI分析", use_container_width=True):
        st.session_state.show_comprehensive_ai = True
        st.rerun()

# 主要内容区域
col1, col2 = st.columns([3, 1])

with col1:
    # 聊天界面
    st.markdown("#### 💬 对话界面")

    # 聊天消息容器
    chat_container = st.container()

    with chat_container:
        # 显示聊天历史
        for message in st.session_state.chat_messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])

                # 显示时间戳
                if "timestamp" in message:
                    st.caption(f"🕒 {message['timestamp']}")

                # 如果是助手消息，显示额外信息
                if message["role"] == "assistant" and "metadata" in message:
                    metadata = message["metadata"]
                    if metadata.get("model"):
                        st.caption(f"🤖 模型: {metadata['model']}")
                    if metadata.get("tokens_used"):
                        st.caption(f"📊 令牌使用: {metadata['tokens_used']}")

    # 消息输入
    message_input_container = st.container()

    with message_input_container:
        # 检查是否有模板消息
        default_message = ""
        if hasattr(st.session_state, 'template_message'):
            default_message = st.session_state.template_message
            del st.session_state.template_message

        # 消息输入框
        user_message = st.chat_input(
            "请输入您的问题...",
            key="chat_input"
        )

        # 如果有默认消息，设置到输入框
        if default_message:
            user_message = default_message

        # 处理用户消息
        if user_message:
            handle_user_message(user_message, selected_model, temperature, max_tokens, selected_language)

with col2:
    # 右侧信息面板
    st.markdown("#### 📊 会话信息")

    # 当前会话统计
    message_count = len(st.session_state.chat_messages)
    user_messages = len([m for m in st.session_state.chat_messages if m["role"] == "user"])
    assistant_messages = len([m for m in st.session_state.chat_messages if m["role"] == "assistant"])

    col_a, col_b = st.columns(2)
    with col_a:
        st.metric("消息总数", message_count)
    with col_b:
        st.metric("AI回复", assistant_messages)

    # 会话ID
    st.text_input("会话ID", value=st.session_state.current_session_id, disabled=True)

    st.markdown("---")

    # 系统状态
    st.markdown("#### 🔧 系统状态")

    # 获取LLM配置状态
    if st.button("🔍 检查状态"):
        check_llm_status()

    # 显示状态信息
    if hasattr(st.session_state, 'llm_status'):
        status = st.session_state.llm_status
        if status.get("success"):
            st.success("✅ LLM服务正常")
            if "service_info" in status:
                info = status["service_info"]
                st.write(f"**服务**: {info.get('service', 'Unknown')}")
                st.write(f"**模型**: {info.get('model', 'Unknown')}")
        else:
            st.error("❌ LLM服务异常")
            st.write(status.get("message", "未知错误"))

    st.markdown("---")

    # 使用提示
    st.markdown("#### 💡 使用提示")

    tips = [
        "💬 直接输入问题开始对话",
        "📝 使用专业模板快速开始",
        "🎛️ 调整参数控制回答风格",
        "📋 查看历史对话记录",
        "🔄 刷新获取最新模型"
    ]

    for tip in tips:
        st.markdown(f"- {tip}")


# 模态框处理
if st.session_state.get('show_history'):
    show_chat_history_modal()

# 数据上下文显示
if st.session_state.get('show_data_context'):
    with st.modal("📊 当前数据上下文"):
        st.markdown("### 系统数据概览")

        try:
            data_context = data_integration_service.get_comprehensive_data_context()

            # 显示数据摘要
            summary = data_context.get("summary", {})

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("设备总数", summary.get("equipment_status", {}).get("total", 0))
            with col2:
                st.metric("PCI物料", summary.get("pci_status", {}).get("total_fs_items", 0))
            with col3:
                st.metric("数据文件", summary.get("data_files", {}).get("recent_files", 0))

            # 显示约束条件
            constraints = data_context.get("constraints", [])
            if constraints:
                st.markdown("#### ⚠️ 当前约束条件")
                for constraint in constraints:
                    st.warning(constraint.get("description", "未知约束"))

            # 显示建议
            recommendations = data_context.get("recommendations", [])
            if recommendations:
                st.markdown("#### 💡 系统建议")
                for rec in recommendations:
                    st.info(rec)

        except Exception as e:
            st.error(f"获取数据上下文失败: {str(e)}")

        if st.button("关闭", use_container_width=True):
            st.session_state.show_data_context = False
            st.rerun()


# 辅助函数
def load_available_models():
    """加载可用模型"""
    try:
        result = api_client.get_available_models()
        if result.get("success"):
            models = result.get("data", {}).get("models", [])
            st.session_state.available_models = models
            st.success(f"✅ 已加载 {len(models)} 个模型")
        else:
            st.error("获取模型列表失败")
            # 使用默认模型
            st.session_state.available_models = [
                {"name": "gpt-3.5-turbo", "service": "azure", "status": "available"},
                {"name": "llama2", "service": "ollama", "status": "available"},
                {"name": "qwen", "service": "ollama", "status": "available"}
            ]
    except Exception as e:
        st.error(f"加载模型失败: {str(e)}")
        st.session_state.available_models = []


def show_plan_generation_result(plan_generation):
    """显示生产计划生成结果"""
    if plan_generation.get("success"):
        st.success("🎯 生产计划已自动生成！")

        with st.expander("📋 查看生成的生产计划", expanded=True):
            plan_data = plan_generation.get("plan_data", {})

            # 设备分配
            if "equipment_allocation" in plan_data:
                st.markdown("#### ⚙️ 设备分配")
                for equipment, allocation in plan_data["equipment_allocation"].items():
                    st.write(f"**{equipment}**: 订单 {', '.join(allocation['orders'])} (利用率: {allocation['utilization']}%)")

            # 物料消耗
            if "material_consumption" in plan_data:
                st.markdown("#### 📦 物料消耗策略")
                consumption = plan_data["material_consumption"]
                st.write(f"**优先物料**: {', '.join(consumption.get('priority_items', []))}")
                st.write(f"**消耗顺序**: {consumption.get('consumption_order', 'FIFO')}")

            # 时间安排
            if "timeline" in plan_data:
                st.markdown("#### 📅 时间安排")
                timeline = plan_data["timeline"]
                st.write(f"**开始日期**: {timeline.get('start_date')}")
                st.write(f"**结束日期**: {timeline.get('end_date')}")

            # 优化结果
            if "optimization_results" in plan_data:
                st.markdown("#### 📈 优化效果")
                results = plan_data["optimization_results"]
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("效率提升", results.get("efficiency_improvement", "0%"))
                with col2:
                    st.metric("成本降低", results.get("cost_reduction", "0%"))
                with col3:
                    st.metric("交期达成", results.get("delivery_performance", "0%"))

        # 操作按钮
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("✅ 应用计划", key="apply_plan"):
                apply_generated_plan(plan_data)
        with col2:
            if st.button("📊 更新图表", key="update_charts"):
                update_charts_with_plan(plan_data)
        with col3:
            if st.button("📤 导出计划", key="export_plan"):
                export_plan_data(plan_data)

    else:
        st.error(f"❌ 生产计划生成失败: {plan_generation.get('message', '未知错误')}")


def apply_generated_plan(plan_data):
    """应用生成的生产计划"""
    try:
        # 这里应该调用API更新生产计划
        # 现在模拟应用过程
        with st.spinner("正在应用生产计划..."):
            time.sleep(2)

        st.success("✅ 生产计划已成功应用到系统！")

        # 记录计划应用
        data_integration_service.record_user_input(
            input_type="plan_application",
            input_data={
                "plan_id": plan_data.get("plan_id"),
                "applied_at": datetime.now().isoformat(),
                "applied_by": st.session_state.get("user_id", "anonymous")
            },
            source_page="06_智能助手"
        )

    except Exception as e:
        st.error(f"❌ 应用计划失败: {str(e)}")


def update_charts_with_plan(plan_data):
    """使用计划数据更新图表"""
    try:
        with st.spinner("正在更新相关图表..."):
            time.sleep(1)

        st.success("📊 相关图表已更新！")
        st.info("💡 请前往生产规划页面查看更新后的甘特图和资源利用率图表")

        # 记录图表更新
        data_integration_service.record_user_input(
            input_type="chart_update",
            input_data={
                "plan_id": plan_data.get("plan_id"),
                "updated_charts": ["gantt_chart", "resource_utilization"],
                "updated_at": datetime.now().isoformat()
            },
            source_page="06_智能助手"
        )

    except Exception as e:
        st.error(f"❌ 更新图表失败: {str(e)}")


def export_plan_data(plan_data):
    """导出计划数据"""
    try:
        # 生成导出数据
        export_data = {
            "plan_info": plan_data,
            "export_time": datetime.now().isoformat(),
            "export_format": "JSON"
        }

        st.download_button(
            label="📥 下载计划文件",
            data=json.dumps(export_data, indent=2, ensure_ascii=False),
            file_name=f"production_plan_{plan_data.get('plan_id', 'unknown')}.json",
            mime="application/json"
        )

        st.success("📤 计划文件已准备好下载！")

    except Exception as e:
        st.error(f"❌ 导出失败: {str(e)}")


def start_new_conversation():
    """开始新对话"""
    st.session_state.chat_messages = []
    st.session_state.current_session_id = str(uuid.uuid4())
    # 重置对话ID以创建新的数据库记录
    if hasattr(st.session_state, 'current_conversation_id'):
        del st.session_state.current_conversation_id
    st.success("🆕 已开始新对话")
    st.rerun()


def clear_current_conversation():
    """清空当前对话"""
    st.session_state.chat_messages = []
    st.success("🗑️ 已清空当前对话")
    st.rerun()


def load_chat_history():
    """加载聊天历史"""
    try:
        user_id = st.session_state.get("user_id", "anonymous")
        conversations = multilingual_llm_service.conversation_manager.get_user_conversations(user_id)
        st.session_state.chat_history = conversations
        st.success(f"📋 已加载 {len(conversations)} 个历史对话")
    except Exception as e:
        st.error(f"加载聊天历史失败: {str(e)}")


def load_conversation(conversation_id):
    """加载指定对话"""
    try:
        messages = multilingual_llm_service.conversation_manager.get_conversation_history(conversation_id)

        # 转换为页面格式
        st.session_state.chat_messages = []
        for msg in messages:
            st.session_state.chat_messages.append({
                "role": msg["role"],
                "content": msg["content"],
                "timestamp": msg["timestamp"],
                "metadata": msg.get("metadata", {})
            })

        st.session_state.current_conversation_id = conversation_id
        st.success(f"✅ 已加载对话，共 {len(messages)} 条消息")
        st.rerun()

    except Exception as e:
        st.error(f"加载对话失败: {str(e)}")


def check_llm_status():
    """检查LLM状态"""
    try:
        result = api_client.get_llm_config()
        if result.get("success"):
            st.session_state.llm_status = {
                "success": True,
                "service_info": result.get("data", {})
            }
        else:
            st.session_state.llm_status = {
                "success": False,
                "message": result.get("message", "配置获取失败")
            }
    except Exception as e:
        st.session_state.llm_status = {
            "success": False,
            "message": f"状态检查失败: {str(e)}"
        }


def handle_user_message(message, model, temperature, max_tokens, language=None):
    """处理用户消息 - 增强版支持多轮对话和数据集成"""
    user_id = st.session_state.get("user_id", "anonymous")

    # 确保有对话ID
    if not hasattr(st.session_state, 'current_conversation_id') or not st.session_state.current_conversation_id:
        conversation_title = f"Conversation {datetime.now().strftime('%m-%d %H:%M')}" if i18n.get_current_language() == "en" else f"对话 {datetime.now().strftime('%m-%d %H:%M')}"
        st.session_state.current_conversation_id = multilingual_llm_service.conversation_manager.create_conversation(
            user_id=user_id,
            title=conversation_title
        )

    # 添加用户消息到聊天历史
    user_msg = {
        "role": "user",
        "content": message,
        "timestamp": datetime.now().strftime("%H:%M:%S")
    }
    st.session_state.chat_messages.append(user_msg)

    # 显示用户消息
    with st.chat_message("user"):
        st.markdown(message)
        st.caption(f"🕒 {user_msg['timestamp']}")

    # 获取AI回复（使用统一AI服务）
    with st.chat_message("assistant"):
        thinking_text = "🤔 AI is analyzing system data and thinking..." if i18n.get_current_language() == "en" else "🤔 AI正在分析系统数据并思考..."
        with st.spinner(thinking_text):
            # 检测是否需要AI增强功能
            ai_suggestions = detect_ai_enhancement_needs(message)

            # 使用统一AI服务处理请求
            ai_request = AIRequest(
                service_type=AIServiceType.LLM_CHAT,
                request_data={
                    "message": message,
                    "model": model,
                    "temperature": temperature,
                    "language": language
                },
                user_id=user_id,
                conversation_id=st.session_state.current_conversation_id,
                context={"ai_suggestions": ai_suggestions}
            )

            # 处理AI请求
            ai_response = await unified_ai_service.process_request(ai_request)

            # 如果统一AI服务失败，回退到原有服务
            if not ai_response.success:
                response = multilingual_llm_service.chat_with_context(
                    message=message,
                    conversation_id=st.session_state.current_conversation_id,
                    user_id=user_id,
                    model=model,
                    temperature=temperature,
                    include_data_context=True,
                    language=language
                )
            else:
                # 转换统一AI服务响应格式
                response = {
                    "success": ai_response.success,
                    "data": ai_response.data,
                    "metadata": ai_response.metadata
                }

        if response.get("success"):
            default_error = "Sorry, I cannot answer this question." if i18n.get_current_language() == "en" else "抱歉，我无法回答这个问题。"
            ai_content = response.get("data", {}).get("response", default_error)
            metadata = response.get("data", {}).get("metadata", {})

            st.markdown(ai_content)

            # 显示元数据
            timestamp = datetime.now().strftime("%H:%M:%S")
            st.caption(f"🕒 {timestamp}")

            if metadata.get("model"):
                st.caption(f"🤖 模型: {metadata['model']}")
            if metadata.get("tokens_used"):
                st.caption(f"📊 令牌使用: {metadata['tokens_used']}")
            if metadata.get("data_context_included"):
                st.caption("📊 已集成系统数据")

            # 显示生产计划生成结果
            plan_generation = metadata.get("plan_generation", {})
            if plan_generation.get("needed"):
                show_plan_generation_result(plan_generation)

            # 添加助手消息到聊天历史
            assistant_msg = {
                "role": "assistant",
                "content": ai_content,
                "timestamp": timestamp,
                "metadata": metadata
            }
            st.session_state.chat_messages.append(assistant_msg)

        else:
            if i18n.get_current_language() == "en":
                error_msg = f"❌ Failed to get AI response: {response.get('message', 'Unknown error')}"
            else:
                error_msg = f"❌ 获取AI回复失败: {response.get('message', '未知错误')}"
            st.error(error_msg)

            # 添加错误消息
            error_assistant_msg = {
                "role": "assistant",
                "content": error_msg,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            }
            st.session_state.chat_messages.append(error_assistant_msg)


def get_ai_response(message, model, temperature, max_tokens):
    """获取AI回复"""
    try:
        # 构建上下文
        context = {
            "session_id": st.session_state.current_session_id,
            "user_role": "production_manager",
            "system_context": "Smart Planning生产管理系统",
            "conversation_history": st.session_state.chat_messages[-5:] if len(st.session_state.chat_messages) > 5 else st.session_state.chat_messages
        }

        result = api_client.chat_with_llm(
            message=message,
            context=context,
            model=model,
            session_id=st.session_state.current_session_id,
            temperature=temperature,
            max_tokens=max_tokens
        )

        # 如果API调用失败，返回模拟响应
        if not result.get("success"):
            return get_mock_ai_response(message, model)

        return result

    except Exception as e:
        return get_mock_ai_response(message, model, error=str(e))


def get_mock_ai_response(message, model, error=None):
    """获取模拟AI回复"""
    if error:
        return {
            "success": True,
            "data": {
                "response": f"抱歉，由于技术原因暂时无法连接到AI服务。错误信息：{error}\n\n不过我可以为您提供一些基本的生产管理建议：\n\n1. 定期检查设备状态\n2. 优化生产计划安排\n3. 关注质量控制指标\n4. 持续改进生产流程",
                "metadata": {
                    "model": model,
                    "tokens_used": 150,
                    "response_time": 1.2,
                    "service": "mock"
                }
            }
        }

    # 基于消息内容生成简单回复
    if "设备" in message:
        response = "关于设备管理，我建议您：\n\n1. 定期进行预防性维护\n2. 监控设备运行状态\n3. 建立设备故障预警机制\n4. 优化设备利用率\n\n如需更详细的分析，请提供具体的设备数据。"
    elif "生产" in message or "规划" in message:
        response = "关于生产规划，我建议您：\n\n1. 分析历史生产数据\n2. 考虑设备产能约束\n3. 优化生产排程\n4. 平衡库存和交期\n\n请提供具体的生产需求数据以获得更精准的建议。"
    elif "质量" in message:
        response = "关于质量管理，我建议您：\n\n1. 建立质量控制体系\n2. 实施统计过程控制\n3. 分析质量问题根因\n4. 持续改进质量流程\n\n请分享具体的质量数据以获得针对性建议。"
    else:
        response = f"感谢您的问题：「{message}」\n\n作为您的生产管理AI助手，我可以帮助您：\n\n🏭 生产规划优化\n⚙️ 设备管理建议\n📊 数据分析解读\n🔧 故障诊断支持\n📈 效率提升方案\n\n请告诉我您具体需要哪方面的帮助？"

    return {
        "success": True,
        "data": {
            "response": response,
            "metadata": {
                "model": model,
                "tokens_used": len(response),
                "response_time": 0.8,
                "service": "mock"
            }
        }
    }


def show_chat_history_modal():
    """显示聊天历史模态框"""
    with st.modal("📋 对话历史"):
        st.markdown("### 历史对话记录")

        if st.session_state.chat_history:
            for i, conversation in enumerate(st.session_state.chat_history):
                with st.expander(f"📝 {conversation.get('title', f'对话 {i+1}')} - {conversation.get('updated_at', 'Unknown')}"):
                    st.write(f"**创建时间**: {conversation.get('created_at', 'Unknown')}")
                    st.write(f"**最后更新**: {conversation.get('updated_at', 'Unknown')}")
                    st.write(f"**状态**: {conversation.get('status', 'Unknown')}")

                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("📂 加载对话", key=f"load_conv_{conversation['id']}"):
                            load_conversation(conversation['id'])
                            st.session_state.show_history = False
                            st.rerun()
                    with col2:
                        if st.button("🗑️ 删除", key=f"delete_conv_{conversation['id']}"):
                            # 这里可以添加删除对话的功能
                            st.warning("删除功能开发中...")
        else:
            st.info("暂无历史对话记录")

        if st.button("关闭", use_container_width=True):
            st.session_state.show_history = False
            st.rerun()


# 数据分析模态框
def show_data_analysis_modal():
    """显示数据分析模态框"""
    with st.modal("📊 智能数据分析"):
        st.markdown("### 选择要分析的数据")

        analysis_type = st.selectbox(
            "分析类型",
            ["趋势分析", "性能分析", "瓶颈分析", "效率分析", "质量分析", "成本分析"]
        )

        data_source = st.selectbox(
            "数据源",
            ["生产数据", "设备数据", "质量数据", "库存数据", "成本数据"]
        )

        time_range = st.selectbox(
            "时间范围",
            ["最近7天", "最近30天", "最近90天", "自定义"]
        )

        if st.button("🔍 开始分析", use_container_width=True):
            # 构建分析请求
            analysis_message = f"请对{data_source}进行{analysis_type}，时间范围为{time_range}。请提供详细的分析结果、关键洞察和改进建议。"

            st.session_state.template_message = analysis_message
            st.session_state.show_data_analysis = False
            st.rerun()

        if st.button("❌ 取消", use_container_width=True):
            st.session_state.show_data_analysis = False
            st.rerun()


# 优化建议模态框
def show_optimization_modal():
    """显示优化建议模态框"""
    with st.modal("💡 智能优化建议"):
        st.markdown("### 获取优化建议")

        optimization_area = st.selectbox(
            "优化领域",
            ["生产效率", "设备利用率", "质量控制", "成本控制", "库存管理", "人员配置"]
        )

        current_situation = st.text_area(
            "当前情况描述",
            placeholder="请描述当前的情况、遇到的问题或希望改进的方面..."
        )

        objectives = st.multiselect(
            "优化目标",
            ["提高效率", "降低成本", "改善质量", "减少浪费", "提升产能", "优化流程"]
        )

        constraints = st.text_area(
            "约束条件",
            placeholder="请描述现有的约束条件，如预算限制、时间要求、技术限制等..."
        )

        if st.button("💡 获取建议", use_container_width=True):
            # 构建优化请求
            optimization_message = f"""
请为{optimization_area}提供优化建议：

**当前情况**：{current_situation}

**优化目标**：{', '.join(objectives) if objectives else '未指定'}

**约束条件**：{constraints if constraints else '无特殊约束'}

请提供具体的优化方案、实施步骤和预期效果。
"""

            st.session_state.template_message = optimization_message
            st.session_state.show_optimization = False
            st.rerun()

        if st.button("❌ 取消", use_container_width=True):
            st.session_state.show_optimization = False
            st.rerun()


# 动态模板模态框
def show_dynamic_template_modal():
    """显示动态模板模态框"""
    template_name = st.session_state.get('selected_dynamic_template', '')

    with st.modal(f"🔍 {template_name}"):
        st.markdown(f"### {template_name}")

        if template_name == "PCI性能分析":
            show_pci_analysis_template()
        elif template_name == "实时设备状态":
            show_equipment_status_template()
        elif template_name == "生产KPI分析":
            show_production_kpi_template()
        elif template_name == "库存优化分析":
            show_inventory_analysis_template()
        else:
            st.error("未知的模板类型")

        if st.button("❌ 取消", use_container_width=True):
            st.session_state.show_dynamic_template = False
            st.rerun()


def show_pci_analysis_template():
    """PCI性能分析模板"""
    st.markdown("#### 📊 PCI性能分析配置")

    # 时间范围选择
    time_range = st.selectbox(
        "分析时间范围",
        ["最近1小时", "最近24小时", "最近7天", "最近30天", "自定义"]
    )

    # PCI指标选择
    pci_metrics = st.multiselect(
        "选择PCI指标",
        ["吞吐量", "延迟", "错误率", "带宽利用率", "队列深度", "IOPS"],
        default=["吞吐量", "延迟", "错误率"]
    )

    # 设备筛选
    device_filter = st.selectbox(
        "设备筛选",
        ["全部设备", "关键设备", "异常设备", "自定义选择"]
    )

    # 分析类型
    analysis_type = st.selectbox(
        "分析类型",
        ["性能趋势分析", "异常检测", "瓶颈识别", "对比分析", "预测分析"]
    )

    if st.button("🚀 开始PCI分析", use_container_width=True):
        with st.spinner("正在获取PCI数据..."):
            # 获取PCI数据
            pci_data = fetch_pci_data(time_range, pci_metrics, device_filter)

            if pci_data:
                # 构建分析消息
                analysis_message = generate_pci_analysis_message(
                    pci_data, analysis_type, pci_metrics, time_range
                )

                st.session_state.template_message = analysis_message
                st.session_state.show_dynamic_template = False
                st.success("✅ PCI数据获取成功，正在生成分析...")
                st.rerun()
            else:
                st.error("❌ 获取PCI数据失败")


def show_equipment_status_template():
    """实时设备状态模板"""
    st.markdown("#### ⚙️ 实时设备状态分析")

    equipment_types = st.multiselect(
        "设备类型",
        ["数控机床", "装配线", "检测设备", "物流设备", "辅助设备"],
        default=["数控机床", "装配线"]
    )

    status_metrics = st.multiselect(
        "状态指标",
        ["运行状态", "利用率", "温度", "振动", "功耗", "故障代码"],
        default=["运行状态", "利用率", "温度"]
    )

    alert_level = st.selectbox(
        "告警级别",
        ["全部", "仅异常", "仅告警", "仅故障"]
    )

    if st.button("📡 获取设备状态", use_container_width=True):
        with st.spinner("正在获取设备状态数据..."):
            equipment_data = fetch_equipment_status_data(equipment_types, status_metrics, alert_level)

            if equipment_data:
                analysis_message = generate_equipment_status_message(equipment_data, status_metrics)
                st.session_state.template_message = analysis_message
                st.session_state.show_dynamic_template = False
                st.success("✅ 设备状态数据获取成功")
                st.rerun()
            else:
                st.error("❌ 获取设备状态数据失败")


def show_production_kpi_template():
    """生产KPI分析模板"""
    st.markdown("#### 📈 生产KPI分析")

    kpi_categories = st.multiselect(
        "KPI类别",
        ["效率指标", "质量指标", "成本指标", "交付指标", "安全指标"],
        default=["效率指标", "质量指标"]
    )

    comparison_type = st.selectbox(
        "对比类型",
        ["同期对比", "环比对比", "目标对比", "行业对比"]
    )

    time_period = st.selectbox(
        "统计周期",
        ["日报", "周报", "月报", "季报"]
    )

    if st.button("📊 生成KPI分析", use_container_width=True):
        with st.spinner("正在获取KPI数据..."):
            kpi_data = fetch_production_kpi_data(kpi_categories, comparison_type, time_period)

            if kpi_data:
                analysis_message = generate_kpi_analysis_message(kpi_data, kpi_categories, comparison_type)
                st.session_state.template_message = analysis_message
                st.session_state.show_dynamic_template = False
                st.success("✅ KPI数据获取成功")
                st.rerun()
            else:
                st.error("❌ 获取KPI数据失败")


def show_inventory_analysis_template():
    """库存优化分析模板"""
    st.markdown("#### 📦 库存优化分析")

    inventory_types = st.multiselect(
        "库存类型",
        ["原材料", "半成品", "成品", "备件", "工具"],
        default=["原材料", "成品"]
    )

    analysis_focus = st.selectbox(
        "分析重点",
        ["库存周转", "安全库存", "缺货风险", "过期风险", "成本优化"]
    )

    optimization_goal = st.selectbox(
        "优化目标",
        ["降低库存成本", "提高周转率", "减少缺货", "优化存储空间"]
    )

    if st.button("📦 开始库存分析", use_container_width=True):
        with st.spinner("正在获取库存数据..."):
            inventory_data = fetch_inventory_data(inventory_types, analysis_focus)

            if inventory_data:
                analysis_message = generate_inventory_analysis_message(
                    inventory_data, analysis_focus, optimization_goal
                )
                st.session_state.template_message = analysis_message
                st.session_state.show_dynamic_template = False
                st.success("✅ 库存数据获取成功")
                st.rerun()
            else:
                st.error("❌ 获取库存数据失败")


# 数据获取函数
def fetch_pci_data(time_range, metrics, device_filter):
    """获取PCI性能数据"""
    try:
        # 这里可以调用实际的API获取PCI数据
        # 示例：result = api_client.get_pci_performance_data(time_range, metrics, device_filter)

        # 模拟PCI数据
        mock_data = {
            "time_range": time_range,
            "metrics": metrics,
            "device_filter": device_filter,
            "data": {
                "吞吐量": {"current": 850, "average": 780, "peak": 950, "unit": "MB/s"},
                "延迟": {"current": 2.3, "average": 2.8, "peak": 5.1, "unit": "ms"},
                "错误率": {"current": 0.02, "average": 0.05, "peak": 0.15, "unit": "%"},
                "带宽利用率": {"current": 75, "average": 68, "peak": 92, "unit": "%"},
                "队列深度": {"current": 32, "average": 28, "peak": 64, "unit": "requests"},
                "IOPS": {"current": 15000, "average": 12500, "peak": 18000, "unit": "ops/s"}
            },
            "devices": [
                {"name": "PCI-Device-01", "status": "正常", "performance": "优秀"},
                {"name": "PCI-Device-02", "status": "警告", "performance": "良好"},
                {"name": "PCI-Device-03", "status": "正常", "performance": "优秀"}
            ],
            "alerts": [
                {"level": "警告", "message": "PCI-Device-02延迟偏高", "time": "10:30"},
                {"level": "信息", "message": "PCI-Device-01性能优化完成", "time": "09:15"}
            ]
        }
        return mock_data
    except Exception as e:
        st.error(f"获取PCI数据失败: {str(e)}")
        return None


def fetch_equipment_status_data(equipment_types, status_metrics, alert_level):
    """获取设备状态数据"""
    try:
        # 模拟设备状态数据
        mock_data = {
            "equipment_types": equipment_types,
            "status_metrics": status_metrics,
            "alert_level": alert_level,
            "equipment_list": [
                {
                    "name": "数控机床-01",
                    "type": "数控机床",
                    "status": "运行中",
                    "utilization": 85.2,
                    "temperature": 45.6,
                    "vibration": 0.8,
                    "power_consumption": 12.5,
                    "alerts": []
                },
                {
                    "name": "装配线-A",
                    "type": "装配线",
                    "status": "运行中",
                    "utilization": 92.1,
                    "temperature": 38.2,
                    "vibration": 0.3,
                    "power_consumption": 25.8,
                    "alerts": ["温度偏高"]
                },
                {
                    "name": "检测设备-03",
                    "type": "检测设备",
                    "status": "维护中",
                    "utilization": 0.0,
                    "temperature": 25.0,
                    "vibration": 0.0,
                    "power_consumption": 2.1,
                    "alerts": ["计划维护"]
                }
            ],
            "summary": {
                "total_equipment": 15,
                "running": 12,
                "maintenance": 2,
                "fault": 1,
                "average_utilization": 78.5
            }
        }
        return mock_data
    except Exception as e:
        st.error(f"获取设备状态数据失败: {str(e)}")
        return None


def fetch_production_kpi_data(kpi_categories, comparison_type, time_period):
    """获取生产KPI数据"""
    try:
        # 模拟KPI数据
        mock_data = {
            "kpi_categories": kpi_categories,
            "comparison_type": comparison_type,
            "time_period": time_period,
            "kpi_data": {
                "效率指标": {
                    "设备利用率": {"current": 78.5, "target": 80.0, "last_period": 75.2},
                    "生产效率": {"current": 92.3, "target": 90.0, "last_period": 89.1},
                    "OEE": {"current": 72.4, "target": 75.0, "last_period": 70.8}
                },
                "质量指标": {
                    "合格率": {"current": 98.7, "target": 99.0, "last_period": 98.2},
                    "返工率": {"current": 1.2, "target": 1.0, "last_period": 1.5},
                    "客户投诉": {"current": 3, "target": 2, "last_period": 5}
                },
                "成本指标": {
                    "单位成本": {"current": 125.6, "target": 120.0, "last_period": 128.3},
                    "材料利用率": {"current": 94.2, "target": 95.0, "last_period": 93.1},
                    "能耗成本": {"current": 15.8, "target": 15.0, "last_period": 16.2}
                }
            },
            "trends": {
                "improving": ["生产效率", "合格率", "材料利用率"],
                "declining": ["返工率", "客户投诉"],
                "stable": ["设备利用率", "OEE"]
            }
        }
        return mock_data
    except Exception as e:
        st.error(f"获取KPI数据失败: {str(e)}")
        return None


def fetch_inventory_data(inventory_types, analysis_focus):
    """获取库存数据"""
    try:
        # 模拟库存数据
        mock_data = {
            "inventory_types": inventory_types,
            "analysis_focus": analysis_focus,
            "inventory_summary": {
                "total_value": 2850000,
                "total_items": 1250,
                "turnover_rate": 6.2,
                "stockout_risk": "中等"
            },
            "category_data": {
                "原材料": {
                    "value": 1200000,
                    "items": 450,
                    "turnover": 8.5,
                    "safety_stock": 85,
                    "current_stock": 380
                },
                "半成品": {
                    "value": 650000,
                    "items": 280,
                    "turnover": 5.2,
                    "safety_stock": 45,
                    "current_stock": 220
                },
                "成品": {
                    "value": 800000,
                    "items": 320,
                    "turnover": 4.8,
                    "safety_stock": 60,
                    "current_stock": 285
                }
            },
            "alerts": [
                {"type": "缺货风险", "item": "钢材-A型", "level": "高"},
                {"type": "过期风险", "item": "润滑油-B", "level": "中"},
                {"type": "库存积压", "item": "配件-C123", "level": "低"}
            ]
        }
        return mock_data
    except Exception as e:
        st.error(f"获取库存数据失败: {str(e)}")
        return None


# 消息生成函数
def generate_pci_analysis_message(pci_data, analysis_type, metrics, time_range):
    """生成PCI分析消息"""
    data_summary = []
    for metric in metrics:
        if metric in pci_data["data"]:
            metric_data = pci_data["data"][metric]
            data_summary.append(f"- {metric}: 当前值 {metric_data['current']}{metric_data['unit']}, 平均值 {metric_data['average']}{metric_data['unit']}")

    devices_info = []
    for device in pci_data["devices"]:
        devices_info.append(f"- {device['name']}: {device['status']} (性能: {device['performance']})")

    alerts_info = []
    for alert in pci_data["alerts"]:
        alerts_info.append(f"- [{alert['level']}] {alert['message']} ({alert['time']})")

    message = f"""
请对以下PCI性能数据进行{analysis_type}（时间范围：{time_range}）：

**性能指标数据**：
{chr(10).join(data_summary)}

**设备状态**：
{chr(10).join(devices_info)}

**告警信息**：
{chr(10).join(alerts_info) if alerts_info else "无告警"}

请提供详细的分析结果，包括：
1. 性能趋势分析
2. 潜在问题识别
3. 优化建议
4. 预防措施
"""
    return message


def generate_equipment_status_message(equipment_data, status_metrics):
    """生成设备状态分析消息"""
    equipment_info = []
    for eq in equipment_data["equipment_list"]:
        alerts_str = f" (告警: {', '.join(eq['alerts'])})" if eq['alerts'] else ""
        equipment_info.append(f"- {eq['name']} ({eq['type']}): {eq['status']}, 利用率 {eq['utilization']}%{alerts_str}")

    summary = equipment_data["summary"]

    message = f"""
请分析以下实时设备状态数据：

**设备概况**：
- 设备总数: {summary['total_equipment']}
- 运行中: {summary['running']}
- 维护中: {summary['maintenance']}
- 故障: {summary['fault']}
- 平均利用率: {summary['average_utilization']}%

**设备详情**：
{chr(10).join(equipment_info)}

请提供：
1. 设备运行状态评估
2. 异常设备分析
3. 利用率优化建议
4. 维护计划建议
"""
    return message


def generate_kpi_analysis_message(kpi_data, kpi_categories, comparison_type):
    """生成KPI分析消息"""
    kpi_info = []
    for category in kpi_categories:
        if category in kpi_data["kpi_data"]:
            kpi_info.append(f"\n**{category}**:")
            for kpi_name, values in kpi_data["kpi_data"][category].items():
                kpi_info.append(f"- {kpi_name}: 当前 {values['current']}, 目标 {values['target']}, 上期 {values['last_period']}")

    trends = kpi_data["trends"]

    message = f"""
请分析以下生产KPI数据（{comparison_type}）：

{chr(10).join(kpi_info)}

**趋势分析**：
- 改善指标: {', '.join(trends['improving'])}
- 下降指标: {', '.join(trends['declining'])}
- 稳定指标: {', '.join(trends['stable'])}

请提供：
1. KPI达成情况分析
2. 趋势变化原因分析
3. 改进措施建议
4. 目标调整建议
"""
    return message


def generate_inventory_analysis_message(inventory_data, analysis_focus, optimization_goal):
    """生成库存分析消息"""
    summary = inventory_data["inventory_summary"]
    category_info = []

    for category, data in inventory_data["category_data"].items():
        category_info.append(f"- {category}: 价值 {data['value']:,}元, 周转率 {data['turnover']}, 库存 {data['current_stock']}/{data['safety_stock']} (当前/安全)")

    alerts_info = []
    for alert in inventory_data["alerts"]:
        alerts_info.append(f"- [{alert['level']}风险] {alert['type']}: {alert['item']}")

    message = f"""
请分析以下库存数据（分析重点：{analysis_focus}，优化目标：{optimization_goal}）：

**库存概况**：
- 总价值: {summary['total_value']:,}元
- 总品项: {summary['total_items']}
- 周转率: {summary['turnover_rate']}
- 缺货风险: {summary['stockout_risk']}

**分类详情**：
{chr(10).join(category_info)}

**风险告警**：
{chr(10).join(alerts_info)}

请提供：
1. 库存结构分析
2. 风险评估
3. 优化建议
4. 实施方案
"""
    return message


# AI增强功能检测
def detect_ai_enhancement_needs(message: str) -> List[Dict]:
    """检测消息中的AI增强功能需求"""
    suggestions = []
    message_lower = message.lower()

    # 预测分析需求
    if any(keyword in message_lower for keyword in ["预测", "forecast", "predict", "趋势", "trend"]):
        suggestions.append({
            "service": "predictive_analytics",
            "reason": "检测到预测分析需求",
            "confidence": 0.8
        })

    # 异常检测需求
    if any(keyword in message_lower for keyword in ["异常", "anomaly", "故障", "问题", "错误"]):
        suggestions.append({
            "service": "anomaly_detection",
            "reason": "检测到异常检测需求",
            "confidence": 0.7
        })

    # 优化需求
    if any(keyword in message_lower for keyword in ["优化", "optimize", "改进", "提升", "效率"]):
        suggestions.append({
            "service": "intelligent_optimization",
            "reason": "检测到优化需求",
            "confidence": 0.9
        })

    return suggestions


# AI增强功能模态框
def show_ai_prediction_modal():
    """显示AI预测分析模态框"""
    with st.modal("🔮 AI预测分析"):
        st.markdown("### 智能预测分析")

        prediction_type = st.selectbox(
            "预测类型",
            ["需求预测", "设备故障预测", "质量预测", "产能预测", "能耗预测"]
        )

        forecast_horizon = st.slider("预测周期（天）", 1, 90, 30)

        input_data = st.text_area(
            "输入数据（JSON格式）",
            placeholder='{"base_demand": 100, "historical_data": "30天历史数据"}'
        )

        if st.button("🚀 开始预测", use_container_width=True):
            try:
                import json
                data = json.loads(input_data) if input_data else {}

                # 使用统一AI服务进行预测
                ai_request = AIRequest(
                    service_type=AIServiceType.PREDICTIVE_ANALYTICS,
                    request_data={
                        "prediction_type": prediction_type,
                        "input_data": data,
                        "forecast_horizon": forecast_horizon
                    },
                    user_id=st.session_state.get("user_id", "anonymous")
                )

                with st.spinner("正在执行预测分析..."):
                    # 这里应该是异步调用，但在Streamlit中需要特殊处理
                    st.success("✅ 预测分析已启动，结果将在对话中显示")

                    # 添加预测请求到对话
                    prediction_message = f"请执行{prediction_type}，预测周期{forecast_horizon}天，输入数据：{data}"
                    st.session_state.template_message = prediction_message
                    st.session_state.show_ai_prediction = False
                    st.rerun()

            except json.JSONDecodeError:
                st.error("❌ 输入数据格式错误，请使用有效的JSON格式")
            except Exception as e:
                st.error(f"❌ 预测分析失败: {str(e)}")

        if st.button("❌ 取消", use_container_width=True):
            st.session_state.show_ai_prediction = False
            st.rerun()


def show_ai_anomaly_modal():
    """显示AI异常检测模态框"""
    with st.modal("🔍 AI异常检测"):
        st.markdown("### 智能异常检测")

        detection_type = st.selectbox(
            "检测类型",
            ["统计异常检测", "孤立森林检测", "LSTM自编码器", "集成检测"]
        )

        sensitivity = st.slider("检测灵敏度", 0.1, 1.0, 0.7, 0.1)

        data_source = st.selectbox(
            "数据源",
            ["生产监控数据", "设备运行数据", "质量检测数据", "上传数据文件"]
        )

        if data_source == "上传数据文件":
            uploaded_file = st.file_uploader("上传CSV文件", type=['csv'])
            if uploaded_file:
                try:
                    import pandas as pd
                    df = pd.read_csv(uploaded_file)
                    st.write("数据预览：")
                    st.dataframe(df.head())

                    if st.button("🔍 执行异常检测", use_container_width=True):
                        # 转换数据格式
                        data_list = df.to_dict('records')

                        anomaly_message = f"请对上传的数据执行{detection_type}，检测灵敏度为{sensitivity}，数据包含{len(data_list)}条记录"
                        st.session_state.template_message = anomaly_message
                        st.session_state.show_ai_anomaly = False
                        st.rerun()

                except Exception as e:
                    st.error(f"❌ 文件处理失败: {str(e)}")
        else:
            if st.button("🔍 执行异常检测", use_container_width=True):
                anomaly_message = f"请对{data_source}执行{detection_type}，检测灵敏度为{sensitivity}"
                st.session_state.template_message = anomaly_message
                st.session_state.show_ai_anomaly = False
                st.rerun()

        if st.button("❌ 取消", use_container_width=True):
            st.session_state.show_ai_anomaly = False
            st.rerun()


def show_ai_optimization_modal():
    """显示AI智能优化模态框"""
    with st.modal("⚡ AI智能优化"):
        st.markdown("### 智能优化分析")

        optimization_type = st.selectbox(
            "优化类型",
            ["生产排程优化", "资源分配优化", "库存优化", "能耗优化"]
        )

        objective = st.selectbox(
            "优化目标",
            ["最小化成本", "最大化效率", "最小化完工时间", "最大化利用率"]
        )

        constraints = st.text_area(
            "约束条件",
            placeholder="请描述优化约束条件，如设备产能限制、时间窗口、资源限制等..."
        )

        input_data = st.text_area(
            "输入数据（JSON格式）",
            placeholder='{"orders": [...], "equipment": {...}, "resources": {...}}'
        )

        if st.button("⚡ 开始优化", use_container_width=True):
            try:
                import json
                data = json.loads(input_data) if input_data else {}

                optimization_message = f"""请执行{optimization_type}：

优化目标：{objective}
约束条件：{constraints}
输入数据：{data}

请提供详细的优化方案、预期效果和实施建议。"""

                st.session_state.template_message = optimization_message
                st.session_state.show_ai_optimization = False
                st.rerun()

            except json.JSONDecodeError:
                st.error("❌ 输入数据格式错误，请使用有效的JSON格式")
            except Exception as e:
                st.error(f"❌ 优化分析失败: {str(e)}")

        if st.button("❌ 取消", use_container_width=True):
            st.session_state.show_ai_optimization = False
            st.rerun()


def show_comprehensive_ai_modal():
    """显示综合AI分析模态框"""
    with st.modal("📊 综合AI分析"):
        st.markdown("### 一键综合AI分析")
        st.info("将同时执行预测分析、异常检测和智能优化，提供全面的AI洞察")

        include_predictions = st.checkbox("包含预测分析", value=True)
        include_anomalies = st.checkbox("包含异常检测", value=True)
        include_optimizations = st.checkbox("包含智能优化", value=True)

        data_sources = st.multiselect(
            "数据源",
            ["生产数据", "设备数据", "质量数据", "库存数据", "PCI数据"],
            default=["生产数据", "设备数据"]
        )

        if st.button("🚀 开始综合分析", use_container_width=True):
            analysis_components = []
            if include_predictions:
                analysis_components.append("预测分析")
            if include_anomalies:
                analysis_components.append("异常检测")
            if include_optimizations:
                analysis_components.append("智能优化")

            comprehensive_message = f"""请执行综合AI分析：

分析组件：{', '.join(analysis_components)}
数据源：{', '.join(data_sources)}

请提供：
1. 各组件的详细分析结果
2. 跨组件的关联洞察
3. 综合优化建议
4. 实施优先级排序"""

            st.session_state.template_message = comprehensive_message
            st.session_state.show_comprehensive_ai = False
            st.rerun()

        if st.button("❌ 取消", use_container_width=True):
            st.session_state.show_comprehensive_ai = False
            st.rerun()


# 模态框处理
if st.session_state.get('show_history'):
    show_chat_history_modal()

if st.session_state.get('show_data_analysis'):
    show_data_analysis_modal()

if st.session_state.get('show_optimization'):
    show_optimization_modal()

if st.session_state.get('show_dynamic_template'):
    show_dynamic_template_modal()

# AI增强功能模态框
if st.session_state.get('show_ai_prediction'):
    show_ai_prediction_modal()

if st.session_state.get('show_ai_anomaly'):
    show_ai_anomaly_modal()

if st.session_state.get('show_ai_optimization'):
    show_ai_optimization_modal()

if st.session_state.get('show_comprehensive_ai'):
    show_comprehensive_ai_modal()

# 初始化时加载模型
if not st.session_state.available_models:
    load_available_models()
