"""
Smart Planning UI主题系统
提供简洁清新的界面主题和样式
"""

import streamlit as st
from typing import Dict, Any

class SmartPlanningTheme:
    """Smart Planning主题管理器"""

    # 清新柔和的渐变配色方案
    COLORS = {
        # 主色调 - 清新蓝绿渐变
        "primary": "#4A90E2",
        "primary_light": "#7BB3F0",
        "primary_dark": "#357ABD",

        # 辅助色调 - 柔和绿色
        "secondary": "#50C878",
        "secondary_light": "#7ED89A",
        "secondary_dark": "#3BA55C",

        # 背景色 - 清新渐变
        "background_primary": "#FAFBFC",
        "background_secondary": "#F5F7FA",
        "background_gradient": "linear-gradient(135deg, #FAFBFC 0%, #F0F4F8 100%)",

        # 卡片和容器
        "card_background": "#FFFFFF",
        "card_shadow": "0 2px 12px rgba(74, 144, 226, 0.08)",
        "card_border": "#E8F2FF",

        # 文字颜色
        "text_primary": "#2C3E50",
        "text_secondary": "#5A6C7D",
        "text_muted": "#8B9DC3",

        # 状态色彩 - 柔和版本
        "success": "#52C41A",
        "warning": "#FAAD14",
        "error": "#FF4D4F",
        "info": "#1890FF",

        # 边框和分割线
        "border_light": "#E8F2FF",
        "border_medium": "#D1E7FF",
        "divider": "#F0F4F8"
    }

    @staticmethod
    def apply_global_styles():
        """应用全局样式"""
        st.markdown(f"""
        <style>
        /* 全局样式重置 */
        .stApp {{
            background: {SmartPlanningTheme.COLORS['background_gradient']};
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }}

        /* 隐藏Streamlit默认元素 */
        #MainMenu {{visibility: hidden;}}
        footer {{visibility: hidden;}}
        header {{visibility: hidden;}}

        /* 主容器样式 */
        .main .block-container {{
            padding-top: 2rem;
            padding-bottom: 2rem;
            max-width: 1200px;
        }}

        /* 侧边栏样式 */
        .css-1d391kg {{
            background: linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%);
            border-right: 1px solid {SmartPlanningTheme.COLORS['border_light']};
        }}

        /* 标题样式 */
        h1, h2, h3, h4, h5, h6 {{
            color: {SmartPlanningTheme.COLORS['text_primary']};
            font-weight: 600;
            margin-bottom: 1rem;
        }}

        h1 {{
            background: linear-gradient(135deg, {SmartPlanningTheme.COLORS['primary']} 0%, {SmartPlanningTheme.COLORS['secondary']} 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
        }}

        /* 卡片样式 */
        .smart-card {{
            background: {SmartPlanningTheme.COLORS['card_background']};
            border-radius: 12px;
            box-shadow: {SmartPlanningTheme.COLORS['card_shadow']};
            border: 1px solid {SmartPlanningTheme.COLORS['card_border']};
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }}

        .smart-card:hover {{
            box-shadow: 0 4px 20px rgba(74, 144, 226, 0.12);
            transform: translateY(-2px);
        }}

        /* 按钮样式 */
        .stButton > button {{
            background: linear-gradient(135deg, {SmartPlanningTheme.COLORS['primary']} 0%, {SmartPlanningTheme.COLORS['primary_light']} 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
        }}

        .stButton > button:hover {{
            background: linear-gradient(135deg, {SmartPlanningTheme.COLORS['primary_dark']} 0%, {SmartPlanningTheme.COLORS['primary']} 100%);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
            transform: translateY(-1px);
        }}

        /* 指标卡片样式 */
        .metric-card {{
            background: {SmartPlanningTheme.COLORS['card_background']};
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: {SmartPlanningTheme.COLORS['card_shadow']};
            border-left: 4px solid {SmartPlanningTheme.COLORS['primary']};
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }}

        .metric-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(74, 144, 226, 0.12);
        }}

        /* 输入框样式 */
        .stTextInput > div > div > input {{
            border-radius: 8px;
            border: 1px solid {SmartPlanningTheme.COLORS['border_medium']};
            padding: 0.75rem;
            transition: all 0.3s ease;
        }}

        .stTextInput > div > div > input:focus {{
            border-color: {SmartPlanningTheme.COLORS['primary']};
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
        }}

        /* 选择框样式 */
        .stSelectbox > div > div {{
            border-radius: 8px;
            border: 1px solid {SmartPlanningTheme.COLORS['border_medium']};
        }}

        /* 进度条样式 */
        .stProgress > div > div > div {{
            background: linear-gradient(90deg, {SmartPlanningTheme.COLORS['primary']} 0%, {SmartPlanningTheme.COLORS['secondary']} 100%);
            border-radius: 4px;
        }}

        /* 分割线样式 */
        hr {{
            border: none;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, {SmartPlanningTheme.COLORS['divider']} 50%, transparent 100%);
            margin: 2rem 0;
        }}

        /* Logo容器样式 */
        .logo-container {{
            position: absolute;
            top: 1rem;
            right: 1rem;
            z-index: 999;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }}

        .logo-container img {{
            max-height: 40px;
            width: auto;
        }}

        /* 响应式设计 */
        @media (max-width: 768px) {{
            .main .block-container {{
                padding-left: 1rem;
                padding-right: 1rem;
            }}

            .logo-container {{
                top: 0.5rem;
                right: 0.5rem;
                padding: 0.25rem;
            }}

            .logo-container img {{
                max-height: 30px;
            }}
        }}
        </style>
        """, unsafe_allow_html=True)

    @staticmethod
    def create_header_with_logo(title: str, subtitle: str = None, logo_path: str = None):
        """创建带Logo的页面头部"""
        # Logo容器（预留位置）
        if logo_path:
            st.markdown(f"""
            <div class="logo-container">
                <img src="{logo_path}" alt="Smart Planning Logo">
            </div>
            """, unsafe_allow_html=True)
        else:
            # 预留Logo位置
            st.markdown("""
            <div class="logo-container">
                <div style="width: 120px; height: 40px; background: linear-gradient(135deg, #4A90E2 0%, #50C878 100%);
                           border-radius: 6px; display: flex; align-items: center; justify-content: center;
                           color: white; font-size: 12px; font-weight: 600;">
                    LOGO位置
                </div>
            </div>
            """, unsafe_allow_html=True)

        # 页面标题
        st.markdown(f"# {title}")
        if subtitle:
            st.markdown(f"### {subtitle}")

    @staticmethod
    def create_metric_card(title: str, value: str, delta: str = None, delta_color: str = "normal"):
        """创建指标卡片"""
        delta_html = ""
        if delta:
            color_map = {
                "normal": SmartPlanningTheme.COLORS['text_secondary'],
                "positive": SmartPlanningTheme.COLORS['success'],
                "negative": SmartPlanningTheme.COLORS['error']
            }
            color = color_map.get(delta_color, SmartPlanningTheme.COLORS['text_secondary'])
            delta_html = f'<div style="color: {color}; font-size: 0.9rem; margin-top: 0.5rem;">{delta}</div>'

        st.markdown(f"""
        <div class="metric-card">
            <div style="font-size: 0.9rem; color: {SmartPlanningTheme.COLORS['text_secondary']}; margin-bottom: 0.5rem;">
                {title}
            </div>
            <div style="font-size: 2rem; font-weight: 600; color: {SmartPlanningTheme.COLORS['text_primary']};">
                {value}
            </div>
            {delta_html}
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def create_info_card(title: str, content: str, icon: str = "📊"):
        """创建信息卡片"""
        st.markdown(f"""
        <div class="smart-card">
            <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                <span style="font-size: 1.5rem; margin-right: 0.5rem;">{icon}</span>
                <h4 style="margin: 0; color: {SmartPlanningTheme.COLORS['text_primary']};">{title}</h4>
            </div>
            <div style="color: {SmartPlanningTheme.COLORS['text_secondary']}; line-height: 1.6;">
                {content}
            </div>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def create_status_badge(text: str, status: str = "default"):
        """创建状态徽章"""
        color_map = {
            "success": SmartPlanningTheme.COLORS['success'],
            "warning": SmartPlanningTheme.COLORS['warning'],
            "error": SmartPlanningTheme.COLORS['error'],
            "info": SmartPlanningTheme.COLORS['info'],
            "default": SmartPlanningTheme.COLORS['text_secondary']
        }

        color = color_map.get(status, SmartPlanningTheme.COLORS['text_secondary'])

        return f"""
        <span style="
            background: {color}20;
            color: {color};
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            border: 1px solid {color}40;
        ">{text}</span>
        """

# 全局应用主题
def apply_smart_planning_theme():
    """应用Smart Planning主题"""
    SmartPlanningTheme.apply_global_styles()
