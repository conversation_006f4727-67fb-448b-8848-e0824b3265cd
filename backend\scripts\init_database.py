#!/usr/bin/env python3
"""
初始化数据库表结构
"""

import asyncio
import sys

# 添加项目路径
sys.path.append('/app')

from app.core.database import engine, Base
from app.models import user  # 导入所有模型


async def init_database():
    """初始化数据库表结构"""
    try:
        print("🔄 开始创建数据库表...")
        
        async with engine.begin() as conn:
            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
        
        print("✅ 数据库表创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库表创建失败: {str(e)}")
        return False
    finally:
        await engine.dispose()


if __name__ == "__main__":
    success = asyncio.run(init_database())
    sys.exit(0 if success else 1)
