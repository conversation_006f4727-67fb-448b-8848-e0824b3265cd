# 🏗️ Smart Planning 前后端分离架构详解

## 📋 架构概述

Smart Planning采用完全的前后端分离架构，前端使用Streamlit构建用户界面，后端使用FastAPI提供RESTful API服务。所有数据交互都通过HTTP API进行。

## 🔄 数据流转机制

### 1. Excel文件上传流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端(Streamlit)
    participant API as API客户端
    participant Backend as 后端(FastAPI)
    participant DB as 数据库(MySQL)
    participant FileService as 文件服务
    
    User->>Frontend: 选择Excel文件
    Frontend->>API: upload_file()
    API->>Backend: POST /api/v1/upload/file
    Backend->>FileService: 验证文件类型和大小
    Backend->>DB: 创建文件记录
    Backend->>FileService: 解析Excel数据
    FileService->>DB: 存储提取的数据
    Backend->>API: 返回上传结果
    API->>Frontend: 显示上传状态
    Frontend->>User: 显示成功/失败消息
```

### 2. LLM对话流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant API as API客户端
    participant Backend as 后端
    participant LLMService as LLM服务
    participant DataService as 数据服务
    participant DB as 数据库
    
    User->>Frontend: 输入问题
    Frontend->>API: chat_with_llm()
    API->>Backend: POST /api/v1/llm/chat
    Backend->>DataService: 获取数据上下文
    DataService->>DB: 查询相关数据
    Backend->>LLMService: 调用LLM推理
    LLMService->>Backend: 返回AI响应
    Backend->>DB: 保存对话记录
    Backend->>API: 返回响应结果
    API->>Frontend: 显示AI回答
    Frontend->>User: 展示智能建议
```

### 3. 算法执行流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant API as API客户端
    participant Backend as 后端
    participant AlgorithmService as 算法服务
    participant DB as 数据库
    
    User->>Frontend: 选择算法和参数
    Frontend->>API: execute_planning_algorithm()
    API->>Backend: POST /api/v1/production-plans/{id}/execute
    Backend->>DB: 获取生产数据
    Backend->>AlgorithmService: 执行优化算法
    AlgorithmService->>DB: 保存算法结果
    Backend->>API: 返回执行结果
    API->>Frontend: 更新图表和数据
    Frontend->>User: 显示优化结果
```

## 🔧 核心组件详解

### 前端架构 (Streamlit)

#### API客户端 (`frontend/utils/api_client.py`)
- **功能**: 封装所有后端API调用
- **认证**: JWT Token自动管理
- **错误处理**: 统一的错误处理和重试机制
- **会话管理**: 自动处理token过期和刷新

<augment_code_snippet path="frontend/utils/api_client.py" mode="EXCERPT">
````python
class APIClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.timeout = AppConfig.API_TIMEOUT

    def upload_file(self, file_data: bytes, filename: str, file_type: str):
        """上传文件到后端"""
        url = f"{self.base_url}/api/v1/upload/file"
        files = {'file': (filename, file_data, file_type)}
        headers = {"Authorization": f"Bearer {st.session_state.access_token}"}
        response = self.session.post(url, files=files, headers=headers)
        return self._handle_response(response)
````
</augment_code_snippet>

#### 页面组件
每个Streamlit页面都通过API客户端与后端交互：

<augment_code_snippet path="frontend/pages/02_数据上传.py" mode="EXCERPT">
````python
def upload_files(files, description, auto_process, processing_options):
    """上传文件"""
    api_client = st.session_state.get('api_client')
    
    for file in files:
        # 调用API上传文件
        result = api_client.upload_file(
            file_data=file.read(),
            filename=file.name,
            file_type=file.type
        )
        
        if result.get('success'):
            st.success(f"✅ {file.name} 上传成功")
        else:
            st.error(f"❌ {file.name} 上传失败")
````
</augment_code_snippet>

### 后端架构 (FastAPI)

#### API路由结构
```
/api/v1/
├── auth/          # 认证相关
├── upload/        # 文件上传
├── production-plans/  # 生产计划
├── equipment/     # 设备管理
├── llm/          # LLM服务
├── database/     # 数据库查询
└── system/       # 系统管理
```

#### 文件上传API (`backend/app/api/v1/endpoints/file_upload.py`)

<augment_code_snippet path="backend/app/api/v1/endpoints/file_upload.py" mode="EXCERPT">
````python
@router.post("/file", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    auto_process: bool = Form(True),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    # 1. 验证文件类型和大小
    # 2. 保存文件到磁盘
    # 3. 创建数据库记录
    # 4. 如果启用自动处理，添加到处理队列
    # 5. 返回处理结果
````
</augment_code_snippet>

#### LLM服务API (`backend/app/api/v1/endpoints/llm_chat.py`)

<augment_code_snippet path="backend/app/api/v1/endpoints/llm_chat.py" mode="EXCERPT">
````python
@router.post("/chat", response_model=ChatResponse)
async def chat_with_llm(
    chat_request: ChatRequest,
    current_user: dict = Depends(require_permission("llm.chat")),
    db: AsyncSession = Depends(get_db)
):
    llm_service = LLMService(db)
    
    # 执行聊天，包含数据上下文
    response = await llm_service.chat(
        message=chat_request.message,
        context=chat_request.context,
        model=chat_request.model,
        user_id=current_user["id"]
    )
    
    return response
````
</augment_code_snippet>

## 📊 数据集成机制

### 1. 数据上下文获取
LLM和算法在执行时会自动获取系统中的所有相关数据：

```python
# 数据集成服务自动收集：
- 上传的Excel/CSV文件数据
- 用户手动输入的数据
- 设备状态和维护记录
- PCI库存数据
- 历史对话记录
- 算法执行结果
```

### 2. 智能数据查询
前端LLM服务会根据用户问题智能查询相关数据：

<augment_code_snippet path="frontend/services/llm_service.py" mode="EXCERPT">
````python
def chat_with_context(self, message: str, conversation_id: str, user_id: str):
    # 获取数据上下文
    data_context = self.data_integration.get_comprehensive_data_context()
    
    # 构建包含数据的LLM提示
    llm_prompt = self._build_llm_prompt(message, conversation_history, data_context)
    
    # 调用LLM进行推理
    llm_response = self._call_llm(llm_prompt, model, temperature)
````
</augment_code_snippet>

### 3. 自动数据更新
当用户通过对话或操作产生新数据时，系统会自动更新相关图表和页面：

```python
# 数据更新触发机制：
1. 文件上传完成 → 更新数据预览页面
2. LLM生成建议 → 更新相关图表
3. 算法执行完成 → 更新生产计划页面
4. 设备状态变更 → 更新设备监控页面
```

## 🔐 安全机制

### 1. 认证授权
- JWT Token认证
- 基于角色的权限控制
- API访问权限验证

### 2. 数据安全
- 文件类型和大小验证
- SQL注入防护
- 敏感数据加密存储

### 3. 会话管理
- 自动token刷新
- 会话超时处理
- 多设备登录控制

## 🚀 性能优化

### 1. 异步处理
- 文件上传异步处理
- 算法执行后台运行
- 数据库异步操作

### 2. 缓存机制
- Redis缓存热点数据
- API响应缓存
- 静态资源缓存

### 3. 数据库优化
- 索引优化
- 查询优化
- 连接池管理

## 📈 扩展性设计

### 1. 微服务架构
- 独立的LLM服务
- 独立的算法服务
- 独立的文件处理服务

### 2. 插件系统
- 可扩展的算法插件
- 可配置的数据源
- 可定制的模板系统

### 3. 多数据库支持
- MySQL主数据库
- 可配置的外部数据库
- 多数据源集成

## 🔧 部署架构

```
Docker Compose 服务编排:
├── frontend (Streamlit)     # 端口: 8501
├── backend (FastAPI)       # 端口: 8000
├── mysql (MySQL 8.0)       # 端口: 3306
├── redis (Redis 7.0)       # 端口: 6379
└── ollama (可选)           # 端口: 11434
```

这种架构确保了系统的高可用性、可扩展性和维护性，同时提供了良好的用户体验和开发体验。
