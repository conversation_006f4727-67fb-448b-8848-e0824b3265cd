"""
生产计划图表组件 - 可扩展架构
可以集成到主页面或其他页面中使用
支持动态注册新的图表类型
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Callable, Any, List
import importlib
import importlib.util
import os


# 图表注册系统
class ChartRegistry:
    """图表注册中心 - 支持动态扩展图表类型"""

    def __init__(self):
        self._charts: Dict[str, Dict[str, Any]] = {}
        self._register_builtin_charts()

    def register_chart(self, chart_id: str, chart_name: str, render_func: Callable,
                      description: str = "", category: str = "生产分析"):
        """
        注册新的图表类型

        Args:
            chart_id: 图表唯一标识
            chart_name: 图表显示名称
            render_func: 渲染函数
            description: 图表描述
            category: 图表分类
        """
        self._charts[chart_id] = {
            "name": chart_name,
            "render_func": render_func,
            "description": description,
            "category": category
        }

    def get_chart_names(self, category: str = None) -> List[str]:
        """获取图表名称列表"""
        if category:
            return [info["name"] for info in self._charts.values()
                   if info["category"] == category]
        return [info["name"] for info in self._charts.values()]

    def get_chart_by_name(self, chart_name: str) -> Dict[str, Any]:
        """根据名称获取图表信息"""
        for chart_id, info in self._charts.items():
            if info["name"] == chart_name:
                return {"id": chart_id, **info}
        return None

    def render_chart(self, chart_name: str, data: Any, height: int = 400, **kwargs):
        """渲染指定图表"""
        chart_info = self.get_chart_by_name(chart_name)
        if chart_info:
            return chart_info["render_func"](data, height, **kwargs)
        else:
            st.error(f"未找到图表类型: {chart_name}")

    def _register_builtin_charts(self):
        """注册内置图表类型"""
        self.register_chart("gantt", "甘特图", render_compact_gantt_chart,
                           "显示任务时间线和资源分配", "时间分析")
        self.register_chart("timeline", "时间线图", render_compact_timeline_chart,
                           "散点时间线分布", "时间分析")
        self.register_chart("workload", "负载图", render_compact_workload_chart,
                           "设备负载率分析", "资源分析")
        self.register_chart("resource", "资源分配图", render_compact_resource_chart,
                           "资源分配比例", "资源分析")
        self.register_chart("progress", "进度仪表板", render_compact_progress_dashboard,
                           "进度和关键指标", "进度监控")

    def load_custom_charts(self, charts_dir: str = "custom_charts"):
        """从指定目录加载自定义图表"""
        if not os.path.exists(charts_dir):
            return

        for filename in os.listdir(charts_dir):
            if filename.endswith('.py') and not filename.startswith('__'):
                module_name = filename[:-3]
                try:
                    spec = importlib.util.spec_from_file_location(
                        module_name, os.path.join(charts_dir, filename)
                    )
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    # 查找注册函数
                    if hasattr(module, 'register_charts'):
                        module.register_charts(self)

                except Exception as e:
                    st.warning(f"加载自定义图表 {filename} 失败: {e}")


# 全局图表注册中心
chart_registry = ChartRegistry()


def render_production_chart_widget(chart_type="甘特图", height=400, show_controls=True, category=None):
    """
    渲染生产计划图表组件 - 支持扩展图表类型

    Args:
        chart_type: 图表类型
        height: 图表高度
        show_controls: 是否显示控制选项
        category: 图表分类过滤
    """

    # 加载自定义图表
    chart_registry.load_custom_charts()

    # 获取可用图表
    available_charts = chart_registry.get_chart_names(category)

    # 获取数据
    plans_data = get_mock_production_plans()

    if show_controls:
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            selected_chart = st.selectbox(
                "图表类型",
                options=available_charts,
                index=available_charts.index(chart_type) if chart_type in available_charts else 0,
                key=f"chart_type_{id(plans_data)}"
            )

        with col2:
            if st.button("🔄 刷新数据", key=f"refresh_{id(plans_data)}"):
                st.rerun()

        with col3:
            if st.button("➕ 扩展图表", key=f"extend_{id(plans_data)}"):
                show_chart_extension_guide()
    else:
        selected_chart = chart_type

    # 使用注册中心渲染图表
    chart_registry.render_chart(selected_chart, plans_data, height)


def show_chart_extension_guide():
    """显示图表扩展指南"""
    with st.expander("📊 图表扩展指南", expanded=True):
        st.markdown("""
        ### 🚀 如何扩展新的图表类型

        #### 1. 创建自定义图表文件
        在 `custom_charts/` 目录下创建 Python 文件，例如 `my_custom_chart.py`

        #### 2. 实现图表渲染函数
        ```python
        import streamlit as st
        import plotly.express as px

        def render_my_custom_chart(data, height=400, **kwargs):
            # 你的图表逻辑
            fig = px.scatter(...)
            st.plotly_chart(fig, use_container_width=True)

        def register_charts(registry):
            registry.register_chart(
                chart_id="my_custom",
                chart_name="我的自定义图表",
                render_func=render_my_custom_chart,
                description="自定义图表描述",
                category="自定义分析"
            )
        ```

        #### 3. 支持的图表库
        - **Plotly**: 交互式图表
        - **Matplotlib**: 静态图表
        - **Altair**: 声明式可视化
        - **Bokeh**: 高性能交互图表

        #### 4. 数据格式
        图表函数接收的 `data` 参数格式：
        ```python
        [
            {
                "product_name": "产品名称",
                "order_id": "订单ID",
                "start_time": "开始时间",
                "end_time": "结束时间",
                "equipment_name": "设备名称",
                "quantity": 数量,
                "status": "状态"
            }
        ]
        ```
        """)

        # 示例代码下载
        if st.button("📥 下载示例代码"):
            create_chart_example_file()


def create_chart_example_file():
    """创建图表扩展示例文件"""
    example_code = '''"""
自定义图表示例 - 3D散点图
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime

def render_3d_scatter_chart(data, height=400, **kwargs):
    """渲染3D散点图"""

    if not data:
        st.info("暂无数据")
        return

    # 数据处理
    df = pd.DataFrame(data)
    df['start_time'] = pd.to_datetime(df['start_time'])
    df['duration'] = (pd.to_datetime(df['end_time']) - df['start_time']).dt.total_seconds() / 3600

    # 创建3D散点图
    fig = px.scatter_3d(
        df,
        x='quantity',
        y='duration',
        z='start_time',
        color='equipment_name',
        size='quantity',
        hover_data=['product_name', 'order_id'],
        title="生产计划3D分析",
        labels={
            'quantity': '产量',
            'duration': '工期(小时)',
            'start_time': '开始时间'
        }
    )

    fig.update_layout(height=height)
    st.plotly_chart(fig, use_container_width=True)

    # 统计信息
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("平均产量", f"{df['quantity'].mean():.0f}")
    with col2:
        st.metric("平均工期", f"{df['duration'].mean():.1f}小时")
    with col3:
        st.metric("设备数量", df['equipment_name'].nunique())

def render_heatmap_chart(data, height=400, **kwargs):
    """渲染热力图"""

    if not data:
        st.info("暂无数据")
        return

    # 创建设备-时间热力图
    df = pd.DataFrame(data)
    df['start_time'] = pd.to_datetime(df['start_time'])
    df['hour'] = df['start_time'].dt.hour
    df['date'] = df['start_time'].dt.date

    # 聚合数据
    heatmap_data = df.groupby(['equipment_name', 'hour'])['quantity'].sum().reset_index()
    heatmap_pivot = heatmap_data.pivot(index='equipment_name', columns='hour', values='quantity').fillna(0)

    # 创建热力图
    fig = px.imshow(
        heatmap_pivot,
        title="设备-时间热力图",
        labels={'x': '小时', 'y': '设备', 'color': '产量'},
        color_continuous_scale='Viridis'
    )

    fig.update_layout(height=height)
    st.plotly_chart(fig, use_container_width=True)

def register_charts(registry):
    """注册自定义图表"""
    registry.register_chart(
        chart_id="3d_scatter",
        chart_name="3D散点图",
        render_func=render_3d_scatter_chart,
        description="三维散点分析，展示产量、工期、时间关系",
        category="高级分析"
    )

    registry.register_chart(
        chart_id="heatmap",
        chart_name="热力图",
        render_func=render_heatmap_chart,
        description="设备-时间热力图，显示负载分布",
        category="高级分析"
    )
'''

    # 确保目录存在
    os.makedirs("custom_charts", exist_ok=True)

    # 写入示例文件
    with open("custom_charts/example_charts.py", "w", encoding="utf-8") as f:
        f.write(example_code)

    st.success("✅ 示例文件已创建: custom_charts/example_charts.py")
    st.info("💡 重新加载页面即可看到新的图表类型")


def render_compact_gantt_chart(plans, height=400):
    """渲染紧凑版甘特图"""

    # 创建甘特图数据
    gantt_data = []
    for plan in plans:
        gantt_data.append({
            "Task": f"{plan['product_name']}",
            "Start": plan["start_time"],
            "Finish": plan["end_time"],
            "Resource": plan["equipment_name"],
            "Status": plan.get("status", "计划中")
        })

    if gantt_data:
        df_gantt = pd.DataFrame(gantt_data)
        df_gantt['Start'] = pd.to_datetime(df_gantt['Start'])
        df_gantt['Finish'] = pd.to_datetime(df_gantt['Finish'])

        fig = px.timeline(
            df_gantt,
            x_start="Start",
            x_end="Finish",
            y="Resource",
            color="Status",
            title="生产计划甘特图",
            color_discrete_map={
                "已完成": "#28a745",
                "进行中": "#ffc107",
                "待开始": "#6c757d",
                "计划中": "#17a2b8"
            }
        )

        fig.update_layout(
            height=height,
            margin=dict(l=20, r=20, t=40, b=20),
            showlegend=True
        )

        st.plotly_chart(fig, use_container_width=True)


def render_compact_timeline_chart(plans, height=400):
    """渲染紧凑版时间线图"""

    timeline_data = []
    for i, plan in enumerate(plans):
        timeline_data.append({
            "x": pd.to_datetime(plan["start_time"]),
            "y": i,
            "size": plan["quantity"] / 20,
            "equipment": plan["equipment_name"],
            "product": plan["product_name"]
        })

    if timeline_data:
        df_timeline = pd.DataFrame(timeline_data)

        fig = px.scatter(
            df_timeline,
            x="x",
            y="y",
            size="size",
            color="equipment",
            hover_data=["product"],
            title="生产时间线"
        )

        fig.update_layout(
            height=height,
            margin=dict(l=20, r=20, t=40, b=20),
            xaxis_title="开始时间",
            yaxis_title="任务序号"
        )

        st.plotly_chart(fig, use_container_width=True)


def render_compact_workload_chart(plans, height=400):
    """渲染紧凑版负载图"""

    # 按设备统计负载
    equipment_workload = {}
    for plan in plans:
        equipment = plan["equipment_name"]
        if equipment not in equipment_workload:
            equipment_workload[equipment] = 0

        start_time = pd.to_datetime(plan["start_time"])
        end_time = pd.to_datetime(plan["end_time"])
        duration = (end_time - start_time).total_seconds() / 3600
        equipment_workload[equipment] += duration

    # 创建负载图
    equipment_names = list(equipment_workload.keys())
    workload_hours = list(equipment_workload.values())
    workload_rates = [min(hours / 40 * 100, 100) for hours in workload_hours]  # 假设每周40小时

    fig = px.bar(
        x=equipment_names,
        y=workload_rates,
        title="设备负载率",
        color=workload_rates,
        color_continuous_scale="RdYlGn_r"
    )

    fig.add_hline(y=100, line_dash="dash", line_color="red")

    fig.update_layout(
        height=height,
        margin=dict(l=20, r=20, t=40, b=20),
        xaxis_title="设备",
        yaxis_title="负载率 (%)",
        showlegend=False
    )

    st.plotly_chart(fig, use_container_width=True)


def render_compact_resource_chart(plans, height=400):
    """渲染紧凑版资源分配图"""

    # 统计资源分配
    resource_allocation = {}
    for plan in plans:
        equipment = plan["equipment_name"]
        if equipment not in resource_allocation:
            resource_allocation[equipment] = 0
        resource_allocation[equipment] += plan["quantity"]

    # 创建饼图
    equipment_names = list(resource_allocation.keys())
    quantities = list(resource_allocation.values())

    fig = px.pie(
        values=quantities,
        names=equipment_names,
        title="设备产量分配"
    )

    fig.update_layout(
        height=height,
        margin=dict(l=20, r=20, t=40, b=20)
    )

    st.plotly_chart(fig, use_container_width=True)


def render_compact_progress_dashboard(plans, height=400):
    """渲染紧凑版进度仪表板"""

    # 计算进度统计
    total_plans = len(plans)
    completed_plans = sum(1 for plan in plans if plan.get("status") == "已完成")
    in_progress_plans = sum(1 for plan in plans if plan.get("status") == "进行中")
    pending_plans = sum(1 for plan in plans if plan.get("status") == "待开始")

    completion_rate = (completed_plans / total_plans * 100) if total_plans > 0 else 0

    # 创建仪表盘
    fig = go.Figure(go.Indicator(
        mode="gauge+number+delta",
        value=completion_rate,
        domain={'x': [0, 1], 'y': [0, 1]},
        title={'text': "完成率 (%)"},
        delta={'reference': 75},
        gauge={
            'axis': {'range': [None, 100]},
            'bar': {'color': "darkblue"},
            'steps': [
                {'range': [0, 50], 'color': "lightgray"},
                {'range': [50, 80], 'color': "yellow"},
                {'range': [80, 100], 'color': "green"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 90
            }
        }
    ))

    fig.update_layout(
        height=height,
        margin=dict(l=20, r=20, t=40, b=20)
    )

    st.plotly_chart(fig, use_container_width=True)

    # 显示统计信息
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("已完成", completed_plans)

    with col2:
        st.metric("进行中", in_progress_plans)

    with col3:
        st.metric("待开始", pending_plans)


def get_mock_production_plans():
    """获取模拟生产计划数据"""
    return [
        {
            "product_name": "产品A",
            "order_id": "ORD001",
            "start_time": "2024-01-15 08:00:00",
            "end_time": "2024-01-17 18:00:00",
            "equipment_name": "设备1",
            "quantity": 100,
            "status": "进行中"
        },
        {
            "product_name": "产品B",
            "order_id": "ORD002",
            "start_time": "2024-01-16 08:00:00",
            "end_time": "2024-01-19 18:00:00",
            "equipment_name": "设备2",
            "quantity": 150,
            "status": "待开始"
        },
        {
            "product_name": "产品C",
            "order_id": "ORD003",
            "start_time": "2024-01-14 08:00:00",
            "end_time": "2024-01-16 18:00:00",
            "equipment_name": "设备1",
            "quantity": 80,
            "status": "已完成"
        },
        {
            "product_name": "产品D",
            "order_id": "ORD004",
            "start_time": "2024-01-18 08:00:00",
            "end_time": "2024-01-20 18:00:00",
            "equipment_name": "设备3",
            "quantity": 120,
            "status": "计划中"
        }
    ]


def render_production_summary_widget():
    """渲染生产概览小组件"""

    plans = get_mock_production_plans()

    # 计算关键指标
    total_quantity = sum(plan["quantity"] for plan in plans)
    completed_quantity = sum(plan["quantity"] for plan in plans if plan.get("status") == "已完成")
    completion_rate = (completed_quantity / total_quantity * 100) if total_quantity > 0 else 0

    # 显示指标
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("总订单", len(plans))

    with col2:
        st.metric("总产量", total_quantity)

    with col3:
        st.metric("完成率", f"{completion_rate:.1f}%")

    with col4:
        active_equipment = len(set(plan["equipment_name"] for plan in plans))
        st.metric("活跃设备", active_equipment)


def get_mock_production_plans():
    """获取模拟生产计划数据"""
    return [
        {
            "product_name": "产品A",
            "order_id": "ORD001",
            "start_time": "2024-01-15 08:00:00",
            "end_time": "2024-01-17 18:00:00",
            "equipment_name": "设备1",
            "quantity": 100,
            "status": "进行中"
        },
        {
            "product_name": "产品B",
            "order_id": "ORD002",
            "start_time": "2024-01-16 08:00:00",
            "end_time": "2024-01-19 18:00:00",
            "equipment_name": "设备2",
            "quantity": 150,
            "status": "待开始"
        },
        {
            "product_name": "产品C",
            "order_id": "ORD003",
            "start_time": "2024-01-14 08:00:00",
            "end_time": "2024-01-16 18:00:00",
            "equipment_name": "设备1",
            "quantity": 80,
            "status": "已完成"
        },
        {
            "product_name": "产品D",
            "order_id": "ORD004",
            "start_time": "2024-01-18 08:00:00",
            "end_time": "2024-01-20 18:00:00",
            "equipment_name": "设备3",
            "quantity": 120,
            "status": "计划中"
        }
    ]
