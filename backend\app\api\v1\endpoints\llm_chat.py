"""
LLM聊天API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.logging_config import business_logger
from app.schemas.llm import (
    ChatRequest, ChatResponse, ChatHistoryResponse, 
    LLMConfigResponse, LLMConfigUpdate
)
from app.services.llm_service import LLMService
from app.api.dependencies import (
    get_current_user, require_permission, get_pagination_params, PaginationParams
)

router = APIRouter()


@router.post("/chat", response_model=ChatResponse, summary="LLM聊天")
async def chat_with_llm(
    chat_request: ChatRequest,
    current_user: dict = Depends(require_permission("llm.chat")),
    db: AsyncSession = Depends(get_db)
):
    """与LLM进行聊天"""
    try:
        llm_service = LLMService(db)
        
        # 执行聊天
        response = await llm_service.chat(
            message=chat_request.message,
            context=chat_request.context,
            model=chat_request.model,
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="llm_chat",
            details={
                "model": chat_request.model,
                "message_length": len(chat_request.message),
                "response_length": len(response.get("response", ""))
            }
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="llm_chat_error",
            error_message=str(e),
            details={"user_id": current_user["id"]}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="LLM聊天失败"
        )


@router.get("/history", response_model=ChatHistoryResponse, summary="获取聊天历史")
async def get_chat_history(
    pagination: PaginationParams = Depends(get_pagination_params),
    session_id: Optional[str] = Query(None, description="会话ID"),
    current_user: dict = Depends(require_permission("llm.chat")),
    db: AsyncSession = Depends(get_db)
):
    """获取聊天历史"""
    try:
        llm_service = LLMService(db)
        
        history, total = await llm_service.get_chat_history(
            user_id=current_user["id"],
            session_id=session_id,
            page=pagination.page,
            page_size=pagination.page_size
        )
        
        total_pages = (total + pagination.page_size - 1) // pagination.page_size
        
        return {
            "history": history,
            "total": total,
            "page": pagination.page,
            "page_size": pagination.page_size,
            "total_pages": total_pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取聊天历史失败"
        )


@router.delete("/history/{session_id}", summary="删除聊天会话")
async def delete_chat_session(
    session_id: str,
    current_user: dict = Depends(require_permission("llm.chat")),
    db: AsyncSession = Depends(get_db)
):
    """删除聊天会话"""
    try:
        llm_service = LLMService(db)
        
        success = await llm_service.delete_chat_session(
            session_id=session_id,
            user_id=current_user["id"]
        )
        
        if success:
            business_logger.log_user_action(
                user_id=current_user["id"],
                action="llm_session_delete",
                details={"session_id": session_id}
            )
            
            return {"success": True, "message": "聊天会话删除成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="聊天会话不存在"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="llm_session_delete_error",
            error_message=str(e),
            details={"session_id": session_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除聊天会话失败"
        )


@router.get("/config", response_model=LLMConfigResponse, summary="获取LLM配置")
async def get_llm_config(
    current_user: dict = Depends(require_permission("llm.config")),
    db: AsyncSession = Depends(get_db)
):
    """获取LLM配置"""
    try:
        llm_service = LLMService(db)
        config = await llm_service.get_llm_config()
        
        return {
            "success": True,
            "data": config
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取LLM配置失败"
        )


@router.put("/config", response_model=LLMConfigResponse, summary="更新LLM配置")
async def update_llm_config(
    config_data: LLMConfigUpdate,
    current_user: dict = Depends(require_permission("llm.config")),
    db: AsyncSession = Depends(get_db)
):
    """更新LLM配置"""
    try:
        llm_service = LLMService(db)
        
        config = await llm_service.update_llm_config(
            config_data=config_data,
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="llm_config_update",
            details=config_data.dict(exclude_unset=True)
        )
        
        return {
            "success": True,
            "message": "LLM配置更新成功",
            "data": config
        }
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="llm_config_update_error",
            error_message=str(e),
            details={"user_id": current_user["id"]}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新LLM配置失败"
        )


@router.get("/models", summary="获取可用模型列表")
async def get_available_models(
    current_user: dict = Depends(require_permission("llm.chat")),
    db: AsyncSession = Depends(get_db)
):
    """获取可用的LLM模型列表"""
    try:
        llm_service = LLMService(db)
        models = await llm_service.get_available_models()
        
        return {
            "success": True,
            "data": {
                "models": models
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型列表失败"
        )


@router.post("/analyze", summary="数据分析")
async def analyze_data(
    analysis_request: dict,
    current_user: dict = Depends(require_permission("llm.chat")),
    db: AsyncSession = Depends(get_db)
):
    """使用LLM进行数据分析"""
    try:
        llm_service = LLMService(db)
        
        analysis_result = await llm_service.analyze_data(
            data=analysis_request.get("data"),
            analysis_type=analysis_request.get("analysis_type"),
            context=analysis_request.get("context"),
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="llm_data_analysis",
            details={
                "analysis_type": analysis_request.get("analysis_type"),
                "data_size": len(str(analysis_request.get("data", "")))
            }
        )
        
        return {
            "success": True,
            "data": analysis_result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="llm_analysis_error",
            error_message=str(e),
            details={"user_id": current_user["id"]}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="数据分析失败"
        )


@router.post("/suggest", summary="获取优化建议")
async def get_optimization_suggestions(
    suggestion_request: dict,
    current_user: dict = Depends(require_permission("llm.chat")),
    db: AsyncSession = Depends(get_db)
):
    """获取生产优化建议"""
    try:
        llm_service = LLMService(db)
        
        suggestions = await llm_service.get_optimization_suggestions(
            plan_data=suggestion_request.get("plan_data"),
            constraints=suggestion_request.get("constraints"),
            objectives=suggestion_request.get("objectives"),
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="llm_optimization_suggest",
            details={
                "plan_id": suggestion_request.get("plan_data", {}).get("id"),
                "objectives": suggestion_request.get("objectives")
            }
        )
        
        return {
            "success": True,
            "data": suggestions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="llm_suggestion_error",
            error_message=str(e),
            details={"user_id": current_user["id"]}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取优化建议失败"
        )


@router.get("/statistics", summary="获取LLM使用统计")
async def get_llm_statistics(
    days: int = Query(7, ge=1, le=90, description="统计天数"),
    current_user: dict = Depends(require_permission("llm.config")),
    db: AsyncSession = Depends(get_db)
):
    """获取LLM使用统计"""
    try:
        llm_service = LLMService(db)
        statistics = await llm_service.get_usage_statistics(days)
        
        return {
            "success": True,
            "data": statistics
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取LLM统计失败"
        )
