# 🗄️ Smart Planning 数据库查询使用示例

## 📋 概述

本文档提供Smart Planning多数据库查询功能的详细使用示例，包括PCI数据查询、Oracle数据库支持、主数据库多表查询等。

## 🔧 配置示例

### 1. PCI数据库配置

```bash
# .env文件配置
PCI_DB_URL=mysql+aiomysql://pci_user:pci_password@localhost:3306/pci_system?charset=utf8mb4
PCI_DB_HOST=localhost
PCI_DB_PORT=3306
PCI_DB_USER=pci_user
PCI_DB_PASSWORD=pci_password
PCI_DB_NAME=pci_system
PCI_DB_ENGINE=mysql
```

### 2. Oracle数据库配置

```bash
# Oracle数据库配置
ORACLE_DB_URL=oracle+oracledb://oracle_user:oracle_password@localhost:1521/?service_name=ORCL
ORACLE_DB_HOST=localhost
ORACLE_DB_PORT=1521
ORACLE_DB_USER=oracle_user
ORACLE_DB_PASSWORD=oracle_password
ORACLE_DB_NAME=ORCL
ORACLE_DB_ENGINE=oracle
```

### 3. 自定义数据库配置

```bash
# 自定义数据库1 - MySQL
CUSTOM1_DB_URL=mysql+aiomysql://custom1_user:password@localhost:3306/custom1_db?charset=utf8mb4
CUSTOM1_DB_ENGINE=mysql

# 自定义数据库2 - PostgreSQL
CUSTOM2_DB_URL=postgresql+asyncpg://custom2_user:password@localhost:5432/custom2_db
CUSTOM2_DB_ENGINE=postgresql

# 自定义数据库3 - Oracle
CUSTOM3_DB_URL=oracle+oracledb://custom3_user:password@localhost:1521/?service_name=CUSTOM3
CUSTOM3_DB_ENGINE=oracle
```

## 💻 Python代码使用示例

### 1. PCI数据查询

```python
from app.services.database_query_service import pci_query_service, database_query_service

# 获取PCI数据汇总
async def get_pci_summary():
    summary = await pci_query_service.get_pci_summary()
    return summary

# 获取FIFO优先级商品
async def get_fifo_items():
    items = await pci_query_service.get_fifo_priority_items()
    return items

# 获取特定商品的消耗策略
async def get_consumption_strategy(item_code: str):
    strategy = await pci_query_service.get_consumption_strategy(item_code)
    return strategy

# 自定义PCI查询
async def custom_pci_query():
    query = """
    SELECT
        item_code,
        item_name,
        quantity,
        age_days,
        CASE
            WHEN age_days > 180 THEN '紧急'
            WHEN age_days > 120 THEN '高优先级'
            WHEN age_days > 60 THEN '中优先级'
            ELSE '正常'
        END as priority_level
    FROM pci_data
    WHERE status = 'available' AND quantity > :min_quantity
    ORDER BY age_days DESC
    """

    params = {"min_quantity": 100}
    result = await database_query_service.query_pci_data(query, params)
    return result
```

### 2. Oracle数据库查询

```python
from app.services.database_query_service import database_query_service

# 查询Oracle数据库
async def query_oracle_inventory():
    # Oracle特有的查询语法
    query = """
    SELECT
        ITEM_CODE,
        ITEM_NAME,
        QUANTITY,
        LOCATION,
        SYSDATE as QUERY_TIME
    FROM INVENTORY_TABLE
    WHERE STATUS = :status
    AND ROWNUM <= :limit
    """

    params = {"status": "ACTIVE", "limit": 100}
    result = await database_query_service.query_oracle_data(query, params)
    return result

# 使用Oracle特有函数
async def oracle_advanced_query():
    query = """
    SELECT
        ITEM_CODE,
        ITEM_NAME,
        QUANTITY,
        LAG(QUANTITY, 1) OVER (PARTITION BY ITEM_CODE ORDER BY UPDATE_DATE) as PREV_QUANTITY,
        DECODE(STATUS, 'A', '可用', 'I', '不可用', '未知') as STATUS_DESC
    FROM INVENTORY_TABLE
    WHERE UPDATE_DATE >= SYSDATE - :days
    ORDER BY UPDATE_DATE DESC
    """

    params = {"days": 30}
    result = await database_query_service.query_oracle_data(query, params)
    return result
```

### 3. 主数据库多表查询

```python
# 主数据库中查询不同类型的数据
async def query_main_database_tables():

    # 查询生产计划数据
    production_query = """
    SELECT
        plan_name,
        start_date,
        end_date,
        status,
        created_at
    FROM production_plans
    WHERE status = :status
    ORDER BY start_date DESC
    """

    # 查询设备数据
    equipment_query = """
    SELECT
        equipment_code,
        equipment_name,
        status,
        efficiency,
        capacity
    FROM equipment
    WHERE equipment_type = :type
    ORDER BY equipment_code
    """

    # 查询PCI数据（如果在主数据库中）
    pci_query = """
    SELECT
        item_code,
        item_name,
        quantity,
        age_days,
        status
    FROM pci_data
    WHERE age_days > :age_threshold
    ORDER BY age_days DESC
    """

    # 执行查询
    production_data = await database_query_service.query_main_data(
        production_query,
        {"status": "active"}
    )

    equipment_data = await database_query_service.query_main_data(
        equipment_query,
        {"type": "production_line"}
    )

    pci_data = await database_query_service.query_main_data(
        pci_query,
        {"age_threshold": 180}
    )

    return {
        "production": production_data,
        "equipment": equipment_data,
        "pci": pci_data
    }
```

### 4. 跨数据库查询

```python
from app.services.database_query_service import multi_database_query_service

# 跨数据库查询
async def cross_database_analysis():
    queries = {
        "inventory": {
            "query": "SELECT item_code, SUM(quantity) as total_stock FROM inventory GROUP BY item_code",
            "params": {}
        },
        "pci": {
            "query": "SELECT item_code, AVG(age_days) as avg_age FROM pci_data GROUP BY item_code",
            "params": {}
        },
        "production_analysis": {
            "query": "SELECT product_code, SUM(output_quantity) as total_output FROM production_records WHERE date >= :start_date GROUP BY product_code",
            "params": {"start_date": "2024-01-01"}
        }
    }

    results = await multi_database_query_service.cross_database_query(queries)
    return results

# 带回退的查询
async def query_with_fallback():
    query = "SELECT * FROM critical_data WHERE status = 'active'"

    # 优先查询专用数据库，失败时回退到主数据库
    result = await multi_database_query_service.query_with_fallback(
        primary_db="inventory",
        fallback_db="main",
        query=query
    )
    return result
```

### 5. 动态数据库管理

```python
from app.core.dynamic_database import dynamic_db_manager, DatabaseConfig, DatabaseEngine

# 运行时添加新数据库
async def add_new_database():
    config = DatabaseConfig(
        name="new_warehouse_db",
        engine_type=DatabaseEngine.MYSQL,
        host="*************",
        port=3306,
        username="warehouse_user",
        password="warehouse_password",
        database="warehouse_system",
        description="新仓库管理系统数据库"
    )

    success = dynamic_db_manager.add_database_config(config)
    if success:
        # 测试连接
        health = await dynamic_db_manager.health_check("new_warehouse_db")
        return {"added": True, "healthy": health}
    return {"added": False}

# 查询动态添加的数据库
async def query_dynamic_database():
    query = "SELECT * FROM warehouse_inventory WHERE location = :location"
    params = {"location": "A区"}

    result = await dynamic_db_manager.execute_query("new_warehouse_db", query, params)
    return result
```

## 🌐 API接口使用示例

### 1. PCI数据查询API

```http
# 获取PCI数据汇总
GET /api/v1/database/pci/summary
Authorization: Bearer <token>

# 获取FIFO优先级商品
GET /api/v1/database/pci/fifo-priority
Authorization: Bearer <token>

# 获取消耗策略
GET /api/v1/database/pci/consumption-strategy?item_code=RAW001
Authorization: Bearer <token>
```

### 2. Oracle数据库查询API

```http
# 执行Oracle查询
POST /api/v1/database/query/custom
Authorization: Bearer <token>
Content-Type: application/json

{
  "database_type": "oracle",
  "query": "SELECT ITEM_CODE, QUANTITY FROM INVENTORY_TABLE WHERE ROWNUM <= :limit",
  "parameters": {
    "limit": 100
  }
}
```

### 3. 跨数据库查询API

```http
# 获取所有数据库状态
GET /api/v1/database/status/all
Authorization: Bearer <token>

# 列出可用数据库
GET /api/v1/database/available
Authorization: Bearer <token>
```

## 📊 前端使用示例

```python
import streamlit as st
import requests

# 前端查询PCI数据
def display_pci_data():
    st.header("PCI数据管理")

    # 获取PCI汇总数据
    response = requests.get(
        f"{API_BASE_URL}/database/pci/summary",
        headers={"Authorization": f"Bearer {st.session_state.token}"}
    )

    if response.status_code == 200:
        data = response.json()["data"]
        st.dataframe(data)

    # 查询特定商品
    item_code = st.text_input("商品代码")
    if item_code:
        strategy_response = requests.get(
            f"{API_BASE_URL}/database/pci/consumption-strategy",
            params={"item_code": item_code},
            headers={"Authorization": f"Bearer {st.session_state.token}"}
        )

        if strategy_response.status_code == 200:
            strategy_data = strategy_response.json()["data"]
            st.write("消耗策略:")
            st.dataframe(strategy_data)

# 前端自定义查询
def custom_query_interface():
    st.header("自定义数据库查询")

    # 数据库选择
    db_type = st.selectbox("选择数据库", ["main", "inventory", "pci", "oracle", "custom1"])

    # SQL查询输入
    query = st.text_area("SQL查询", height=150)

    # 参数输入
    params_json = st.text_area("查询参数 (JSON格式)", value="{}")

    if st.button("执行查询"):
        try:
            import json
            params = json.loads(params_json)

            response = requests.post(
                f"{API_BASE_URL}/database/query/custom",
                json={
                    "database_type": db_type,
                    "query": query,
                    "parameters": params
                },
                headers={"Authorization": f"Bearer {st.session_state.token}"}
            )

            if response.status_code == 200:
                result = response.json()
                st.success(f"查询成功，返回 {result['total_rows']} 行数据")
                st.dataframe(result["data"])
            else:
                st.error(f"查询失败: {response.text}")

        except json.JSONDecodeError:
            st.error("参数格式错误，请输入有效的JSON")
        except Exception as e:
            st.error(f"查询出错: {str(e)}")
```

## 🎯 最佳实践

### 1. 数据库连接管理
- 使用连接池避免频繁创建连接
- 设置合理的超时时间
- 实施连接健康检查

### 2. 查询优化
- 使用参数化查询防止SQL注入
- 添加适当的索引提高查询性能
- 限制查询结果数量避免内存溢出

### 3. 错误处理
- 实施完善的异常处理机制
- 提供有意义的错误信息
- 实施查询重试机制

### 4. 安全考虑
- 使用最小权限原则配置数据库用户
- 加密敏感配置信息
- 实施访问日志记录

---

**🎉 通过这些示例，您可以充分利用Smart Planning的多数据库查询功能，实现灵活的数据访问和分析！**
