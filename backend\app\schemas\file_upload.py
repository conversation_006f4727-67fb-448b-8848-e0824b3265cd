"""
文件上传相关的Pydantic模式
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, validator


class FileUploadResponse(BaseModel):
    """文件上传响应模式"""
    success: bool
    message: str
    data: Dict[str, Any]


class UploadedFileInfo(BaseModel):
    """上传文件信息模式"""
    id: str
    original_filename: str
    file_size: int
    file_type: str
    status: str
    created_at: str
    processed_at: Optional[str] = None


class UploadHistoryResponse(BaseModel):
    """上传历史响应模式"""
    success: bool
    data: Dict[str, Any]


class ExtractedDataInfo(BaseModel):
    """提取数据信息模式"""
    id: str
    data_type: str
    table_name: Optional[str] = None
    sheet_name: Optional[str] = None
    row_count: int
    column_count: int
    raw_data: Dict[str, Any]
    processed_data: Optional[Dict[str, Any]] = None
    field_mapping: Optional[Dict[str, Any]] = None
    validation_result: Optional[Dict[str, Any]] = None
    import_status: str


class ExtractedDataResponse(BaseModel):
    """提取数据响应模式"""
    success: bool
    data: Dict[str, Any]


class FileProcessingRequest(BaseModel):
    """文件处理请求模式"""
    processing_type: str
    processing_params: Optional[Dict[str, Any]] = None
    priority: Optional[int] = 5
    
    @validator('processing_type')
    def validate_processing_type(cls, v):
        allowed_types = [
            'parse_excel', 'parse_email', 'extract_tables', 
            'auto_parse', 'manual_parse'
        ]
        if v not in allowed_types:
            raise ValueError(f'处理类型必须是: {", ".join(allowed_types)}')
        return v
    
    @validator('priority')
    def validate_priority(cls, v):
        if v is not None and (v < 1 or v > 10):
            raise ValueError('优先级必须在1-10之间')
        return v


class FieldMappingRequest(BaseModel):
    """字段映射请求模式"""
    extracted_data_id: str
    field_mapping: Dict[str, str]
    target_table: str
    
    @validator('field_mapping')
    def validate_field_mapping(cls, v):
        if not v:
            raise ValueError('字段映射不能为空')
        return v


class DataImportRequest(BaseModel):
    """数据导入请求模式"""
    extracted_data_id: str
    target_table: str
    operation_type: str = "insert"
    field_mapping: Optional[Dict[str, str]] = None
    
    @validator('operation_type')
    def validate_operation_type(cls, v):
        allowed_types = ['insert', 'update', 'upsert']
        if v not in allowed_types:
            raise ValueError(f'操作类型必须是: {", ".join(allowed_types)}')
        return v


class FileStatusResponse(BaseModel):
    """文件状态响应模式"""
    success: bool
    data: Dict[str, Any]


class ProcessingQueueInfo(BaseModel):
    """处理队列信息模式"""
    id: str
    file_id: str
    processing_type: str
    priority: int
    status: str
    scheduled_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    retry_count: int
    error_message: Optional[str] = None


class FileStatistics(BaseModel):
    """文件统计模式"""
    total_files: int
    uploaded_today: int
    processing_files: int
    processed_files: int
    failed_files: int
    total_size: int
    file_types: Dict[str, int]


class FileStatisticsResponse(BaseModel):
    """文件统计响应模式"""
    success: bool
    data: FileStatistics


class EmailParsingResult(BaseModel):
    """邮件解析结果模式"""
    sender: Optional[str] = None
    recipient: Optional[str] = None
    subject: Optional[str] = None
    date: Optional[str] = None
    body_text: Optional[str] = None
    body_html: Optional[str] = None
    attachments: List[Dict[str, Any]] = []
    tables: List[Dict[str, Any]] = []


class ExcelParsingResult(BaseModel):
    """Excel解析结果模式"""
    sheets: List[Dict[str, Any]]
    total_sheets: int
    total_rows: int
    total_columns: int


class TableExtractionResult(BaseModel):
    """表格提取结果模式"""
    tables: List[Dict[str, Any]]
    total_tables: int
    extraction_method: str
    confidence_score: Optional[float] = None


class ValidationRule(BaseModel):
    """验证规则模式"""
    field_name: str
    rule_type: str  # required, type, range, pattern, custom
    rule_params: Dict[str, Any]
    error_message: str


class DataValidationRequest(BaseModel):
    """数据验证请求模式"""
    extracted_data_id: str
    validation_rules: List[ValidationRule]


class DataValidationResult(BaseModel):
    """数据验证结果模式"""
    is_valid: bool
    total_records: int
    valid_records: int
    invalid_records: int
    validation_errors: List[Dict[str, Any]]
    field_statistics: Dict[str, Any]


class DataValidationResponse(BaseModel):
    """数据验证响应模式"""
    success: bool
    data: DataValidationResult


class FilePreviewRequest(BaseModel):
    """文件预览请求模式"""
    file_id: str
    sheet_name: Optional[str] = None
    start_row: Optional[int] = 0
    max_rows: Optional[int] = 100


class FilePreviewResponse(BaseModel):
    """文件预览响应模式"""
    success: bool
    data: Dict[str, Any]
