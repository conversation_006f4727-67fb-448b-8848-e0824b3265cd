"""
SSO单点登录服务
支持SAML 2.0和OAuth 2.0/OpenID Connect
"""

import jwt
import requests
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime, timedelta
from urllib.parse import urlencode, parse_qs, urlparse
import xml.etree.ElementTree as ET
import base64
import zlib

from ..core.config import settings
from ..models.user import User
from ..services.user_service import UserService

logger = logging.getLogger(__name__)


class SSOConfig:
    """SSO配置类"""

    def __init__(self):
        # SSO类型配置
        self.sso_type = getattr(settings, 'SSO_TYPE', 'saml')  # saml, oauth, oidc
        self.enabled = getattr(settings, 'SSO_ENABLED', False)

        # SAML配置
        self.saml_idp_url = getattr(settings, 'SAML_IDP_URL', '')
        self.saml_idp_cert = getattr(settings, 'SAML_IDP_CERT', '')
        self.saml_sp_entity_id = getattr(settings, 'SAML_SP_ENTITY_ID', 'smart-aps')
        self.saml_acs_url = getattr(settings, 'SAML_ACS_URL', 'http://localhost:8000/api/v1/auth/saml/acs')
        self.saml_sls_url = getattr(settings, 'SAML_SLS_URL', 'http://localhost:8000/api/v1/auth/saml/sls')

        # OAuth/OIDC配置
        self.oauth_client_id = getattr(settings, 'OAUTH_CLIENT_ID', '')
        self.oauth_client_secret = getattr(settings, 'OAUTH_CLIENT_SECRET', '')
        self.oauth_auth_url = getattr(settings, 'OAUTH_AUTH_URL', '')
        self.oauth_token_url = getattr(settings, 'OAUTH_TOKEN_URL', '')
        self.oauth_userinfo_url = getattr(settings, 'OAUTH_USERINFO_URL', '')
        self.oauth_redirect_uri = getattr(settings, 'OAUTH_REDIRECT_URI', 'http://localhost:8000/api/v1/auth/oauth/callback')
        self.oauth_scope = getattr(settings, 'OAUTH_SCOPE', 'openid profile email')

        # 用户属性映射
        self.username_claim = getattr(settings, 'SSO_USERNAME_CLAIM', 'sub')
        self.email_claim = getattr(settings, 'SSO_EMAIL_CLAIM', 'email')
        self.name_claim = getattr(settings, 'SSO_NAME_CLAIM', 'name')
        self.groups_claim = getattr(settings, 'SSO_GROUPS_CLAIM', 'groups')

        # 权限映射
        self.admin_groups = getattr(settings, 'SSO_ADMIN_GROUPS', ['administrators', 'admin'])
        self.user_groups = getattr(settings, 'SSO_USER_GROUPS', ['users'])

        # 同步配置
        self.auto_create_users = getattr(settings, 'SSO_AUTO_CREATE_USERS', True)
        self.auto_update_users = getattr(settings, 'SSO_AUTO_UPDATE_USERS', True)


class SSOService:
    """SSO认证服务"""

    def __init__(self, db_session=None):
        self.config = SSOConfig()
        self.db = db_session
        self.user_service = UserService(db_session) if db_session else None

    def get_oauth_login_url(self, state: str = None) -> str:
        """获取OAuth登录URL"""
        if not self.config.enabled or self.config.sso_type not in ['oauth', 'oidc']:
            raise ValueError("OAuth/OIDC未启用")

        params = {
            'client_id': self.config.oauth_client_id,
            'redirect_uri': self.config.oauth_redirect_uri,
            'scope': self.config.oauth_scope,
            'response_type': 'code',
            'state': state or 'default'
        }

        return f"{self.config.oauth_auth_url}?{urlencode(params)}"

    def handle_oauth_callback(self, code: str, state: str = None) -> Optional[Dict[str, Any]]:
        """处理OAuth回调"""
        try:
            # 交换授权码获取访问令牌
            token_data = self._exchange_oauth_code(code)
            if not token_data:
                return None

            # 获取用户信息
            user_info = self._get_oauth_user_info(token_data['access_token'])
            if not user_info:
                return None

            # 处理用户信息
            processed_user = self._process_sso_user(user_info, 'oauth')

            return processed_user

        except Exception as e:
            logger.error(f"OAuth回调处理失败: {e}")
            return None

    def _exchange_oauth_code(self, code: str) -> Optional[Dict[str, Any]]:
        """交换OAuth授权码"""
        try:
            data = {
                'grant_type': 'authorization_code',
                'client_id': self.config.oauth_client_id,
                'client_secret': self.config.oauth_client_secret,
                'redirect_uri': self.config.oauth_redirect_uri,
                'code': code
            }

            response = requests.post(
                self.config.oauth_token_url,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=30
            )

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"OAuth令牌交换失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"OAuth令牌交换错误: {e}")
            return None

    def _get_oauth_user_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        """获取OAuth用户信息"""
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }

            response = requests.get(
                self.config.oauth_userinfo_url,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取OAuth用户信息失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"获取OAuth用户信息错误: {e}")
            return None

    def get_saml_login_url(self, relay_state: str = None) -> str:
        """获取SAML登录URL"""
        if not self.config.enabled or self.config.sso_type != 'saml':
            raise ValueError("SAML未启用")

        # 构建SAML认证请求
        saml_request = self._build_saml_auth_request()

        # Base64编码并压缩
        compressed = zlib.compress(saml_request.encode('utf-8'))
        encoded_request = base64.b64encode(compressed).decode('utf-8')

        params = {
            'SAMLRequest': encoded_request,
            'RelayState': relay_state or 'default'
        }

        return f"{self.config.saml_idp_url}?{urlencode(params)}"

    def _build_saml_auth_request(self) -> str:
        """构建SAML认证请求"""
        request_id = f"_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"
        issue_instant = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')

        saml_request = f"""<?xml version="1.0" encoding="UTF-8"?>
<samlp:AuthnRequest xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
                    xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
                    ID="{request_id}"
                    Version="2.0"
                    IssueInstant="{issue_instant}"
                    Destination="{self.config.saml_idp_url}"
                    AssertionConsumerServiceURL="{self.config.saml_acs_url}"
                    ProtocolBinding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST">
    <saml:Issuer>{self.config.saml_sp_entity_id}</saml:Issuer>
    <samlp:NameIDPolicy Format="urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress"
                        AllowCreate="true"/>
</samlp:AuthnRequest>"""

        return saml_request

    def handle_saml_response(self, saml_response: str, relay_state: str = None) -> Optional[Dict[str, Any]]:
        """处理SAML响应"""
        try:
            # 解码SAML响应
            decoded_response = base64.b64decode(saml_response).decode('utf-8')

            # 解析XML
            root = ET.fromstring(decoded_response)

            # 验证响应（简化版本，生产环境需要完整验证）
            if not self._verify_saml_response(root):
                logger.error("SAML响应验证失败")
                return None

            # 提取用户信息
            user_info = self._extract_saml_user_info(root)
            if not user_info:
                return None

            # 处理用户信息
            processed_user = self._process_sso_user(user_info, 'saml')

            return processed_user

        except Exception as e:
            logger.error(f"SAML响应处理失败: {e}")
            return None

    def _verify_saml_response(self, root: ET.Element) -> bool:
        """验证SAML响应（简化版本）"""
        try:
            # 检查状态码
            status_code = root.find('.//{urn:oasis:names:tc:SAML:2.0:protocol}StatusCode')
            if status_code is not None:
                value = status_code.get('Value')
                if value != 'urn:oasis:names:tc:SAML:2.0:status:Success':
                    logger.error(f"SAML认证失败: {value}")
                    return False

            # 检查断言
            assertion = root.find('.//{urn:oasis:names:tc:SAML:2.0:assertion}Assertion')
            if assertion is None:
                logger.error("SAML响应中未找到断言")
                return False

            # 生产环境需要验证签名、时间戳等
            return True

        except Exception as e:
            logger.error(f"SAML响应验证错误: {e}")
            return False

    def _extract_saml_user_info(self, root: ET.Element) -> Optional[Dict[str, Any]]:
        """从SAML响应中提取用户信息"""
        try:
            user_info = {}

            # 获取NameID
            name_id = root.find('.//{urn:oasis:names:tc:SAML:2.0:assertion}NameID')
            if name_id is not None:
                user_info['username'] = name_id.text
                user_info['email'] = name_id.text  # 假设NameID是邮箱

            # 获取属性
            attributes = root.findall('.//{urn:oasis:names:tc:SAML:2.0:assertion}Attribute')
            for attr in attributes:
                attr_name = attr.get('Name')
                attr_value = attr.find('.//{urn:oasis:names:tc:SAML:2.0:assertion}AttributeValue')

                if attr_value is not None and attr_name:
                    if attr_name in ['email', 'mail']:
                        user_info['email'] = attr_value.text
                    elif attr_name in ['name', 'displayName']:
                        user_info['full_name'] = attr_value.text
                    elif attr_name in ['groups', 'memberOf']:
                        user_info['groups'] = attr_value.text.split(',') if attr_value.text else []

            return user_info if user_info else None

        except Exception as e:
            logger.error(f"提取SAML用户信息错误: {e}")
            return None

    def _process_sso_user(self, user_info: Dict[str, Any], source: str) -> Optional[Dict[str, Any]]:
        """处理SSO用户信息"""
        try:
            # 提取标准字段
            username = user_info.get(self.config.username_claim) or user_info.get('username')
            email = user_info.get(self.config.email_claim) or user_info.get('email')
            full_name = user_info.get(self.config.name_claim) or user_info.get('full_name', username)
            groups = user_info.get(self.config.groups_claim) or user_info.get('groups', [])

            if not username:
                logger.error("SSO用户信息中缺少用户名")
                return None

            processed_user = {
                'username': username,
                'email': email or '',
                'full_name': full_name or username,
                'groups': groups,
                'source': source,
                'raw_info': user_info
            }

            # 同步到数据库
            if self.user_service:
                # 注意：这里需要在调用处使用await
                processed_user['sync_needed'] = True

            return processed_user

        except Exception as e:
            logger.error(f"处理SSO用户信息错误: {e}")
            return None

    async def sync_user_to_database(self, sso_user_info: Dict[str, Any]) -> Optional[User]:
        """同步SSO用户到数据库"""
        if not self.user_service:
            return None

        try:
            username = sso_user_info['username']

            # 查找现有用户
            existing_user = await self.user_service.get_by_username(username)

            if existing_user:
                # 更新现有用户
                if self.config.auto_update_users:
                    update_data = {
                        'email': sso_user_info.get('email', ''),
                        'full_name': sso_user_info.get('full_name', ''),
                        'auth_source': sso_user_info['source'],
                        'last_login': datetime.utcnow()
                    }

                    # 更新权限
                    if 'groups' in sso_user_info:
                        update_data['is_superuser'] = any(
                            group in self.config.admin_groups
                            for group in sso_user_info['groups']
                        )

                    await self.user_service.update_user(existing_user.id, update_data)
                    return existing_user
            else:
                # 创建新用户
                if self.config.auto_create_users:
                    # 创建用户对象
                    from ..models.user import User
                    new_user = User(
                        username=username,
                        email=sso_user_info.get('email', ''),
                        full_name=sso_user_info.get('full_name', ''),
                        password_hash='',  # SSO用户不需要本地密码
                        auth_source=sso_user_info['source'],
                        is_active=True,
                        is_superuser=False
                    )

                    # 设置权限
                    if 'groups' in sso_user_info:
                        new_user.is_superuser = any(
                            group in self.config.admin_groups
                            for group in sso_user_info['groups']
                        )

                    self.db.add(new_user)
                    await self.db.commit()
                    await self.db.refresh(new_user)
                    return new_user

            return None

        except Exception as e:
            logger.error(f"同步SSO用户失败: {sso_user_info.get('username')} - {e}")
            return None

    def test_sso_config(self) -> Dict[str, Any]:
        """测试SSO配置"""
        try:
            if not self.config.enabled:
                return {
                    'success': False,
                    'message': 'SSO未启用'
                }

            if self.config.sso_type in ['oauth', 'oidc']:
                # 测试OAuth配置
                if not all([self.config.oauth_client_id, self.config.oauth_auth_url, self.config.oauth_token_url]):
                    return {
                        'success': False,
                        'message': 'OAuth配置不完整'
                    }

                return {
                    'success': True,
                    'message': 'OAuth配置正常',
                    'type': self.config.sso_type,
                    'auth_url': self.config.oauth_auth_url
                }

            elif self.config.sso_type == 'saml':
                # 测试SAML配置
                if not all([self.config.saml_idp_url, self.config.saml_sp_entity_id]):
                    return {
                        'success': False,
                        'message': 'SAML配置不完整'
                    }

                return {
                    'success': True,
                    'message': 'SAML配置正常',
                    'type': self.config.sso_type,
                    'idp_url': self.config.saml_idp_url
                }

            else:
                return {
                    'success': False,
                    'message': f'不支持的SSO类型: {self.config.sso_type}'
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'SSO配置测试失败: {str(e)}'
            }


# 全局SSO服务实例
sso_service = SSOService()
