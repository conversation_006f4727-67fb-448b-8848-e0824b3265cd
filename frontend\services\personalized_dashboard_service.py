"""
Smart Planning 个性化仪表板服务
实现可拖拽组件和用户自定义仪表板布局
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import pandas as pd

@dataclass
class DashboardComponent:
    """仪表板组件"""
    id: str
    type: str  # chart, metric, table, text, image
    title: str
    position: Dict[str, int]  # x, y, width, height
    config: Dict[str, Any]
    data_source: str
    refresh_interval: int  # seconds
    visible: bool = True
    created_at: str = None
    updated_at: str = None

@dataclass
class DashboardLayout:
    """仪表板布局"""
    id: str
    name: str
    description: str
    user_id: str
    components: List[DashboardComponent]
    grid_config: Dict[str, Any]
    is_default: bool = False
    is_public: bool = False
    created_at: str = None
    updated_at: str = None

@dataclass
class ComponentTemplate:
    """组件模板"""
    id: str
    name: str
    type: str
    description: str
    default_config: Dict[str, Any]
    preview_image: str
    category: str
    tags: List[str]

class PersonalizedDashboardService:
    """个性化仪表板服务"""

    def __init__(self, storage_dir: str = "data/dashboards"):
        self.logger = logging.getLogger(__name__)
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)

        # 用户仪表板存储
        self.user_dashboards = {}
        self.component_templates = {}

        # 初始化组件模板
        self._initialize_component_templates()

        # 加载用户仪表板
        self._load_user_dashboards()

        # 配置参数
        self.max_components_per_dashboard = 20
        self.auto_save_interval = 30  # seconds
        self.grid_columns = 12
        self.grid_row_height = 60

    def _initialize_component_templates(self):
        """初始化组件模板"""
        self.component_templates = {
            # 图表组件
            "line_chart": ComponentTemplate(
                id="line_chart",
                name="折线图",
                type="chart",
                description="显示趋势数据的折线图",
                default_config={
                    "chart_type": "line",
                    "x_axis": "date",
                    "y_axis": "value",
                    "color_scheme": "blue",
                    "show_legend": True,
                    "show_grid": True
                },
                preview_image="/static/images/line_chart_preview.png",
                category="图表",
                tags=["趋势", "时间序列", "分析"]
            ),
            "bar_chart": ComponentTemplate(
                id="bar_chart",
                name="柱状图",
                type="chart",
                description="显示分类数据的柱状图",
                default_config={
                    "chart_type": "bar",
                    "orientation": "vertical",
                    "color_scheme": "green",
                    "show_values": True
                },
                preview_image="/static/images/bar_chart_preview.png",
                category="图表",
                tags=["分类", "对比", "统计"]
            ),
            "pie_chart": ComponentTemplate(
                id="pie_chart",
                name="饼图",
                type="chart",
                description="显示比例数据的饼图",
                default_config={
                    "chart_type": "pie",
                    "show_percentages": True,
                    "donut_mode": False
                },
                preview_image="/static/images/pie_chart_preview.png",
                category="图表",
                tags=["比例", "分布", "占比"]
            ),
            "gantt_chart": ComponentTemplate(
                id="gantt_chart",
                name="甘特图",
                type="chart",
                description="显示项目进度的甘特图",
                default_config={
                    "chart_type": "gantt",
                    "time_format": "YYYY-MM-DD",
                    "show_critical_path": True,
                    "color_by": "status"
                },
                preview_image="/static/images/gantt_chart_preview.png",
                category="图表",
                tags=["项目", "进度", "时间线"]
            ),

            # 指标组件
            "kpi_metric": ComponentTemplate(
                id="kpi_metric",
                name="KPI指标",
                type="metric",
                description="显示关键绩效指标",
                default_config={
                    "metric_type": "number",
                    "format": "0,0",
                    "show_trend": True,
                    "trend_period": "7d",
                    "color_scheme": "auto"
                },
                preview_image="/static/images/kpi_metric_preview.png",
                category="指标",
                tags=["KPI", "绩效", "监控"]
            ),
            "gauge_metric": ComponentTemplate(
                id="gauge_metric",
                name="仪表盘指标",
                type="metric",
                description="显示仪表盘样式的指标",
                default_config={
                    "metric_type": "gauge",
                    "min_value": 0,
                    "max_value": 100,
                    "target_value": 80,
                    "color_ranges": [
                        {"min": 0, "max": 60, "color": "red"},
                        {"min": 60, "max": 80, "color": "yellow"},
                        {"min": 80, "max": 100, "color": "green"}
                    ]
                },
                preview_image="/static/images/gauge_metric_preview.png",
                category="指标",
                tags=["仪表盘", "目标", "状态"]
            ),

            # 表格组件
            "data_table": ComponentTemplate(
                id="data_table",
                name="数据表格",
                type="table",
                description="显示结构化数据表格",
                default_config={
                    "show_pagination": True,
                    "page_size": 10,
                    "sortable": True,
                    "filterable": True,
                    "exportable": True
                },
                preview_image="/static/images/data_table_preview.png",
                category="表格",
                tags=["数据", "列表", "详情"]
            ),

            # 文本组件
            "text_widget": ComponentTemplate(
                id="text_widget",
                name="文本组件",
                type="text",
                description="显示自定义文本内容",
                default_config={
                    "content": "请输入文本内容",
                    "font_size": 14,
                    "text_align": "left",
                    "background_color": "transparent"
                },
                preview_image="/static/images/text_widget_preview.png",
                category="内容",
                tags=["文本", "说明", "标题"]
            ),

            # 设备状态组件
            "equipment_status": ComponentTemplate(
                id="equipment_status",
                name="设备状态",
                type="status",
                description="显示设备运行状态",
                default_config={
                    "layout": "grid",
                    "show_details": True,
                    "auto_refresh": True,
                    "alert_threshold": 0.8
                },
                preview_image="/static/images/equipment_status_preview.png",
                category="监控",
                tags=["设备", "状态", "监控"]
            ),

            # 生产计划组件
            "production_plan": ComponentTemplate(
                id="production_plan",
                name="生产计划",
                type="plan",
                description="显示生产计划和进度",
                default_config={
                    "view_mode": "timeline",
                    "time_range": "week",
                    "show_progress": True,
                    "group_by": "equipment"
                },
                preview_image="/static/images/production_plan_preview.png",
                category="生产",
                tags=["计划", "进度", "排程"]
            )
        }

    def _load_user_dashboards(self):
        """加载用户仪表板"""
        try:
            dashboard_files = list(self.storage_dir.glob("*.json"))
            for file_path in dashboard_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        dashboard_data = json.load(f)

                    # 转换为DashboardLayout对象
                    components = [
                        DashboardComponent(**comp) for comp in dashboard_data.get('components', [])
                    ]
                    dashboard_data['components'] = components

                    dashboard = DashboardLayout(**dashboard_data)

                    user_id = dashboard.user_id
                    if user_id not in self.user_dashboards:
                        self.user_dashboards[user_id] = []

                    self.user_dashboards[user_id].append(dashboard)

                except Exception as e:
                    self.logger.error(f"加载仪表板文件失败 {file_path}: {str(e)}")

        except Exception as e:
            self.logger.error(f"加载用户仪表板失败: {str(e)}")

    def get_component_templates(self, category: str = None) -> List[ComponentTemplate]:
        """获取组件模板"""
        templates = list(self.component_templates.values())

        if category:
            templates = [t for t in templates if t.category == category]

        return templates

    def get_template_categories(self) -> List[str]:
        """获取模板分类"""
        categories = set(template.category for template in self.component_templates.values())
        return sorted(list(categories))

    def create_dashboard(self, user_id: str, name: str, description: str = "") -> DashboardLayout:
        """创建新仪表板"""
        dashboard_id = f"dashboard_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        dashboard = DashboardLayout(
            id=dashboard_id,
            name=name,
            description=description,
            user_id=user_id,
            components=[],
            grid_config={
                "columns": self.grid_columns,
                "row_height": self.grid_row_height,
                "margin": [10, 10],
                "container_padding": [10, 10]
            },
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

        # 添加到用户仪表板列表
        if user_id not in self.user_dashboards:
            self.user_dashboards[user_id] = []

        self.user_dashboards[user_id].append(dashboard)

        # 保存到文件
        self._save_dashboard(dashboard)

        self.logger.info(f"创建仪表板: {dashboard_id} for user {user_id}")
        return dashboard

    def get_user_dashboards(self, user_id: str) -> List[DashboardLayout]:
        """获取用户仪表板列表"""
        return self.user_dashboards.get(user_id, [])

    def get_dashboard(self, user_id: str, dashboard_id: str) -> Optional[DashboardLayout]:
        """获取特定仪表板"""
        user_dashboards = self.user_dashboards.get(user_id, [])
        for dashboard in user_dashboards:
            if dashboard.id == dashboard_id:
                return dashboard
        return None

    def add_component(self, user_id: str, dashboard_id: str,
                     template_id: str, position: Dict[str, int],
                     custom_config: Dict[str, Any] = None) -> Optional[DashboardComponent]:
        """添加组件到仪表板"""
        dashboard = self.get_dashboard(user_id, dashboard_id)
        if not dashboard:
            return None

        template = self.component_templates.get(template_id)
        if not template:
            return None

        # 检查组件数量限制
        if len(dashboard.components) >= self.max_components_per_dashboard:
            raise ValueError(f"仪表板组件数量已达到上限 {self.max_components_per_dashboard}")

        # 创建新组件
        component_id = f"comp_{dashboard_id}_{len(dashboard.components) + 1}_{datetime.now().strftime('%H%M%S')}"

        # 合并配置
        config = template.default_config.copy()
        if custom_config:
            config.update(custom_config)

        component = DashboardComponent(
            id=component_id,
            type=template.type,
            title=template.name,
            position=position,
            config=config,
            data_source="default",
            refresh_interval=60,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

        dashboard.components.append(component)
        dashboard.updated_at = datetime.now().isoformat()

        # 保存仪表板
        self._save_dashboard(dashboard)

        self.logger.info(f"添加组件 {component_id} 到仪表板 {dashboard_id}")
        return component

    def update_component(self, user_id: str, dashboard_id: str, component_id: str,
                        updates: Dict[str, Any]) -> bool:
        """更新组件"""
        dashboard = self.get_dashboard(user_id, dashboard_id)
        if not dashboard:
            return False

        # 查找组件
        component = None
        for comp in dashboard.components:
            if comp.id == component_id:
                component = comp
                break

        if not component:
            return False

        # 更新组件属性
        if 'title' in updates:
            component.title = updates['title']
        if 'position' in updates:
            component.position.update(updates['position'])
        if 'config' in updates:
            component.config.update(updates['config'])
        if 'data_source' in updates:
            component.data_source = updates['data_source']
        if 'refresh_interval' in updates:
            component.refresh_interval = updates['refresh_interval']
        if 'visible' in updates:
            component.visible = updates['visible']

        component.updated_at = datetime.now().isoformat()
        dashboard.updated_at = datetime.now().isoformat()

        # 保存仪表板
        self._save_dashboard(dashboard)

        self.logger.info(f"更新组件 {component_id} 在仪表板 {dashboard_id}")
        return True

    def remove_component(self, user_id: str, dashboard_id: str, component_id: str) -> bool:
        """移除组件"""
        dashboard = self.get_dashboard(user_id, dashboard_id)
        if not dashboard:
            return False

        # 查找并移除组件
        original_count = len(dashboard.components)
        dashboard.components = [comp for comp in dashboard.components if comp.id != component_id]

        if len(dashboard.components) == original_count:
            return False  # 组件未找到

        dashboard.updated_at = datetime.now().isoformat()

        # 保存仪表板
        self._save_dashboard(dashboard)

        self.logger.info(f"移除组件 {component_id} 从仪表板 {dashboard_id}")
        return True

    def update_layout(self, user_id: str, dashboard_id: str,
                     layout_data: List[Dict[str, Any]]) -> bool:
        """更新仪表板布局"""
        dashboard = self.get_dashboard(user_id, dashboard_id)
        if not dashboard:
            return False

        # 更新组件位置
        for layout_item in layout_data:
            component_id = layout_item.get('i')  # react-grid-layout使用'i'作为id
            position = {
                'x': layout_item.get('x', 0),
                'y': layout_item.get('y', 0),
                'width': layout_item.get('w', 1),
                'height': layout_item.get('h', 1)
            }

            # 查找并更新组件位置
            for component in dashboard.components:
                if component.id == component_id:
                    component.position = position
                    component.updated_at = datetime.now().isoformat()
                    break

        dashboard.updated_at = datetime.now().isoformat()

        # 保存仪表板
        self._save_dashboard(dashboard)

        self.logger.info(f"更新仪表板布局 {dashboard_id}")
        return True

    def duplicate_dashboard(self, user_id: str, dashboard_id: str, new_name: str) -> Optional[DashboardLayout]:
        """复制仪表板"""
        original_dashboard = self.get_dashboard(user_id, dashboard_id)
        if not original_dashboard:
            return None

        # 创建新仪表板
        new_dashboard_id = f"dashboard_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 复制组件
        new_components = []
        for comp in original_dashboard.components:
            new_comp_id = f"comp_{new_dashboard_id}_{len(new_components) + 1}_{datetime.now().strftime('%H%M%S')}"
            new_component = DashboardComponent(
                id=new_comp_id,
                type=comp.type,
                title=comp.title,
                position=comp.position.copy(),
                config=comp.config.copy(),
                data_source=comp.data_source,
                refresh_interval=comp.refresh_interval,
                visible=comp.visible,
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat()
            )
            new_components.append(new_component)

        new_dashboard = DashboardLayout(
            id=new_dashboard_id,
            name=new_name,
            description=f"复制自: {original_dashboard.name}",
            user_id=user_id,
            components=new_components,
            grid_config=original_dashboard.grid_config.copy(),
            is_default=False,
            is_public=False,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

        # 添加到用户仪表板列表
        if user_id not in self.user_dashboards:
            self.user_dashboards[user_id] = []

        self.user_dashboards[user_id].append(new_dashboard)

        # 保存到文件
        self._save_dashboard(new_dashboard)

        self.logger.info(f"复制仪表板 {dashboard_id} 为 {new_dashboard_id}")
        return new_dashboard

    def delete_dashboard(self, user_id: str, dashboard_id: str) -> bool:
        """删除仪表板"""
        user_dashboards = self.user_dashboards.get(user_id, [])
        original_count = len(user_dashboards)

        # 移除仪表板
        self.user_dashboards[user_id] = [
            dashboard for dashboard in user_dashboards
            if dashboard.id != dashboard_id
        ]

        if len(self.user_dashboards[user_id]) == original_count:
            return False  # 仪表板未找到

        # 删除文件
        file_path = self.storage_dir / f"{dashboard_id}.json"
        if file_path.exists():
            file_path.unlink()

        self.logger.info(f"删除仪表板 {dashboard_id}")
        return True

    def set_default_dashboard(self, user_id: str, dashboard_id: str) -> bool:
        """设置默认仪表板"""
        user_dashboards = self.user_dashboards.get(user_id, [])

        # 清除所有默认标记
        for dashboard in user_dashboards:
            dashboard.is_default = False

        # 设置新的默认仪表板
        target_dashboard = None
        for dashboard in user_dashboards:
            if dashboard.id == dashboard_id:
                dashboard.is_default = True
                target_dashboard = dashboard
                break

        if not target_dashboard:
            return False

        # 保存所有相关仪表板
        for dashboard in user_dashboards:
            self._save_dashboard(dashboard)

        self.logger.info(f"设置默认仪表板 {dashboard_id} for user {user_id}")
        return True

    def get_component_data(self, user_id: str, dashboard_id: str, component_id: str) -> Dict[str, Any]:
        """获取组件数据"""
        dashboard = self.get_dashboard(user_id, dashboard_id)
        if not dashboard:
            return {}

        # 查找组件
        component = None
        for comp in dashboard.components:
            if comp.id == component_id:
                component = comp
                break

        if not component:
            return {}

        # 根据组件类型和数据源获取数据
        return self._fetch_component_data(component)

    def _fetch_component_data(self, component: DashboardComponent) -> Dict[str, Any]:
        """获取组件数据（模拟数据）"""
        try:
            if component.type == "chart":
                return self._generate_chart_data(component)
            elif component.type == "metric":
                return self._generate_metric_data(component)
            elif component.type == "table":
                return self._generate_table_data(component)
            elif component.type == "status":
                return self._generate_status_data(component)
            elif component.type == "plan":
                return self._generate_plan_data(component)
            else:
                return {"data": [], "message": "暂无数据"}

        except Exception as e:
            self.logger.error(f"获取组件数据失败: {str(e)}")
            return {"error": str(e)}

    def _generate_chart_data(self, component: DashboardComponent) -> Dict[str, Any]:
        """生成图表数据"""
        import random
        from datetime import datetime, timedelta

        chart_type = component.config.get("chart_type", "line")

        if chart_type == "line":
            # 生成时间序列数据
            dates = [(datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d") for i in range(30, 0, -1)]
            values = [random.randint(50, 150) for _ in range(30)]
            return {
                "data": [{"date": date, "value": value} for date, value in zip(dates, values)],
                "type": "line"
            }
        elif chart_type == "bar":
            # 生成分类数据
            categories = ["产品A", "产品B", "产品C", "产品D", "产品E"]
            values = [random.randint(20, 100) for _ in range(5)]
            return {
                "data": [{"category": cat, "value": val} for cat, val in zip(categories, values)],
                "type": "bar"
            }
        elif chart_type == "pie":
            # 生成饼图数据
            categories = ["正常", "维护", "故障", "停机"]
            values = [60, 25, 10, 5]
            return {
                "data": [{"category": cat, "value": val} for cat, val in zip(categories, values)],
                "type": "pie"
            }
        elif chart_type == "gantt":
            # 生成甘特图数据
            tasks = [
                {"task": "订单A", "start": "2024-01-01", "end": "2024-01-05", "resource": "设备1"},
                {"task": "订单B", "start": "2024-01-03", "end": "2024-01-08", "resource": "设备2"},
                {"task": "订单C", "start": "2024-01-06", "end": "2024-01-10", "resource": "设备1"},
            ]
            return {"data": tasks, "type": "gantt"}

        return {"data": [], "type": chart_type}

    def _generate_metric_data(self, component: DashboardComponent) -> Dict[str, Any]:
        """生成指标数据"""
        import random

        metric_type = component.config.get("metric_type", "number")

        if metric_type == "number":
            return {
                "value": random.randint(80, 120),
                "trend": random.choice(["up", "down", "stable"]),
                "trend_value": random.randint(1, 10),
                "unit": "件"
            }
        elif metric_type == "gauge":
            return {
                "value": random.randint(60, 95),
                "min_value": component.config.get("min_value", 0),
                "max_value": component.config.get("max_value", 100),
                "target_value": component.config.get("target_value", 80),
                "unit": "%"
            }

        return {"value": 0}

    def _generate_table_data(self, component: DashboardComponent) -> Dict[str, Any]:
        """生成表格数据"""
        import random

        # 生成模拟表格数据
        data = []
        for i in range(10):
            data.append({
                "id": i + 1,
                "订单号": f"ORD{1000 + i}",
                "产品": random.choice(["产品A", "产品B", "产品C"]),
                "数量": random.randint(10, 100),
                "状态": random.choice(["进行中", "已完成", "待开始"]),
                "进度": f"{random.randint(0, 100)}%"
            })

        return {
            "data": data,
            "columns": ["订单号", "产品", "数量", "状态", "进度"],
            "total": len(data)
        }

    def _generate_status_data(self, component: DashboardComponent) -> Dict[str, Any]:
        """生成状态数据"""
        import random

        # 生成设备状态数据
        equipment_list = []
        for i in range(6):
            equipment_list.append({
                "id": f"EQ{i+1:03d}",
                "name": f"设备{i+1}",
                "status": random.choice(["运行中", "维护中", "故障", "停机"]),
                "utilization": random.randint(60, 95),
                "temperature": random.randint(60, 80),
                "pressure": random.randint(2, 5)
            })

        return {"equipment": equipment_list}

    def _generate_plan_data(self, component: DashboardComponent) -> Dict[str, Any]:
        """生成计划数据"""
        import random
        from datetime import datetime, timedelta

        # 生成生产计划数据
        plans = []
        for i in range(5):
            start_date = datetime.now() + timedelta(days=i)
            end_date = start_date + timedelta(hours=random.randint(4, 12))

            plans.append({
                "id": f"PLAN{i+1:03d}",
                "order": f"订单{i+1}",
                "product": random.choice(["产品A", "产品B", "产品C"]),
                "equipment": f"设备{random.randint(1, 3)}",
                "start_time": start_date.strftime("%Y-%m-%d %H:%M"),
                "end_time": end_date.strftime("%Y-%m-%d %H:%M"),
                "progress": random.randint(0, 100),
                "status": random.choice(["计划中", "进行中", "已完成"])
            })

        return {"plans": plans}

    def _save_dashboard(self, dashboard: DashboardLayout):
        """保存仪表板到文件"""
        try:
            file_path = self.storage_dir / f"{dashboard.id}.json"

            # 转换为字典格式
            dashboard_dict = asdict(dashboard)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(dashboard_dict, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"保存仪表板失败: {str(e)}")

    def export_dashboard(self, user_id: str, dashboard_id: str) -> Optional[Dict[str, Any]]:
        """导出仪表板配置"""
        dashboard = self.get_dashboard(user_id, dashboard_id)
        if not dashboard:
            return None

        return asdict(dashboard)

    def import_dashboard(self, user_id: str, dashboard_data: Dict[str, Any]) -> Optional[DashboardLayout]:
        """导入仪表板配置"""
        try:
            # 生成新的ID
            new_dashboard_id = f"dashboard_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 更新ID和用户信息
            dashboard_data['id'] = new_dashboard_id
            dashboard_data['user_id'] = user_id
            dashboard_data['created_at'] = datetime.now().isoformat()
            dashboard_data['updated_at'] = datetime.now().isoformat()

            # 更新组件ID
            for i, comp_data in enumerate(dashboard_data.get('components', [])):
                comp_data['id'] = f"comp_{new_dashboard_id}_{i+1}_{datetime.now().strftime('%H%M%S')}"
                comp_data['created_at'] = datetime.now().isoformat()
                comp_data['updated_at'] = datetime.now().isoformat()

            # 转换为对象
            components = [DashboardComponent(**comp) for comp in dashboard_data.get('components', [])]
            dashboard_data['components'] = components

            dashboard = DashboardLayout(**dashboard_data)

            # 添加到用户仪表板列表
            if user_id not in self.user_dashboards:
                self.user_dashboards[user_id] = []

            self.user_dashboards[user_id].append(dashboard)

            # 保存到文件
            self._save_dashboard(dashboard)

            self.logger.info(f"导入仪表板 {new_dashboard_id} for user {user_id}")
            return dashboard

        except Exception as e:
            self.logger.error(f"导入仪表板失败: {str(e)}")
            return None

    def get_dashboard_statistics(self, user_id: str) -> Dict[str, Any]:
        """获取仪表板统计信息"""
        user_dashboards = self.user_dashboards.get(user_id, [])

        total_dashboards = len(user_dashboards)
        total_components = sum(len(dashboard.components) for dashboard in user_dashboards)

        # 组件类型统计
        component_types = {}
        for dashboard in user_dashboards:
            for component in dashboard.components:
                comp_type = component.type
                component_types[comp_type] = component_types.get(comp_type, 0) + 1

        # 最近活动
        recent_activity = []
        for dashboard in user_dashboards:
            if dashboard.updated_at:
                recent_activity.append({
                    "dashboard_name": dashboard.name,
                    "updated_at": dashboard.updated_at,
                    "action": "更新"
                })

        # 按更新时间排序
        recent_activity.sort(key=lambda x: x["updated_at"], reverse=True)
        recent_activity = recent_activity[:5]  # 最近5条

        return {
            "total_dashboards": total_dashboards,
            "total_components": total_components,
            "component_types": component_types,
            "recent_activity": recent_activity,
            "templates_available": len(self.component_templates)
        }


# 创建全局实例
personalized_dashboard_service = PersonalizedDashboardService()
