"""
数据分析页面
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import random

from config.settings import AppConfig
from config.theme import apply_plotly_theme
from utils.auth import check_authentication, require_permission
from utils.api_client import APIClient

# 页面配置
st.set_page_config(
    page_title="数据分析 - Smart Planning",
    page_icon="📊",
    layout="wide"
)

# 检查认证
if not check_authentication():
    st.error("请先登录")
    st.stop()

# 检查权限
if not require_permission("analytics.view"):
    st.error("权限不足")
    st.stop()

# 初始化API客户端
if 'api_client' not in st.session_state:
    st.session_state.api_client = APIClient(AppConfig.API_BASE_URL)

api_client = st.session_state.api_client

# 初始化会话状态
if 'selected_analysis_type' not in st.session_state:
    st.session_state.selected_analysis_type = "效率分析"

if 'analysis_time_range' not in st.session_state:
    st.session_state.analysis_time_range = "30days"

if 'custom_filters' not in st.session_state:
    st.session_state.custom_filters = {}

# 页面标题
st.title("📊 数据分析")
st.markdown("### 深度分析生产数据，发现趋势和优化机会")

# 侧边栏 - 分析配置
with st.sidebar:
    st.markdown("### ⚙️ 分析配置")

    # 分析类型选择
    analysis_type = st.selectbox(
        "分析类型",
        ["效率分析", "质量分析", "成本分析", "设备性能", "预测分析", "对比分析"],
        index=0
    )
    st.session_state.selected_analysis_type = analysis_type

    # 时间范围选择
    time_range = st.selectbox(
        "时间范围",
        ["7days", "30days", "90days", "6months", "1year", "custom"],
        index=1,
        format_func=lambda x: {
            "7days": "最近7天",
            "30days": "最近30天",
            "90days": "最近90天",
            "6months": "最近6个月",
            "1year": "最近1年",
            "custom": "自定义"
        }[x]
    )
    st.session_state.analysis_time_range = time_range

    if time_range == "custom":
        start_date = st.date_input("开始日期", value=datetime.now().date() - timedelta(days=30))
        end_date = st.date_input("结束日期", value=datetime.now().date())
        st.session_state.custom_date_range = (start_date, end_date)

    # 数据维度选择
    st.markdown("#### 📈 数据维度")

    if analysis_type == "效率分析":
        dimensions = st.multiselect(
            "效率维度",
            ["设备利用率", "人员效率", "生产效率", "OEE", "产能利用率"],
            default=["设备利用率", "生产效率", "OEE"]
        )
    elif analysis_type == "质量分析":
        dimensions = st.multiselect(
            "质量维度",
            ["合格率", "返工率", "废品率", "客户投诉", "质量成本"],
            default=["合格率", "返工率", "废品率"]
        )
    elif analysis_type == "成本分析":
        dimensions = st.multiselect(
            "成本维度",
            ["材料成本", "人工成本", "设备成本", "能耗成本", "质量成本"],
            default=["材料成本", "人工成本", "设备成本"]
        )
    elif analysis_type == "设备性能":
        dimensions = st.multiselect(
            "设备类型",
            ["数控机床", "装配线", "检测设备", "包装设备", "物流设备"],
            default=["数控机床", "装配线"]
        )
    else:
        dimensions = []

    st.session_state.selected_dimensions = dimensions

    st.markdown("---")

    # 筛选条件
    st.markdown("### 🔍 筛选条件")

    # 产品类型筛选
    product_filter = st.multiselect(
        "产品类型",
        ["精密零件", "装配件", "标准件", "定制件"],
        default=[]
    )

    # 生产线筛选
    line_filter = st.multiselect(
        "生产线",
        ["生产线A", "生产线B", "生产线C", "生产线D"],
        default=[]
    )

    # 班次筛选
    shift_filter = st.multiselect(
        "班次",
        ["早班", "中班", "夜班"],
        default=[]
    )

    st.session_state.custom_filters = {
        "product_types": product_filter,
        "production_lines": line_filter,
        "shifts": shift_filter
    }

    st.markdown("---")

    # 快速操作
    st.markdown("### ⚡ 快速操作")

    if st.button("🔄 刷新数据", use_container_width=True):
        st.rerun()

    if st.button("📊 生成报告", use_container_width=True):
        generate_analysis_report()

    if st.button("📋 导出数据", use_container_width=True):
        export_analysis_data()

    if require_permission("analytics.advanced"):
        if st.button("🔮 高级分析", use_container_width=True):
            st.session_state.show_advanced_analysis = True
            st.rerun()

# 分析页面函数
def show_efficiency_analysis():
    """显示效率分析"""
    st.markdown("#### 📈 效率分析")

    # 获取效率分析数据
    efficiency_data = get_efficiency_analysis_data()

    if efficiency_data and efficiency_data.get("success"):
        data = efficiency_data.get("data", {})

        # 效率概览指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            current_efficiency = data.get("overall_efficiency", 0)
            target_efficiency = data.get("target_efficiency", 85)
            delta = current_efficiency - target_efficiency
            st.metric(
                "整体效率",
                f"{current_efficiency:.1f}%",
                delta=f"{delta:+.1f}%",
                delta_color="normal" if delta >= 0 else "inverse"
            )

        with col2:
            equipment_utilization = data.get("equipment_utilization", 0)
            st.metric(
                "设备利用率",
                f"{equipment_utilization:.1f}%",
                delta=f"{equipment_utilization - 80:+.1f}%"
            )

        with col3:
            oee_score = data.get("oee_score", 0)
            st.metric(
                "OEE得分",
                f"{oee_score:.1f}%",
                delta=f"{oee_score - 75:+.1f}%"
            )

        with col4:
            productivity_index = data.get("productivity_index", 0)
            st.metric(
                "生产力指数",
                f"{productivity_index:.2f}",
                delta=f"{productivity_index - 1.0:+.2f}"
            )

        st.markdown("---")

        # 效率趋势图表
        col1, col2 = st.columns(2)

        with col1:
            # 效率趋势线图
            trend_data = data.get("efficiency_trends", [])
            if trend_data:
                df_trends = pd.DataFrame(trend_data)

                fig_trends = px.line(
                    df_trends,
                    x="date",
                    y="value",
                    color="metric",
                    title="📈 效率趋势分析",
                    markers=True
                )
                fig_trends.update_layout(
                    xaxis_title="日期",
                    yaxis_title="效率 (%)",
                    legend_title="指标"
                )
                fig_trends = apply_plotly_theme(fig_trends)
                st.plotly_chart(fig_trends, use_container_width=True)
            else:
                st.info("暂无趋势数据")

        with col2:
            # 效率分布饼图
            distribution_data = data.get("efficiency_distribution", [])
            if distribution_data:
                df_dist = pd.DataFrame(distribution_data)

                fig_pie = px.pie(
                    df_dist,
                    values="percentage",
                    names="category",
                    title="📊 效率分布"
                )
                fig_pie = apply_plotly_theme(fig_pie)
                st.plotly_chart(fig_pie, use_container_width=True)
            else:
                st.info("暂无分布数据")

        # 详细分析表格
        st.markdown("##### 📋 详细效率分析")

        detailed_data = data.get("detailed_analysis", [])
        if detailed_data:
            df_detailed = pd.DataFrame(detailed_data)

            # 添加样式
            def highlight_efficiency(val):
                if val >= 90:
                    return 'background-color: #d4edda'
                elif val >= 80:
                    return 'background-color: #fff3cd'
                else:
                    return 'background-color: #f8d7da'

            styled_df = df_detailed.style.applymap(
                highlight_efficiency,
                subset=['efficiency']
            )

            st.dataframe(styled_df, use_container_width=True)
        else:
            st.info("暂无详细分析数据")

        # 改进建议
        st.markdown("##### 💡 改进建议")

        recommendations = data.get("recommendations", [])
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                st.markdown(f"**{i}. {rec.get('title', '建议')}**")
                st.markdown(f"   {rec.get('description', '无描述')}")
                if rec.get('impact'):
                    st.markdown(f"   *预期影响: {rec['impact']}*")
                st.markdown("")
        else:
            st.info("暂无改进建议")

    else:
        st.error("获取效率分析数据失败")


def show_quality_analysis():
    """显示质量分析"""
    st.markdown("#### 🎯 质量分析")

    # 获取质量分析数据
    quality_data = get_quality_analysis_data()

    if quality_data and quality_data.get("success"):
        data = quality_data.get("data", {})

        # 质量概览指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            pass_rate = data.get("overall_pass_rate", 0)
            target_rate = data.get("target_pass_rate", 99)
            delta = pass_rate - target_rate
            st.metric(
                "整体合格率",
                f"{pass_rate:.2f}%",
                delta=f"{delta:+.2f}%",
                delta_color="normal" if delta >= 0 else "inverse"
            )

        with col2:
            rework_rate = data.get("rework_rate", 0)
            st.metric(
                "返工率",
                f"{rework_rate:.2f}%",
                delta=f"{rework_rate - 2.0:+.2f}%",
                delta_color="inverse" if rework_rate > 2.0 else "normal"
            )

        with col3:
            defect_rate = data.get("defect_rate", 0)
            st.metric(
                "废品率",
                f"{defect_rate:.2f}%",
                delta=f"{defect_rate - 1.0:+.2f}%",
                delta_color="inverse" if defect_rate > 1.0 else "normal"
            )

        with col4:
            customer_complaints = data.get("customer_complaints", 0)
            st.metric(
                "客户投诉",
                customer_complaints,
                delta=f"{customer_complaints - data.get('last_period_complaints', 0):+d}",
                delta_color="inverse" if customer_complaints > 0 else "normal"
            )

        st.markdown("---")

        # 质量趋势和分析图表
        col1, col2 = st.columns(2)

        with col1:
            # 质量趋势图
            quality_trends = data.get("quality_trends", [])
            if quality_trends:
                df_quality = pd.DataFrame(quality_trends)

                fig_quality = px.line(
                    df_quality,
                    x="date",
                    y="value",
                    color="metric",
                    title="📈 质量趋势分析",
                    markers=True
                )
                fig_quality.update_layout(
                    xaxis_title="日期",
                    yaxis_title="百分比 (%)",
                    legend_title="质量指标"
                )
                fig_quality = apply_plotly_theme(fig_quality)
                st.plotly_chart(fig_quality, use_container_width=True)
            else:
                st.info("暂无质量趋势数据")

        with col2:
            # 缺陷类型分析
            defect_types = data.get("defect_analysis", [])
            if defect_types:
                df_defects = pd.DataFrame(defect_types)

                fig_defects = px.bar(
                    df_defects,
                    x="defect_type",
                    y="count",
                    title="📊 缺陷类型分析",
                    color="count",
                    color_continuous_scale="Reds"
                )
                fig_defects.update_layout(
                    xaxis_title="缺陷类型",
                    yaxis_title="数量",
                    showlegend=False
                )
                fig_defects = apply_plotly_theme(fig_defects)
                st.plotly_chart(fig_defects, use_container_width=True)
            else:
                st.info("暂无缺陷分析数据")

        # 质量控制图
        st.markdown("##### 📊 质量控制图")

        control_chart_data = data.get("control_chart", [])
        if control_chart_data:
            df_control = pd.DataFrame(control_chart_data)

            fig_control = go.Figure()

            # 添加数据点
            fig_control.add_trace(go.Scatter(
                x=df_control['sample'],
                y=df_control['value'],
                mode='lines+markers',
                name='测量值',
                line=dict(color='blue')
            ))

            # 添加控制线
            ucl = df_control['ucl'].iloc[0] if 'ucl' in df_control.columns else df_control['value'].max()
            lcl = df_control['lcl'].iloc[0] if 'lcl' in df_control.columns else df_control['value'].min()
            center_line = df_control['center_line'].iloc[0] if 'center_line' in df_control.columns else df_control['value'].mean()

            fig_control.add_hline(y=ucl, line_dash="dash", line_color="red", annotation_text="UCL")
            fig_control.add_hline(y=center_line, line_dash="dash", line_color="green", annotation_text="中心线")
            fig_control.add_hline(y=lcl, line_dash="dash", line_color="red", annotation_text="LCL")

            fig_control.update_layout(
                title="质量控制图",
                xaxis_title="样本",
                yaxis_title="测量值"
            )
            fig_control = apply_plotly_theme(fig_control)
            st.plotly_chart(fig_control, use_container_width=True)
        else:
            st.info("暂无控制图数据")

    else:
        st.error("获取质量分析数据失败")


def show_cost_analysis():
    """显示成本分析"""
    st.markdown("#### 💰 成本分析")

    # 获取成本分析数据
    cost_data = get_cost_analysis_data()

    if cost_data and cost_data.get("success"):
        data = cost_data.get("data", {})

        # 成本概览指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_cost = data.get("total_cost", 0)
            last_period_cost = data.get("last_period_cost", 0)
            delta = total_cost - last_period_cost
            st.metric(
                "总成本",
                f"¥{total_cost:,.0f}",
                delta=f"¥{delta:+,.0f}",
                delta_color="inverse" if delta > 0 else "normal"
            )

        with col2:
            unit_cost = data.get("unit_cost", 0)
            target_unit_cost = data.get("target_unit_cost", 0)
            delta = unit_cost - target_unit_cost
            st.metric(
                "单位成本",
                f"¥{unit_cost:.2f}",
                delta=f"¥{delta:+.2f}",
                delta_color="inverse" if delta > 0 else "normal"
            )

        with col3:
            cost_efficiency = data.get("cost_efficiency", 0)
            st.metric(
                "成本效率",
                f"{cost_efficiency:.1f}%",
                delta=f"{cost_efficiency - 85:+.1f}%"
            )

        with col4:
            cost_variance = data.get("cost_variance", 0)
            st.metric(
                "成本偏差",
                f"{cost_variance:+.1f}%",
                delta_color="inverse" if abs(cost_variance) > 5 else "normal"
            )

        st.markdown("---")

        # 成本结构和趋势
        col1, col2 = st.columns(2)

        with col1:
            # 成本结构饼图
            cost_breakdown = data.get("cost_breakdown", [])
            if cost_breakdown:
                df_breakdown = pd.DataFrame(cost_breakdown)

                fig_pie = px.pie(
                    df_breakdown,
                    values="amount",
                    names="category",
                    title="📊 成本结构分析"
                )
                fig_pie = apply_plotly_theme(fig_pie)
                st.plotly_chart(fig_pie, use_container_width=True)
            else:
                st.info("暂无成本结构数据")

        with col2:
            # 成本趋势图
            cost_trends = data.get("cost_trends", [])
            if cost_trends:
                df_trends = pd.DataFrame(cost_trends)

                fig_trends = px.line(
                    df_trends,
                    x="date",
                    y="amount",
                    color="category",
                    title="📈 成本趋势分析",
                    markers=True
                )
                fig_trends.update_layout(
                    xaxis_title="日期",
                    yaxis_title="成本 (元)",
                    legend_title="成本类别"
                )
                fig_trends = apply_plotly_theme(fig_trends)
                st.plotly_chart(fig_trends, use_container_width=True)
            else:
                st.info("暂无成本趋势数据")

        # 成本对比分析
        st.markdown("##### 📊 成本对比分析")

        comparison_data = data.get("cost_comparison", [])
        if comparison_data:
            df_comparison = pd.DataFrame(comparison_data)

            fig_comparison = px.bar(
                df_comparison,
                x="category",
                y=["current_period", "last_period"],
                title="成本期间对比",
                barmode="group"
            )
            fig_comparison.update_layout(
                xaxis_title="成本类别",
                yaxis_title="成本 (元)",
                legend_title="期间"
            )
            fig_comparison = apply_plotly_theme(fig_comparison)
            st.plotly_chart(fig_comparison, use_container_width=True)
        else:
            st.info("暂无对比数据")

    else:
        st.error("获取成本分析数据失败")


def show_equipment_performance_analysis():
    """显示设备性能分析"""
    st.markdown("#### ⚙️ 设备性能分析")

    # 获取设备性能数据
    equipment_data = get_equipment_performance_data()

    if equipment_data and equipment_data.get("success"):
        data = equipment_data.get("data", {})

        # 设备性能概览
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            avg_utilization = data.get("average_utilization", 0)
            st.metric(
                "平均利用率",
                f"{avg_utilization:.1f}%",
                delta=f"{avg_utilization - 80:+.1f}%"
            )

        with col2:
            avg_efficiency = data.get("average_efficiency", 0)
            st.metric(
                "平均效率",
                f"{avg_efficiency:.1f}%",
                delta=f"{avg_efficiency - 85:+.1f}%"
            )

        with col3:
            mtbf = data.get("mtbf_hours", 0)
            st.metric(
                "平均故障间隔",
                f"{mtbf:.0f}小时",
                delta=f"{mtbf - 200:+.0f}小时"
            )

        with col4:
            mttr = data.get("mttr_hours", 0)
            st.metric(
                "平均修复时间",
                f"{mttr:.1f}小时",
                delta=f"{mttr - 4:+.1f}小时",
                delta_color="inverse" if mttr > 4 else "normal"
            )

        st.markdown("---")

        # 设备性能图表
        col1, col2 = st.columns(2)

        with col1:
            # 设备利用率对比
            utilization_data = data.get("equipment_utilization", [])
            if utilization_data:
                df_util = pd.DataFrame(utilization_data)

                fig_util = px.bar(
                    df_util,
                    x="equipment_name",
                    y="utilization",
                    title="📊 设备利用率对比",
                    color="utilization",
                    color_continuous_scale="RdYlGn"
                )
                fig_util.update_layout(
                    xaxis_title="设备",
                    yaxis_title="利用率 (%)",
                    showlegend=False
                )
                fig_util = apply_plotly_theme(fig_util)
                st.plotly_chart(fig_util, use_container_width=True)
            else:
                st.info("暂无设备利用率数据")

        with col2:
            # 设备故障分析
            failure_data = data.get("failure_analysis", [])
            if failure_data:
                df_failure = pd.DataFrame(failure_data)

                fig_failure = px.pie(
                    df_failure,
                    values="count",
                    names="failure_type",
                    title="📊 故障类型分析"
                )
                fig_failure = apply_plotly_theme(fig_failure)
                st.plotly_chart(fig_failure, use_container_width=True)
            else:
                st.info("暂无故障分析数据")

        # 设备性能热力图
        st.markdown("##### 🔥 设备性能热力图")

        heatmap_data = data.get("performance_heatmap", [])
        if heatmap_data:
            df_heatmap = pd.DataFrame(heatmap_data)

            # 创建热力图
            fig_heatmap = px.imshow(
                df_heatmap.pivot(index="equipment", columns="metric", values="value"),
                title="设备性能热力图",
                color_continuous_scale="RdYlGn",
                aspect="auto"
            )
            fig_heatmap.update_layout(
                xaxis_title="性能指标",
                yaxis_title="设备"
            )
            fig_heatmap = apply_plotly_theme(fig_heatmap)
            st.plotly_chart(fig_heatmap, use_container_width=True)
        else:
            st.info("暂无热力图数据")

    else:
        st.error("获取设备性能数据失败")


def show_predictive_analysis():
    """显示预测分析"""
    st.markdown("#### 🔮 预测分析")

    # 预测类型选择
    col1, col2, col3 = st.columns(3)

    with col1:
        prediction_type = st.selectbox(
            "预测类型",
            ["需求预测", "产能预测", "故障预测", "成本预测", "质量预测"]
        )

    with col2:
        time_horizon = st.selectbox(
            "预测周期",
            ["7days", "30days", "90days", "6months"],
            format_func=lambda x: {
                "7days": "未来7天",
                "30days": "未来30天",
                "90days": "未来90天",
                "6months": "未来6个月"
            }[x]
        )

    with col3:
        confidence_level = st.selectbox(
            "置信度",
            [0.8, 0.9, 0.95],
            format_func=lambda x: f"{x*100:.0f}%"
        )

    if st.button("🚀 开始预测分析", use_container_width=True):
        with st.spinner("正在进行预测分析..."):
            prediction_data = get_predictive_analysis_data(prediction_type, time_horizon, confidence_level)

            if prediction_data and prediction_data.get("success"):
                data = prediction_data.get("data", {})

                # 预测结果展示
                st.markdown("##### 📈 预测结果")

                # 预测指标
                col1, col2, col3 = st.columns(3)

                with col1:
                    predicted_value = data.get("predicted_value", 0)
                    st.metric(
                        f"预测{prediction_type}",
                        f"{predicted_value:,.0f}",
                        delta=f"{data.get('change_percentage', 0):+.1f}%"
                    )

                with col2:
                    accuracy = data.get("model_accuracy", 0)
                    st.metric(
                        "模型准确度",
                        f"{accuracy:.1f}%"
                    )

                with col3:
                    confidence = data.get("confidence_score", 0)
                    st.metric(
                        "置信度",
                        f"{confidence:.1f}%"
                    )

                # 预测图表
                forecast_data = data.get("forecast_chart", [])
                if forecast_data:
                    df_forecast = pd.DataFrame(forecast_data)

                    fig_forecast = go.Figure()

                    # 历史数据
                    historical = df_forecast[df_forecast['type'] == 'historical']
                    fig_forecast.add_trace(go.Scatter(
                        x=historical['date'],
                        y=historical['value'],
                        mode='lines',
                        name='历史数据',
                        line=dict(color='blue')
                    ))

                    # 预测数据
                    predicted = df_forecast[df_forecast['type'] == 'predicted']
                    fig_forecast.add_trace(go.Scatter(
                        x=predicted['date'],
                        y=predicted['value'],
                        mode='lines',
                        name='预测数据',
                        line=dict(color='red', dash='dash')
                    ))

                    # 置信区间
                    if 'upper_bound' in df_forecast.columns:
                        fig_forecast.add_trace(go.Scatter(
                            x=predicted['date'],
                            y=predicted['upper_bound'],
                            mode='lines',
                            line=dict(width=0),
                            showlegend=False
                        ))

                        fig_forecast.add_trace(go.Scatter(
                            x=predicted['date'],
                            y=predicted['lower_bound'],
                            mode='lines',
                            fill='tonexty',
                            fillcolor='rgba(255,0,0,0.2)',
                            line=dict(width=0),
                            name='置信区间'
                        ))

                    fig_forecast.update_layout(
                        title=f"{prediction_type}预测图",
                        xaxis_title="日期",
                        yaxis_title="数值"
                    )
                    fig_forecast = apply_plotly_theme(fig_forecast)
                    st.plotly_chart(fig_forecast, use_container_width=True)

                # 预测洞察
                insights = data.get("insights", [])
                if insights:
                    st.markdown("##### 💡 预测洞察")
                    for insight in insights:
                        st.markdown(f"• {insight}")

            else:
                st.error("预测分析失败")


def show_comparative_analysis():
    """显示对比分析"""
    st.markdown("#### 📊 对比分析")

    # 对比配置
    col1, col2 = st.columns(2)

    with col1:
        comparison_type = st.selectbox(
            "对比类型",
            ["期间对比", "产品对比", "生产线对比", "班次对比"]
        )

    with col2:
        metrics_to_compare = st.multiselect(
            "对比指标",
            ["生产效率", "质量指标", "成本指标", "设备利用率"],
            default=["生产效率", "质量指标"]
        )

    # 期间选择
    period1, period2 = None, None
    if comparison_type == "期间对比":
        col1, col2 = st.columns(2)
        with col1:
            period1 = st.selectbox("期间1", ["本月", "上月", "去年同期", "自定义"])
        with col2:
            period2 = st.selectbox("期间2", ["上月", "去年同期", "自定义", "本月"])

        # 存储期间选择到会话状态
        st.session_state.comparison_periods = (period1, period2)

    if st.button("📊 开始对比分析", use_container_width=True):
        with st.spinner("正在进行对比分析..."):
            comparison_data = get_comparative_analysis_data(comparison_type, metrics_to_compare)

            if comparison_data and comparison_data.get("success"):
                data = comparison_data.get("data", {})

                # 对比结果图表
                chart_data = data.get("comparison_chart", [])
                if chart_data:
                    df_comparison = pd.DataFrame(chart_data)

                    fig_comparison = px.bar(
                        df_comparison,
                        x="metric",
                        y="value",
                        color="period",
                        title=f"{comparison_type}结果",
                        barmode="group"
                    )
                    fig_comparison.update_layout(
                        xaxis_title="指标",
                        yaxis_title="数值",
                        legend_title="期间/类别"
                    )
                    fig_comparison = apply_plotly_theme(fig_comparison)
                    st.plotly_chart(fig_comparison, use_container_width=True)

                # 对比表格
                st.markdown("##### 📋 详细对比")

                comparison_table = data.get("comparison_table", [])
                if comparison_table:
                    df_table = pd.DataFrame(comparison_table)
                    st.dataframe(df_table, use_container_width=True)

                # 对比洞察
                insights = data.get("insights", [])
                if insights:
                    st.markdown("##### 💡 对比洞察")
                    for insight in insights:
                        st.markdown(f"• {insight}")

            else:
                st.error("对比分析失败")


# 数据获取函数
def get_efficiency_analysis_data():
    """获取效率分析数据"""
    try:
        result = api_client.get_efficiency_analysis(
            time_range=st.session_state.analysis_time_range,
            dimensions=st.session_state.selected_dimensions
        )

        if not result.get("success"):
            return get_mock_efficiency_data()

        return result
    except Exception as e:
        return get_mock_efficiency_data()


def get_quality_analysis_data():
    """获取质量分析数据"""
    try:
        result = api_client.get_quality_analysis(
            time_range=st.session_state.analysis_time_range,
            product_types=st.session_state.custom_filters.get("product_types", [])
        )

        if not result.get("success"):
            return get_mock_quality_data()

        return result
    except Exception as e:
        return get_mock_quality_data()


def get_cost_analysis_data():
    """获取成本分析数据"""
    try:
        result = api_client.get_cost_analysis(
            time_range=st.session_state.analysis_time_range,
            cost_categories=st.session_state.selected_dimensions
        )

        if not result.get("success"):
            return get_mock_cost_data()

        return result
    except Exception as e:
        return get_mock_cost_data()


def get_equipment_performance_data():
    """获取设备性能数据"""
    try:
        result = api_client.get_equipment_performance_analysis(
            time_range=st.session_state.analysis_time_range,
            equipment_types=st.session_state.selected_dimensions
        )

        if not result.get("success"):
            return get_mock_equipment_data()

        return result
    except Exception as e:
        return get_mock_equipment_data()


def get_predictive_analysis_data(prediction_type, time_horizon, confidence_level):
    """获取预测分析数据"""
    try:
        result = api_client.get_predictive_analysis(
            prediction_type=prediction_type,
            time_horizon=time_horizon,
            parameters={"confidence_level": confidence_level}
        )

        if not result.get("success"):
            return get_mock_prediction_data(prediction_type, time_horizon)

        return result
    except Exception as e:
        return get_mock_prediction_data(prediction_type, time_horizon)


def get_comparative_analysis_data(comparison_type, metrics):
    """获取对比分析数据"""
    try:
        result = api_client.get_comparative_analysis(
            analysis_type=comparison_type,
            compare_periods=["current", "previous"],
            metrics=metrics
        )

        if not result.get("success"):
            return get_mock_comparison_data(comparison_type, metrics)

        return result
    except Exception as e:
        return get_mock_comparison_data(comparison_type, metrics)


# 模拟数据函数
def get_mock_efficiency_data():
    """获取模拟效率数据"""

    # 生成趋势数据
    trends = []
    base_date = datetime.now() - timedelta(days=30)

    for i in range(30):
        date = base_date + timedelta(days=i)
        for metric in ["设备利用率", "生产效率", "OEE"]:
            if metric == "设备利用率":
                value = 75 + random.uniform(-10, 15)
            elif metric == "生产效率":
                value = 85 + random.uniform(-8, 12)
            else:  # OEE
                value = 70 + random.uniform(-10, 15)

            trends.append({
                "date": date.strftime("%Y-%m-%d"),
                "metric": metric,
                "value": max(0, min(100, value))
            })

    return {
        "success": True,
        "data": {
            "overall_efficiency": 87.5,
            "target_efficiency": 85.0,
            "equipment_utilization": 82.3,
            "oee_score": 74.2,
            "productivity_index": 1.15,
            "efficiency_trends": trends,
            "efficiency_distribution": [
                {"category": "优秀 (>90%)", "percentage": 25},
                {"category": "良好 (80-90%)", "percentage": 45},
                {"category": "一般 (70-80%)", "percentage": 25},
                {"category": "较差 (<70%)", "percentage": 5}
            ],
            "detailed_analysis": [
                {"equipment": "数控机床-01", "efficiency": 92.5, "utilization": 88.2, "oee": 81.5},
                {"equipment": "装配线-A", "efficiency": 89.3, "utilization": 85.7, "oee": 76.5},
                {"equipment": "检测设备-03", "efficiency": 78.9, "utilization": 72.1, "oee": 56.9}
            ],
            "recommendations": [
                {
                    "title": "优化设备维护计划",
                    "description": "建议调整设备维护频率，减少计划外停机时间",
                    "impact": "预计提升效率3-5%"
                },
                {
                    "title": "改进生产调度",
                    "description": "优化生产排程，减少换线时间和等待时间",
                    "impact": "预计提升利用率5-8%"
                }
            ]
        }
    }


def get_mock_quality_data():
    """获取模拟质量数据"""

    # 生成质量趋势数据
    trends = []
    base_date = datetime.now() - timedelta(days=30)

    for i in range(30):
        date = base_date + timedelta(days=i)
        trends.append({
            "date": date.strftime("%Y-%m-%d"),
            "metric": "合格率",
            "value": 96 + random.uniform(-2, 4)
        })
        trends.append({
            "date": date.strftime("%Y-%m-%d"),
            "metric": "返工率",
            "value": 2 + random.uniform(-1, 2)
        })

    # 生成控制图数据
    control_chart = []
    for i in range(50):
        control_chart.append({
            "sample": i + 1,
            "value": 98.5 + random.uniform(-2, 2),
            "ucl": 101.0,
            "lcl": 96.0,
            "center_line": 98.5
        })

    return {
        "success": True,
        "data": {
            "overall_pass_rate": 98.7,
            "target_pass_rate": 99.0,
            "rework_rate": 1.8,
            "defect_rate": 0.5,
            "customer_complaints": 2,
            "last_period_complaints": 4,
            "quality_trends": trends,
            "defect_analysis": [
                {"defect_type": "尺寸偏差", "count": 15},
                {"defect_type": "表面缺陷", "count": 8},
                {"defect_type": "材料问题", "count": 5},
                {"defect_type": "装配错误", "count": 3}
            ],
            "control_chart": control_chart
        }
    }


def get_mock_cost_data():
    """获取模拟成本数据"""

    # 生成成本趋势数据
    trends = []
    base_date = datetime.now() - timedelta(days=30)

    for i in range(30):
        date = base_date + timedelta(days=i)
        for category in ["材料成本", "人工成本", "设备成本"]:
            if category == "材料成本":
                amount = 50000 + random.uniform(-5000, 8000)
            elif category == "人工成本":
                amount = 30000 + random.uniform(-3000, 5000)
            else:  # 设备成本
                amount = 20000 + random.uniform(-2000, 3000)

            trends.append({
                "date": date.strftime("%Y-%m-%d"),
                "category": category,
                "amount": amount
            })

    return {
        "success": True,
        "data": {
            "total_cost": 1250000,
            "last_period_cost": 1180000,
            "unit_cost": 125.50,
            "target_unit_cost": 120.00,
            "cost_efficiency": 87.2,
            "cost_variance": 4.6,
            "cost_breakdown": [
                {"category": "材料成本", "amount": 650000},
                {"category": "人工成本", "amount": 350000},
                {"category": "设备成本", "amount": 150000},
                {"category": "能耗成本", "amount": 100000}
            ],
            "cost_trends": trends,
            "cost_comparison": [
                {"category": "材料成本", "current_period": 650000, "last_period": 620000},
                {"category": "人工成本", "current_period": 350000, "last_period": 340000},
                {"category": "设备成本", "current_period": 150000, "last_period": 145000}
            ]
        }
    }


def get_mock_equipment_data():
    """获取模拟设备数据"""
    return {
        "success": True,
        "data": {
            "average_utilization": 82.5,
            "average_efficiency": 88.3,
            "mtbf_hours": 245.5,
            "mttr_hours": 3.2,
            "equipment_utilization": [
                {"equipment_name": "数控机床-01", "utilization": 88.5},
                {"equipment_name": "装配线-A", "utilization": 92.3},
                {"equipment_name": "检测设备-03", "utilization": 76.8},
                {"equipment_name": "包装线-B", "utilization": 85.2}
            ],
            "failure_analysis": [
                {"failure_type": "机械故障", "count": 12},
                {"failure_type": "电气故障", "count": 8},
                {"failure_type": "软件故障", "count": 5},
                {"failure_type": "人为错误", "count": 3}
            ],
            "performance_heatmap": [
                {"equipment": "数控机床-01", "metric": "利用率", "value": 88.5},
                {"equipment": "数控机床-01", "metric": "效率", "value": 92.1},
                {"equipment": "数控机床-01", "metric": "可靠性", "value": 95.2},
                {"equipment": "装配线-A", "metric": "利用率", "value": 92.3},
                {"equipment": "装配线-A", "metric": "效率", "value": 89.7},
                {"equipment": "装配线-A", "metric": "可靠性", "value": 91.8}
            ]
        }
    }


def get_mock_prediction_data(prediction_type, time_horizon):
    """获取模拟预测数据"""

    # 生成预测图表数据
    forecast_data = []
    base_date = datetime.now() - timedelta(days=30)

    # 历史数据
    for i in range(30):
        date = base_date + timedelta(days=i)
        value = 1000 + random.uniform(-100, 150) + i * 5  # 带趋势的随机数据
        forecast_data.append({
            "date": date.strftime("%Y-%m-%d"),
            "type": "historical",
            "value": value
        })

    # 预测数据
    horizon_days = {"7days": 7, "30days": 30, "90days": 90, "6months": 180}[time_horizon]
    for i in range(horizon_days):
        date = datetime.now() + timedelta(days=i)
        base_value = 1150 + i * 3  # 预测趋势
        value = base_value + random.uniform(-50, 50)

        forecast_data.append({
            "date": date.strftime("%Y-%m-%d"),
            "type": "predicted",
            "value": value,
            "upper_bound": value + 100,
            "lower_bound": value - 100
        })

    return {
        "success": True,
        "data": {
            "predicted_value": 1250,
            "change_percentage": 8.5,
            "model_accuracy": 92.3,
            "confidence_score": 87.5,
            "forecast_chart": forecast_data,
            "insights": [
                f"{prediction_type}预计在未来{time_horizon}内呈上升趋势",
                "建议提前准备相应的资源配置",
                "关注市场变化对预测结果的影响"
            ]
        }
    }


def get_mock_comparison_data(comparison_type, metrics):
    """获取模拟对比数据"""

    chart_data = []
    table_data = []

    for metric in metrics:
        current_value = 85 + random.uniform(-10, 15)
        previous_value = 80 + random.uniform(-10, 15)

        chart_data.extend([
            {"metric": metric, "period": "当前期间", "value": current_value},
            {"metric": metric, "period": "对比期间", "value": previous_value}
        ])

        table_data.append({
            "指标": metric,
            "当前期间": f"{current_value:.1f}%",
            "对比期间": f"{previous_value:.1f}%",
            "变化": f"{current_value - previous_value:+.1f}%",
            "变化率": f"{(current_value - previous_value) / previous_value * 100:+.1f}%"
        })

    return {
        "success": True,
        "data": {
            "comparison_chart": chart_data,
            "comparison_table": table_data,
            "insights": [
                f"{comparison_type}显示整体性能有所提升",
                "部分指标需要重点关注和改进",
                "建议制定针对性的改进措施"
            ]
        }
    }


# 操作函数
def generate_analysis_report():
    """生成分析报告"""
    st.success("分析报告生成功能开发中...")


def export_analysis_data():
    """导出分析数据"""
    st.success("数据导出功能开发中...")


# 主要内容区域 - 在所有函数定义之后调用
if analysis_type == "效率分析":
    show_efficiency_analysis()
elif analysis_type == "质量分析":
    show_quality_analysis()
elif analysis_type == "成本分析":
    show_cost_analysis()
elif analysis_type == "设备性能":
    show_equipment_performance_analysis()
elif analysis_type == "预测分析":
    show_predictive_analysis()
elif analysis_type == "对比分析":
    show_comparative_analysis()
