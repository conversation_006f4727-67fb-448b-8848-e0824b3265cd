"""
AI能力增强配置文件
定义预测分析、异常检测、智能优化等功能的配置参数
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import numpy as np

class ModelType(Enum):
    """模型类型枚举"""
    RANDOM_FOREST = "random_forest"
    LSTM = "lstm"
    ARIMA = "arima"
    PROPHET = "prophet"
    ISOLATION_FOREST = "isolation_forest"
    AUTOENCODER = "autoencoder"
    GENETIC_ALGORITHM = "genetic_algorithm"
    REINFORCEMENT_LEARNING = "reinforcement_learning"

class DataFrequency(Enum):
    """数据频率枚举"""
    HOURLY = "H"
    DAILY = "D"
    WEEKLY = "W"
    MONTHLY = "M"

@dataclass
class ModelConfig:
    """模型配置"""
    model_type: ModelType
    parameters: Dict[str, Any]
    training_config: Dict[str, Any]
    performance_thresholds: Dict[str, float]
    update_frequency: DataFrequency
    enabled: bool = True

@dataclass
class PredictionConfig:
    """预测配置"""
    name: str
    description: str
    model_config: ModelConfig
    input_features: List[str]
    target_variable: str
    forecast_horizon: int
    confidence_level: float
    data_requirements: Dict[str, Any]

@dataclass
class AnomalyDetectionConfig:
    """异常检测配置"""
    name: str
    description: str
    detection_methods: List[ModelConfig]
    sensitivity: float
    threshold_config: Dict[str, float]
    alert_config: Dict[str, Any]

@dataclass
class OptimizationConfig:
    """优化配置"""
    name: str
    description: str
    algorithm_config: ModelConfig
    objective_function: str
    constraints: List[Dict[str, Any]]
    optimization_parameters: Dict[str, Any]

# 预测分析配置
PREDICTION_CONFIGS = {
    "demand_forecast": PredictionConfig(
        name="需求预测",
        description="基于历史数据预测未来需求",
        model_config=ModelConfig(
            model_type=ModelType.RANDOM_FOREST,
            parameters={
                "n_estimators": 100,
                "max_depth": 10,
                "min_samples_split": 5,
                "min_samples_leaf": 2,
                "random_state": 42
            },
            training_config={
                "test_size": 0.2,
                "validation_split": 0.1,
                "cross_validation_folds": 5,
                "feature_selection": True
            },
            performance_thresholds={
                "mae": 10.0,
                "rmse": 15.0,
                "mape": 0.15,
                "r2_score": 0.8
            },
            update_frequency=DataFrequency.WEEKLY
        ),
        input_features=[
            "historical_demand",
            "seasonal_factors",
            "trend_components",
            "external_factors",
            "promotional_activities"
        ],
        target_variable="demand",
        forecast_horizon=30,
        confidence_level=0.95,
        data_requirements={
            "min_history_days": 90,
            "data_quality_threshold": 0.9,
            "missing_value_tolerance": 0.05
        }
    ),
    
    "equipment_failure": PredictionConfig(
        name="设备故障预测",
        description="预测设备故障概率和时间",
        model_config=ModelConfig(
            model_type=ModelType.RANDOM_FOREST,
            parameters={
                "n_estimators": 150,
                "max_depth": 15,
                "class_weight": "balanced",
                "random_state": 42
            },
            training_config={
                "test_size": 0.25,
                "stratify": True,
                "feature_importance_threshold": 0.01
            },
            performance_thresholds={
                "precision": 0.85,
                "recall": 0.80,
                "f1_score": 0.82,
                "auc_roc": 0.90
            },
            update_frequency=DataFrequency.DAILY
        ),
        input_features=[
            "operating_hours",
            "maintenance_history",
            "vibration_data",
            "temperature_data",
            "pressure_data",
            "load_factors"
        ],
        target_variable="failure_probability",
        forecast_horizon=14,
        confidence_level=0.90,
        data_requirements={
            "min_operating_hours": 100,
            "sensor_data_frequency": "hourly",
            "maintenance_record_completeness": 0.95
        }
    ),
    
    "quality_prediction": PredictionConfig(
        name="质量预测",
        description="预测产品质量指标",
        model_config=ModelConfig(
            model_type=ModelType.RANDOM_FOREST,
            parameters={
                "n_estimators": 120,
                "max_depth": 12,
                "min_samples_split": 3,
                "random_state": 42
            },
            training_config={
                "test_size": 0.2,
                "feature_scaling": True,
                "outlier_removal": True
            },
            performance_thresholds={
                "mae": 0.05,
                "rmse": 0.08,
                "accuracy": 0.90
            },
            update_frequency=DataFrequency.DAILY
        ),
        input_features=[
            "process_parameters",
            "raw_material_quality",
            "environmental_conditions",
            "equipment_status",
            "operator_experience"
        ],
        target_variable="quality_score",
        forecast_horizon=7,
        confidence_level=0.95,
        data_requirements={
            "min_samples": 500,
            "quality_data_completeness": 0.98
        }
    )
}

# 异常检测配置
ANOMALY_DETECTION_CONFIGS = {
    "production_monitoring": AnomalyDetectionConfig(
        name="生产监控异常检测",
        description="检测生产过程中的异常情况",
        detection_methods=[
            ModelConfig(
                model_type=ModelType.ISOLATION_FOREST,
                parameters={
                    "contamination": 0.1,
                    "n_estimators": 100,
                    "max_samples": "auto",
                    "random_state": 42
                },
                training_config={
                    "feature_scaling": True,
                    "outlier_fraction": 0.05
                },
                performance_thresholds={
                    "precision": 0.80,
                    "recall": 0.75,
                    "f1_score": 0.77
                },
                update_frequency=DataFrequency.DAILY
            )
        ],
        sensitivity=0.7,
        threshold_config={
            "statistical_threshold": 3.0,
            "isolation_threshold": -0.1,
            "ensemble_threshold": 0.6
        },
        alert_config={
            "email_notifications": True,
            "sms_alerts": False,
            "dashboard_alerts": True,
            "severity_levels": ["low", "medium", "high", "critical"]
        }
    ),
    
    "equipment_monitoring": AnomalyDetectionConfig(
        name="设备监控异常检测",
        description="检测设备运行异常",
        detection_methods=[
            ModelConfig(
                model_type=ModelType.AUTOENCODER,
                parameters={
                    "encoding_dim": 32,
                    "epochs": 100,
                    "batch_size": 32,
                    "learning_rate": 0.001
                },
                training_config={
                    "validation_split": 0.2,
                    "early_stopping": True,
                    "patience": 10
                },
                performance_thresholds={
                    "reconstruction_error": 0.1,
                    "detection_rate": 0.85
                },
                update_frequency=DataFrequency.HOURLY
            )
        ],
        sensitivity=0.8,
        threshold_config={
            "reconstruction_threshold": 0.1,
            "anomaly_score_threshold": 0.7
        },
        alert_config={
            "real_time_alerts": True,
            "maintenance_notifications": True,
            "escalation_rules": {
                "high_severity": "immediate",
                "medium_severity": "within_1_hour",
                "low_severity": "daily_summary"
            }
        }
    )
}

# 智能优化配置
OPTIMIZATION_CONFIGS = {
    "production_scheduling": OptimizationConfig(
        name="生产排程优化",
        description="优化生产排程以最小化完工时间",
        algorithm_config=ModelConfig(
            model_type=ModelType.GENETIC_ALGORITHM,
            parameters={
                "population_size": 100,
                "generations": 200,
                "crossover_rate": 0.8,
                "mutation_rate": 0.1,
                "selection_method": "tournament"
            },
            training_config={
                "convergence_threshold": 0.001,
                "max_iterations": 1000,
                "parallel_processing": True
            },
            performance_thresholds={
                "solution_quality": 0.90,
                "convergence_rate": 0.95
            },
            update_frequency=DataFrequency.DAILY
        ),
        objective_function="minimize_makespan",
        constraints=[
            {"type": "capacity", "description": "设备产能约束"},
            {"type": "precedence", "description": "工序优先级约束"},
            {"type": "resource", "description": "资源可用性约束"},
            {"type": "deadline", "description": "交期约束"}
        ],
        optimization_parameters={
            "time_horizon": 7,  # 天
            "optimization_interval": 24,  # 小时
            "solution_pool_size": 10
        }
    ),
    
    "resource_allocation": OptimizationConfig(
        name="资源分配优化",
        description="优化资源分配以最小化成本",
        algorithm_config=ModelConfig(
            model_type=ModelType.GENETIC_ALGORITHM,
            parameters={
                "population_size": 80,
                "generations": 150,
                "crossover_rate": 0.7,
                "mutation_rate": 0.15
            },
            training_config={
                "elite_size": 10,
                "diversity_maintenance": True
            },
            performance_thresholds={
                "cost_reduction": 0.10,
                "resource_utilization": 0.85
            },
            update_frequency=DataFrequency.WEEKLY
        ),
        objective_function="minimize_cost",
        constraints=[
            {"type": "demand", "description": "需求满足约束"},
            {"type": "capacity", "description": "资源容量约束"},
            {"type": "budget", "description": "预算约束"}
        ],
        optimization_parameters={
            "planning_horizon": 30,  # 天
            "resource_types": ["human", "equipment", "material"],
            "cost_factors": ["fixed_cost", "variable_cost", "opportunity_cost"]
        }
    ),
    
    "energy_optimization": OptimizationConfig(
        name="能耗优化",
        description="优化能源消耗以降低成本",
        algorithm_config=ModelConfig(
            model_type=ModelType.REINFORCEMENT_LEARNING,
            parameters={
                "learning_rate": 0.001,
                "discount_factor": 0.95,
                "exploration_rate": 0.1,
                "network_architecture": [64, 32, 16]
            },
            training_config={
                "episodes": 1000,
                "batch_size": 32,
                "memory_size": 10000,
                "target_update_frequency": 100
            },
            performance_thresholds={
                "energy_savings": 0.15,
                "cost_reduction": 0.12
            },
            update_frequency=DataFrequency.HOURLY
        ),
        objective_function="minimize_energy_cost",
        constraints=[
            {"type": "production", "description": "生产需求约束"},
            {"type": "power_limit", "description": "电力容量约束"},
            {"type": "equipment", "description": "设备运行约束"}
        ],
        optimization_parameters={
            "optimization_window": 24,  # 小时
            "energy_price_forecast": True,
            "renewable_energy_integration": True
        }
    )
}

# AI服务全局配置
AI_SERVICE_CONFIG = {
    "general": {
        "max_concurrent_requests": 10,
        "request_timeout": 300,  # 秒
        "cache_enabled": True,
        "cache_ttl": 3600,  # 秒
        "logging_level": "INFO",
        "performance_monitoring": True
    },
    
    "data_processing": {
        "max_data_points": 100000,
        "data_validation": True,
        "outlier_detection": True,
        "missing_value_handling": "interpolation",
        "feature_scaling": "standard"
    },
    
    "model_management": {
        "auto_retrain": True,
        "retrain_threshold": 0.05,  # 性能下降阈值
        "model_versioning": True,
        "a_b_testing": True,
        "model_backup": True
    },
    
    "security": {
        "data_encryption": True,
        "access_control": True,
        "audit_logging": True,
        "rate_limiting": True,
        "input_validation": True
    },
    
    "integration": {
        "api_endpoints": {
            "prediction": "/api/v1/ai/predict",
            "anomaly_detection": "/api/v1/ai/detect-anomalies",
            "optimization": "/api/v1/ai/optimize",
            "comprehensive_analysis": "/api/v1/ai/comprehensive-analysis"
        },
        "webhook_notifications": True,
        "external_data_sources": ["erp", "mes", "iot", "weather"],
        "export_formats": ["json", "csv", "excel", "pdf"]
    }
}

# 性能基准
PERFORMANCE_BENCHMARKS = {
    "prediction_accuracy": {
        "demand_forecast": {"target": 0.90, "acceptable": 0.85, "poor": 0.80},
        "equipment_failure": {"target": 0.85, "acceptable": 0.80, "poor": 0.75},
        "quality_prediction": {"target": 0.92, "acceptable": 0.88, "poor": 0.85}
    },
    
    "anomaly_detection": {
        "precision": {"target": 0.90, "acceptable": 0.85, "poor": 0.80},
        "recall": {"target": 0.85, "acceptable": 0.80, "poor": 0.75},
        "false_positive_rate": {"target": 0.05, "acceptable": 0.10, "poor": 0.15}
    },
    
    "optimization": {
        "improvement_percentage": {"target": 0.20, "acceptable": 0.15, "poor": 0.10},
        "convergence_time": {"target": 60, "acceptable": 120, "poor": 300},  # 秒
        "solution_quality": {"target": 0.95, "acceptable": 0.90, "poor": 0.85}
    },
    
    "system_performance": {
        "response_time": {"target": 0.5, "acceptable": 1.0, "poor": 2.0},  # 秒
        "throughput": {"target": 100, "acceptable": 50, "poor": 20},  # 请求/分钟
        "availability": {"target": 0.999, "acceptable": 0.995, "poor": 0.99}
    }
}

def get_prediction_config(prediction_type: str) -> Optional[PredictionConfig]:
    """获取预测配置"""
    return PREDICTION_CONFIGS.get(prediction_type)

def get_anomaly_config(detection_type: str) -> Optional[AnomalyDetectionConfig]:
    """获取异常检测配置"""
    return ANOMALY_DETECTION_CONFIGS.get(detection_type)

def get_optimization_config(optimization_type: str) -> Optional[OptimizationConfig]:
    """获取优化配置"""
    return OPTIMIZATION_CONFIGS.get(optimization_type)

def validate_performance(metric_type: str, metric_name: str, value: float) -> str:
    """验证性能指标"""
    benchmarks = PERFORMANCE_BENCHMARKS.get(metric_type, {}).get(metric_name, {})
    
    if not benchmarks:
        return "unknown"
    
    if value >= benchmarks.get("target", float('inf')):
        return "excellent"
    elif value >= benchmarks.get("acceptable", float('inf')):
        return "good"
    elif value >= benchmarks.get("poor", float('inf')):
        return "acceptable"
    else:
        return "poor"
