"""
Smart Planning - 智能工厂生产管理规划系统
主应用入口文件
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import engine, Base
from app.core.logging_config import setup_logging
from app.api.v1.api import api_router
from app.middleware.rate_limit import RateLimitMiddleware
from app.middleware.request_logging import RequestLoggingMiddleware

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 Smart Planning 系统启动中...")

    # 创建数据库表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    logger.info("✅ 数据库初始化完成")
    logger.info("🏭 Smart Planning 系统启动完成")

    yield

    # 关闭时执行
    logger.info("🔄 Smart Planning 系统关闭中...")
    await engine.dispose()
    logger.info("✅ Smart Planning 系统已关闭")


def create_application() -> FastAPI:
    """创建FastAPI应用实例"""

    app = FastAPI(
        title=settings.PROJECT_NAME,
        description="智能工厂生产管理规划系统 - 专为20-30人小规模团队设计",
        version="1.0.0",
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )

    # 添加中间件
    setup_middleware(app)

    # 添加路由
    app.include_router(api_router, prefix=settings.API_V1_STR)

    # 添加异常处理
    setup_exception_handlers(app)

    # 添加健康检查
    setup_health_check(app)

    return app


def setup_middleware(app: FastAPI):
    """设置中间件"""

    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_HOSTS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 受信任主机中间件
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )

    # 请求日志中间件
    app.add_middleware(RequestLoggingMiddleware)

    # 限流中间件
    app.add_middleware(RateLimitMiddleware)


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""

    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理"""
        logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "message": exc.detail,
                "error_code": f"HTTP_{exc.status_code}",
                "timestamp": settings.get_current_timestamp(),
                "request_id": getattr(request.state, "request_id", None)
            }
        )

    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """通用异常处理"""
        logger.error(f"未处理异常: {type(exc).__name__} - {str(exc)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": "服务器内部错误",
                "error_code": "INTERNAL_SERVER_ERROR",
                "timestamp": settings.get_current_timestamp(),
                "request_id": getattr(request.state, "request_id", None)
            }
        )


def setup_health_check(app: FastAPI):
    """设置健康检查端点"""

    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {
            "status": "healthy",
            "service": "Smart Planning",
            "version": "1.0.0",
            "timestamp": settings.get_current_timestamp()
        }

    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": "🏭 Smart Planning - 智能生产管理系统",
            "version": "1.0.0",
            "docs": "/docs",
            "health": "/health"
        }


# 创建应用实例
app = create_application()


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
