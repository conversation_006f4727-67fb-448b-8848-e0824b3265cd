"""
AI助手模板配置
支持静态模板和动态数据模板的扩展
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class TemplateType(Enum):
    """模板类型枚举"""
    STATIC = "static"
    DYNAMIC = "dynamic"


class DataSource(Enum):
    """数据源枚举"""
    DATABASE = "database"
    API = "api"
    FILE = "file"
    REALTIME = "realtime"


@dataclass
class TemplateConfig:
    """模板配置类"""
    name: str
    type: TemplateType
    description: str
    category: str
    prompt_template: str
    data_source: Optional[DataSource] = None
    api_endpoint: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    required_permissions: Optional[List[str]] = None


# 静态模板配置
STATIC_TEMPLATES = {
    "生产规划分析": TemplateConfig(
        name="生产规划分析",
        type=TemplateType.STATIC,
        description="分析生产规划数据，识别瓶颈和优化机会",
        category="生产管理",
        prompt_template="请帮我分析当前的生产规划数据，识别潜在的瓶颈和优化机会。",
        required_permissions=["production.view"]
    ),
    
    "设备故障诊断": TemplateConfig(
        name="设备故障诊断",
        type=TemplateType.STATIC,
        description="诊断设备异常，提供解决方案",
        category="设备管理",
        prompt_template="我的设备出现了异常，请帮我分析可能的原因和解决方案。",
        required_permissions=["equipment.view"]
    ),
    
    "质量问题分析": TemplateConfig(
        name="质量问题分析",
        type=TemplateType.STATIC,
        description="分析质量波动原因，提供改进建议",
        category="质量管理",
        prompt_template="产品质量出现波动，请帮我分析原因并提供改进建议。",
        required_permissions=["quality.view"]
    ),
    
    "成本优化建议": TemplateConfig(
        name="成本优化建议",
        type=TemplateType.STATIC,
        description="分析生产成本构成，提供降本增效建议",
        category="成本管理",
        prompt_template="请分析我的生产成本构成，提供降本增效的建议。",
        required_permissions=["cost.view"]
    ),
    
    "效率提升方案": TemplateConfig(
        name="效率提升方案",
        type=TemplateType.STATIC,
        description="分析生产效率数据，提供提升方案",
        category="效率管理",
        prompt_template="请帮我分析生产效率数据，提供提升方案。",
        required_permissions=["efficiency.view"]
    )
}


# 动态数据模板配置
DYNAMIC_TEMPLATES = {
    "PCI性能分析": TemplateConfig(
        name="PCI性能分析",
        type=TemplateType.DYNAMIC,
        description="实时分析PCI设备性能数据",
        category="系统性能",
        prompt_template="请对以下PCI性能数据进行{analysis_type}（时间范围：{time_range}）：\n\n{data_content}\n\n请提供详细的分析结果，包括：\n1. 性能趋势分析\n2. 潜在问题识别\n3. 优化建议\n4. 预防措施",
        data_source=DataSource.REALTIME,
        api_endpoint="/api/v1/system/pci-performance",
        parameters={
            "time_ranges": ["最近1小时", "最近24小时", "最近7天", "最近30天", "自定义"],
            "metrics": ["吞吐量", "延迟", "错误率", "带宽利用率", "队列深度", "IOPS"],
            "device_filters": ["全部设备", "关键设备", "异常设备", "自定义选择"],
            "analysis_types": ["性能趋势分析", "异常检测", "瓶颈识别", "对比分析", "预测分析"]
        },
        required_permissions=["system.view", "llm.chat"]
    ),
    
    "实时设备状态": TemplateConfig(
        name="实时设备状态",
        type=TemplateType.DYNAMIC,
        description="分析实时设备运行状态",
        category="设备监控",
        prompt_template="请分析以下实时设备状态数据：\n\n{data_content}\n\n请提供：\n1. 设备运行状态评估\n2. 异常设备分析\n3. 利用率优化建议\n4. 维护计划建议",
        data_source=DataSource.API,
        api_endpoint="/api/v1/equipment/status",
        parameters={
            "equipment_types": ["数控机床", "装配线", "检测设备", "物流设备", "辅助设备"],
            "status_metrics": ["运行状态", "利用率", "温度", "振动", "功耗", "故障代码"],
            "alert_levels": ["全部", "仅异常", "仅告警", "仅故障"]
        },
        required_permissions=["equipment.view", "llm.chat"]
    ),
    
    "生产KPI分析": TemplateConfig(
        name="生产KPI分析",
        type=TemplateType.DYNAMIC,
        description="分析生产关键绩效指标",
        category="绩效分析",
        prompt_template="请分析以下生产KPI数据（{comparison_type}）：\n\n{data_content}\n\n请提供：\n1. KPI达成情况分析\n2. 趋势变化原因分析\n3. 改进措施建议\n4. 目标调整建议",
        data_source=DataSource.DATABASE,
        api_endpoint="/api/v1/production/kpi",
        parameters={
            "kpi_categories": ["效率指标", "质量指标", "成本指标", "交付指标", "安全指标"],
            "comparison_types": ["同期对比", "环比对比", "目标对比", "行业对比"],
            "time_periods": ["日报", "周报", "月报", "季报"]
        },
        required_permissions=["production.view", "llm.chat"]
    ),
    
    "库存优化分析": TemplateConfig(
        name="库存优化分析",
        type=TemplateType.DYNAMIC,
        description="分析库存数据，提供优化建议",
        category="库存管理",
        prompt_template="请分析以下库存数据（分析重点：{analysis_focus}，优化目标：{optimization_goal}）：\n\n{data_content}\n\n请提供：\n1. 库存结构分析\n2. 风险评估\n3. 优化建议\n4. 实施方案",
        data_source=DataSource.DATABASE,
        api_endpoint="/api/v1/inventory/analysis",
        parameters={
            "inventory_types": ["原材料", "半成品", "成品", "备件", "工具"],
            "analysis_focus": ["库存周转", "安全库存", "缺货风险", "过期风险", "成本优化"],
            "optimization_goals": ["降低库存成本", "提高周转率", "减少缺货", "优化存储空间"]
        },
        required_permissions=["inventory.view", "llm.chat"]
    ),
    
    "供应链分析": TemplateConfig(
        name="供应链分析",
        type=TemplateType.DYNAMIC,
        description="分析供应链数据，识别风险和机会",
        category="供应链管理",
        prompt_template="请分析以下供应链数据：\n\n{data_content}\n\n请提供：\n1. 供应链健康度评估\n2. 风险点识别\n3. 优化机会分析\n4. 改进建议",
        data_source=DataSource.API,
        api_endpoint="/api/v1/supply-chain/analysis",
        parameters={
            "analysis_dimensions": ["供应商表现", "交付准时率", "质量稳定性", "成本波动", "风险评估"],
            "time_ranges": ["最近30天", "最近90天", "最近半年", "最近一年"],
            "supplier_categories": ["原材料供应商", "零部件供应商", "服务供应商", "物流供应商"]
        },
        required_permissions=["supply_chain.view", "llm.chat"]
    )
}


# 模板类别配置
TEMPLATE_CATEGORIES = {
    "生产管理": {
        "icon": "🏭",
        "description": "生产规划、调度、监控相关模板",
        "color": "#1f77b4"
    },
    "设备管理": {
        "icon": "⚙️",
        "description": "设备监控、维护、故障诊断模板",
        "color": "#ff7f0e"
    },
    "质量管理": {
        "icon": "🎯",
        "description": "质量控制、分析、改进模板",
        "color": "#2ca02c"
    },
    "成本管理": {
        "icon": "💰",
        "description": "成本分析、控制、优化模板",
        "color": "#d62728"
    },
    "效率管理": {
        "icon": "📈",
        "description": "效率分析、提升、优化模板",
        "color": "#9467bd"
    },
    "系统性能": {
        "icon": "🖥️",
        "description": "系统性能监控、分析模板",
        "color": "#8c564b"
    },
    "设备监控": {
        "icon": "📡",
        "description": "实时设备状态监控模板",
        "color": "#e377c2"
    },
    "绩效分析": {
        "icon": "📊",
        "description": "KPI分析、绩效评估模板",
        "color": "#7f7f7f"
    },
    "库存管理": {
        "icon": "📦",
        "description": "库存分析、优化模板",
        "color": "#bcbd22"
    },
    "供应链管理": {
        "icon": "🚚",
        "description": "供应链分析、风险管理模板",
        "color": "#17becf"
    }
}


def get_templates_by_category(category: str = None) -> Dict[str, TemplateConfig]:
    """根据类别获取模板"""
    all_templates = {**STATIC_TEMPLATES, **DYNAMIC_TEMPLATES}
    
    if category is None:
        return all_templates
    
    return {
        name: template for name, template in all_templates.items()
        if template.category == category
    }


def get_template_by_name(name: str) -> Optional[TemplateConfig]:
    """根据名称获取模板"""
    all_templates = {**STATIC_TEMPLATES, **DYNAMIC_TEMPLATES}
    return all_templates.get(name)


def get_available_categories() -> List[str]:
    """获取可用的模板类别"""
    return list(TEMPLATE_CATEGORIES.keys())


def check_template_permissions(template: TemplateConfig, user_permissions: List[str]) -> bool:
    """检查用户是否有模板使用权限"""
    if not template.required_permissions:
        return True
    
    return all(perm in user_permissions for perm in template.required_permissions)


def add_custom_template(name: str, config: TemplateConfig, template_type: TemplateType = TemplateType.STATIC):
    """添加自定义模板"""
    if template_type == TemplateType.STATIC:
        STATIC_TEMPLATES[name] = config
    else:
        DYNAMIC_TEMPLATES[name] = config


def remove_template(name: str):
    """移除模板"""
    if name in STATIC_TEMPLATES:
        del STATIC_TEMPLATES[name]
    elif name in DYNAMIC_TEMPLATES:
        del DYNAMIC_TEMPLATES[name]


# 示例：如何添加新的PCI模板
def add_pci_template_example():
    """添加PCI模板的示例"""
    pci_template = TemplateConfig(
        name="PCI总线分析",
        type=TemplateType.DYNAMIC,
        description="深度分析PCI总线性能和瓶颈",
        category="系统性能",
        prompt_template="""
请对以下PCI总线数据进行深度分析：

**系统信息**：
{system_info}

**PCI设备列表**：
{device_list}

**性能指标**：
{performance_metrics}

**错误日志**：
{error_logs}

请提供：
1. PCI总线性能评估
2. 瓶颈识别和分析
3. 设备兼容性检查
4. 优化建议和解决方案
5. 预防性维护建议
""",
        data_source=DataSource.REALTIME,
        api_endpoint="/api/v1/system/pci-detailed",
        parameters={
            "analysis_depth": ["基础分析", "详细分析", "深度诊断"],
            "focus_areas": ["性能瓶颈", "兼容性问题", "错误分析", "优化建议"],
            "device_types": ["网卡", "显卡", "存储控制器", "USB控制器", "音频设备"]
        },
        required_permissions=["system.admin", "llm.chat"]
    )
    
    add_custom_template("PCI总线分析", pci_template, TemplateType.DYNAMIC)
    return pci_template
