"""
基础模型类
"""

from sqlalchemy import Column, Integer, DateTime, String, Boolean, Text
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql import func
from datetime import datetime
import uuid

from app.core.database import Base


class TimestampMixin:
    """时间戳混入类"""
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )


class SoftDeleteMixin:
    """软删除混入类"""
    
    is_deleted = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已删除"
    )
    
    deleted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="删除时间"
    )


class VersionMixin:
    """版本控制混入类（乐观锁）"""
    
    version = Column(
        Integer,
        default=1,
        nullable=False,
        comment="版本号"
    )


class UserTrackingMixin:
    """用户跟踪混入类"""
    
    created_by = Column(
        String(50),
        nullable=True,
        comment="创建者ID"
    )
    
    updated_by = Column(
        String(50),
        nullable=True,
        comment="更新者ID"
    )


class BaseModel(Base, TimestampMixin, SoftDeleteMixin, VersionMixin):
    """基础模型类"""
    
    __abstract__ = True
    
    id = Column(
        String(50),
        primary_key=True,
        default=lambda: str(uuid.uuid4()),
        comment="主键ID"
    )
    
    @declared_attr
    def __tablename__(cls):
        """自动生成表名"""
        # 将驼峰命名转换为下划线命名
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()
    
    def to_dict(self, exclude_fields: list = None) -> dict:
        """转换为字典"""
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: dict, exclude_fields: list = None):
        """从字典更新"""
        exclude_fields = exclude_fields or ['id', 'created_at', 'updated_at']
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
    
    def soft_delete(self, user_id: str = None):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        if user_id:
            self.updated_by = user_id
    
    def restore(self, user_id: str = None):
        """恢复删除"""
        self.is_deleted = False
        self.deleted_at = None
        if user_id:
            self.updated_by = user_id
    
    @classmethod
    def get_table_comment(cls) -> str:
        """获取表注释"""
        return getattr(cls.__table__, 'comment', cls.__name__)


class AuditLogMixin:
    """审计日志混入类"""
    
    operation_type = Column(
        String(50),
        nullable=False,
        comment="操作类型"
    )
    
    operation_data = Column(
        Text,
        nullable=True,
        comment="操作数据(JSON)"
    )
    
    ip_address = Column(
        String(45),
        nullable=True,
        comment="IP地址"
    )
    
    user_agent = Column(
        String(500),
        nullable=True,
        comment="用户代理"
    )
