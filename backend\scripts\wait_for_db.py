#!/usr/bin/env python3
"""
等待数据库连接就绪
"""

import asyncio
import sys
import time
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text

# 添加项目路径
sys.path.append('/app')

from app.core.config import settings


async def wait_for_database():
    """等待数据库连接就绪"""
    max_retries = 30
    retry_interval = 2
    
    engine = create_async_engine(settings.DATABASE_URL)
    
    for attempt in range(max_retries):
        try:
            async with engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            
            print("✅ 数据库连接成功")
            await engine.dispose()
            return True
            
        except Exception as e:
            print(f"⏳ 数据库连接失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            
            if attempt < max_retries - 1:
                time.sleep(retry_interval)
            else:
                print("❌ 数据库连接超时")
                await engine.dispose()
                return False
    
    return False


if __name__ == "__main__":
    success = asyncio.run(wait_for_database())
    sys.exit(0 if success else 1)
