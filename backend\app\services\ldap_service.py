"""
LDAP认证服务
支持Active Directory和OpenLDAP集成
"""

import ldap3
from ldap3 import Server, Connection, ALL, NTLM, SIMPLE
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime

from ..core.config import settings
from ..models.user import User
from ..services.user_service import UserService

logger = logging.getLogger(__name__)


class LDAPConfig:
    """LDAP配置类"""

    def __init__(self):
        # LDAP服务器配置
        self.server_uri = getattr(settings, 'LDAP_SERVER_URI', 'ldap://localhost:389')
        self.server_port = getattr(settings, 'LDAP_SERVER_PORT', 389)
        self.use_ssl = getattr(settings, 'LDAP_USE_SSL', False)
        self.use_tls = getattr(settings, 'LDAP_USE_TLS', False)

        # 认证配置
        self.bind_dn = getattr(settings, 'LDAP_BIND_DN', '')
        self.bind_password = getattr(settings, 'LDAP_BIND_PASSWORD', '')
        self.auth_method = getattr(settings, 'LDAP_AUTH_METHOD', 'SIMPLE')  # SIMPLE, NTLM

        # 搜索配置
        self.base_dn = getattr(settings, 'LDAP_BASE_DN', 'dc=example,dc=com')
        self.user_search_base = getattr(settings, 'LDAP_USER_SEARCH_BASE', 'ou=users,dc=example,dc=com')
        self.group_search_base = getattr(settings, 'LDAP_GROUP_SEARCH_BASE', 'ou=groups,dc=example,dc=com')

        # 用户属性映射
        self.user_filter = getattr(settings, 'LDAP_USER_FILTER', '(uid={username})')
        self.username_attr = getattr(settings, 'LDAP_USERNAME_ATTR', 'uid')
        self.email_attr = getattr(settings, 'LDAP_EMAIL_ATTR', 'mail')
        self.first_name_attr = getattr(settings, 'LDAP_FIRST_NAME_ATTR', 'givenName')
        self.last_name_attr = getattr(settings, 'LDAP_LAST_NAME_ATTR', 'sn')
        self.display_name_attr = getattr(settings, 'LDAP_DISPLAY_NAME_ATTR', 'displayName')

        # 组属性映射
        self.group_filter = getattr(settings, 'LDAP_GROUP_FILTER', '(member={user_dn})')
        self.group_name_attr = getattr(settings, 'LDAP_GROUP_NAME_ATTR', 'cn')

        # 权限映射
        self.admin_groups = getattr(settings, 'LDAP_ADMIN_GROUPS', ['administrators', 'admin'])
        self.user_groups = getattr(settings, 'LDAP_USER_GROUPS', ['users'])

        # 同步配置
        self.auto_create_users = getattr(settings, 'LDAP_AUTO_CREATE_USERS', True)
        self.auto_update_users = getattr(settings, 'LDAP_AUTO_UPDATE_USERS', True)
        self.sync_groups = getattr(settings, 'LDAP_SYNC_GROUPS', True)


class LDAPService:
    """LDAP认证服务"""

    def __init__(self, db_session=None):
        self.config = LDAPConfig()
        self.db = db_session
        self.user_service = UserService(db_session) if db_session else None

    def _get_server(self) -> Server:
        """获取LDAP服务器连接"""
        try:
            server = Server(
                self.config.server_uri,
                port=self.config.server_port,
                use_ssl=self.config.use_ssl,
                get_info=ALL
            )
            return server
        except Exception as e:
            logger.error(f"LDAP服务器连接失败: {e}")
            raise

    def _get_connection(self, username: str = None, password: str = None) -> Connection:
        """获取LDAP连接"""
        server = self._get_server()

        if username and password:
            # 用户认证连接
            if self.config.auth_method == 'NTLM':
                conn = Connection(
                    server,
                    user=username,
                    password=password,
                    authentication=NTLM,
                    auto_bind=True
                )
            else:
                # 构建用户DN
                user_dn = self.config.user_filter.format(username=username)
                if not user_dn.startswith('cn=') and not user_dn.startswith('uid='):
                    user_dn = f"uid={username},{self.config.user_search_base}"

                conn = Connection(
                    server,
                    user=user_dn,
                    password=password,
                    authentication=SIMPLE,
                    auto_bind=True
                )
        else:
            # 管理员连接
            conn = Connection(
                server,
                user=self.config.bind_dn,
                password=self.config.bind_password,
                authentication=SIMPLE,
                auto_bind=True
            )

        return conn

    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """LDAP用户认证"""
        try:
            # 首先用管理员连接搜索用户
            admin_conn = self._get_connection()

            # 搜索用户
            search_filter = self.config.user_filter.format(username=username)
            admin_conn.search(
                search_base=self.config.user_search_base,
                search_filter=search_filter,
                attributes=[
                    self.config.username_attr,
                    self.config.email_attr,
                    self.config.first_name_attr,
                    self.config.last_name_attr,
                    self.config.display_name_attr
                ]
            )

            if not admin_conn.entries:
                logger.warning(f"LDAP用户未找到: {username}")
                return None

            user_entry = admin_conn.entries[0]
            user_dn = user_entry.entry_dn

            # 尝试用用户凭据认证
            try:
                user_conn = Connection(
                    admin_conn.server,
                    user=user_dn,
                    password=password,
                    authentication=SIMPLE,
                    auto_bind=True
                )
                user_conn.unbind()
            except Exception as e:
                logger.warning(f"LDAP认证失败: {username} - {e}")
                return None

            # 获取用户信息
            user_info = self._extract_user_info(user_entry)

            # 获取用户组
            if self.config.sync_groups:
                user_info['groups'] = self._get_user_groups(admin_conn, user_dn)

            admin_conn.unbind()

            logger.info(f"LDAP认证成功: {username}")
            return user_info

        except Exception as e:
            logger.error(f"LDAP认证错误: {username} - {e}")
            return None

    def _extract_user_info(self, user_entry) -> Dict[str, Any]:
        """提取用户信息"""
        def get_attr_value(attr_name: str) -> str:
            """安全获取属性值"""
            try:
                attr = getattr(user_entry, attr_name, None)
                if attr and hasattr(attr, 'value'):
                    return str(attr.value) if attr.value else ""
                return ""
            except:
                return ""

        username = get_attr_value(self.config.username_attr)
        email = get_attr_value(self.config.email_attr)
        first_name = get_attr_value(self.config.first_name_attr)
        last_name = get_attr_value(self.config.last_name_attr)
        display_name = get_attr_value(self.config.display_name_attr)

        # 构建全名
        if display_name:
            full_name = display_name
        elif first_name or last_name:
            full_name = f"{first_name} {last_name}".strip()
        else:
            full_name = username

        return {
            'username': username,
            'email': email,
            'full_name': full_name,
            'first_name': first_name,
            'last_name': last_name,
            'dn': user_entry.entry_dn,
            'source': 'ldap'
        }

    def _get_user_groups(self, conn: Connection, user_dn: str) -> List[str]:
        """获取用户所属组"""
        try:
            search_filter = self.config.group_filter.format(user_dn=user_dn)
            conn.search(
                search_base=self.config.group_search_base,
                search_filter=search_filter,
                attributes=[self.config.group_name_attr]
            )

            groups = []
            for entry in conn.entries:
                group_name = getattr(entry, self.config.group_name_attr, None)
                if group_name and hasattr(group_name, 'value'):
                    groups.append(str(group_name.value))

            return groups

        except Exception as e:
            logger.error(f"获取用户组失败: {user_dn} - {e}")
            return []

    async def sync_user_to_database(self, ldap_user_info: Dict[str, Any]) -> Optional[User]:
        """同步LDAP用户到数据库"""
        if not self.user_service:
            return None

        try:
            username = ldap_user_info['username']

            # 查找现有用户
            existing_user = await self.user_service.get_by_username(username)

            if existing_user:
                # 更新现有用户
                if self.config.auto_update_users:
                    update_data = {
                        'email': ldap_user_info.get('email', ''),
                        'full_name': ldap_user_info.get('full_name', ''),
                        'auth_source': 'ldap',
                        'last_login': datetime.utcnow()
                    }

                    # 更新权限
                    if 'groups' in ldap_user_info:
                        update_data['is_superuser'] = any(
                            group in self.config.admin_groups
                            for group in ldap_user_info['groups']
                        )

                    await self.user_service.update_user(existing_user.id, update_data)
                    return existing_user
            else:
                # 创建新用户
                if self.config.auto_create_users:
                    # 创建用户对象
                    from ..models.user import User
                    new_user = User(
                        username=username,
                        email=ldap_user_info.get('email', ''),
                        full_name=ldap_user_info.get('full_name', ''),
                        password_hash='',  # LDAP用户不需要本地密码
                        auth_source='ldap',
                        is_active=True,
                        is_superuser=False
                    )

                    # 设置权限
                    if 'groups' in ldap_user_info:
                        new_user.is_superuser = any(
                            group in self.config.admin_groups
                            for group in ldap_user_info['groups']
                        )

                    self.db.add(new_user)
                    await self.db.commit()
                    await self.db.refresh(new_user)
                    return new_user

            return None

        except Exception as e:
            logger.error(f"同步LDAP用户失败: {ldap_user_info.get('username')} - {e}")
            return None

    def test_connection(self) -> Dict[str, Any]:
        """测试LDAP连接"""
        try:
            conn = self._get_connection()

            # 测试搜索
            conn.search(
                search_base=self.config.base_dn,
                search_filter='(objectClass=*)',
                search_scope='BASE',
                attributes=['*']
            )

            conn.unbind()

            return {
                'success': True,
                'message': 'LDAP连接测试成功',
                'server': self.config.server_uri,
                'base_dn': self.config.base_dn
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'LDAP连接测试失败: {str(e)}',
                'server': self.config.server_uri
            }


# 全局LDAP服务实例
ldap_service = LDAPService()
