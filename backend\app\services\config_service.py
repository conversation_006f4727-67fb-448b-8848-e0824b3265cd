"""
配置管理服务
支持运行时配置更新和认证方式动态切换
"""

import os
import json
from typing import Dict, Any, Optional
import logging
from datetime import datetime

from ..core.config import settings

logger = logging.getLogger(__name__)


class ConfigService:
    """配置管理服务"""
    
    def __init__(self):
        self.config_file = "config/auth_config.json"
        self.runtime_config = {}
        self.load_runtime_config()
    
    def load_runtime_config(self):
        """加载运行时配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.runtime_config = json.load(f)
            else:
                # 使用默认配置
                self.runtime_config = self.get_default_config()
                self.save_runtime_config()
        except Exception as e:
            logger.error(f"加载运行时配置失败: {e}")
            self.runtime_config = self.get_default_config()
    
    def save_runtime_config(self):
        """保存运行时配置"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.runtime_config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存运行时配置失败: {e}")
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "auth": {
                "local": {
                    "enabled": True,
                    "allow_registration": True,
                    "password_policy": {
                        "min_length": 6,
                        "require_uppercase": False,
                        "require_lowercase": False,
                        "require_numbers": False,
                        "require_symbols": False
                    }
                },
                "ldap": {
                    "enabled": settings.LDAP_ENABLED,
                    "server_uri": settings.LDAP_SERVER_URI,
                    "bind_dn": settings.LDAP_BIND_DN,
                    "base_dn": settings.LDAP_BASE_DN,
                    "user_search_base": settings.LDAP_USER_SEARCH_BASE,
                    "auto_create_users": settings.LDAP_AUTO_CREATE_USERS,
                    "auto_update_users": settings.LDAP_AUTO_UPDATE_USERS
                },
                "sso": {
                    "enabled": settings.SSO_ENABLED,
                    "type": settings.SSO_TYPE,
                    "auto_create_users": settings.SSO_AUTO_CREATE_USERS,
                    "auto_update_users": settings.SSO_AUTO_UPDATE_USERS,
                    "saml": {
                        "idp_url": settings.SAML_IDP_URL,
                        "sp_entity_id": settings.SAML_SP_ENTITY_ID,
                        "acs_url": settings.SAML_ACS_URL
                    },
                    "oauth": {
                        "client_id": settings.OAUTH_CLIENT_ID,
                        "auth_url": settings.OAUTH_AUTH_URL,
                        "token_url": settings.OAUTH_TOKEN_URL,
                        "userinfo_url": settings.OAUTH_USERINFO_URL,
                        "scope": settings.OAUTH_SCOPE
                    }
                }
            },
            "updated_at": datetime.now().isoformat(),
            "updated_by": "system"
        }
    
    def get_auth_config(self) -> Dict[str, Any]:
        """获取认证配置"""
        return self.runtime_config.get("auth", {})
    
    def is_ldap_enabled(self) -> bool:
        """检查LDAP是否启用"""
        return self.runtime_config.get("auth", {}).get("ldap", {}).get("enabled", False)
    
    def is_sso_enabled(self) -> bool:
        """检查SSO是否启用"""
        return self.runtime_config.get("auth", {}).get("sso", {}).get("enabled", False)
    
    def get_sso_type(self) -> str:
        """获取SSO类型"""
        return self.runtime_config.get("auth", {}).get("sso", {}).get("type", "saml")
    
    def update_ldap_config(self, config: Dict[str, Any], updated_by: str = "admin") -> bool:
        """更新LDAP配置"""
        try:
            if "auth" not in self.runtime_config:
                self.runtime_config["auth"] = {}
            if "ldap" not in self.runtime_config["auth"]:
                self.runtime_config["auth"]["ldap"] = {}
            
            # 更新LDAP配置
            self.runtime_config["auth"]["ldap"].update(config)
            self.runtime_config["updated_at"] = datetime.now().isoformat()
            self.runtime_config["updated_by"] = updated_by
            
            # 保存配置
            self.save_runtime_config()
            
            logger.info(f"LDAP配置已更新，操作者: {updated_by}")
            return True
            
        except Exception as e:
            logger.error(f"更新LDAP配置失败: {e}")
            return False
    
    def update_sso_config(self, config: Dict[str, Any], updated_by: str = "admin") -> bool:
        """更新SSO配置"""
        try:
            if "auth" not in self.runtime_config:
                self.runtime_config["auth"] = {}
            if "sso" not in self.runtime_config["auth"]:
                self.runtime_config["auth"]["sso"] = {}
            
            # 更新SSO配置
            self.runtime_config["auth"]["sso"].update(config)
            self.runtime_config["updated_at"] = datetime.now().isoformat()
            self.runtime_config["updated_by"] = updated_by
            
            # 保存配置
            self.save_runtime_config()
            
            logger.info(f"SSO配置已更新，操作者: {updated_by}")
            return True
            
        except Exception as e:
            logger.error(f"更新SSO配置失败: {e}")
            return False
    
    def toggle_ldap(self, enabled: bool, updated_by: str = "admin") -> bool:
        """切换LDAP启用状态"""
        return self.update_ldap_config({"enabled": enabled}, updated_by)
    
    def toggle_sso(self, enabled: bool, updated_by: str = "admin") -> bool:
        """切换SSO启用状态"""
        return self.update_sso_config({"enabled": enabled}, updated_by)
    
    def get_available_auth_methods(self) -> Dict[str, Any]:
        """获取可用的认证方法"""
        auth_config = self.get_auth_config()
        
        return {
            "local": {
                "enabled": auth_config.get("local", {}).get("enabled", True),
                "name": "本地认证",
                "description": "使用用户名和密码进行认证"
            },
            "ldap": {
                "enabled": auth_config.get("ldap", {}).get("enabled", False),
                "name": "LDAP认证",
                "description": "使用企业目录服务进行认证",
                "server": auth_config.get("ldap", {}).get("server_uri", "")
            },
            "sso": {
                "enabled": auth_config.get("sso", {}).get("enabled", False),
                "name": "SSO单点登录",
                "description": "使用单点登录服务进行认证",
                "type": auth_config.get("sso", {}).get("type", "saml")
            }
        }
    
    def validate_ldap_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证LDAP配置"""
        errors = []
        
        if config.get("enabled", False):
            if not config.get("server_uri"):
                errors.append("LDAP服务器URI不能为空")
            
            if not config.get("bind_dn"):
                errors.append("绑定DN不能为空")
            
            if not config.get("base_dn"):
                errors.append("基础DN不能为空")
            
            if not config.get("user_search_base"):
                errors.append("用户搜索基础不能为空")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    def validate_sso_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证SSO配置"""
        errors = []
        
        if config.get("enabled", False):
            sso_type = config.get("type", "saml")
            
            if sso_type == "saml":
                saml_config = config.get("saml", {})
                if not saml_config.get("idp_url"):
                    errors.append("SAML身份提供商URL不能为空")
                if not saml_config.get("sp_entity_id"):
                    errors.append("SAML服务提供商实体ID不能为空")
            
            elif sso_type in ["oauth", "oidc"]:
                oauth_config = config.get("oauth", {})
                if not oauth_config.get("client_id"):
                    errors.append("OAuth客户端ID不能为空")
                if not oauth_config.get("auth_url"):
                    errors.append("OAuth授权URL不能为空")
                if not oauth_config.get("token_url"):
                    errors.append("OAuth令牌URL不能为空")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }
    
    def export_config(self) -> Dict[str, Any]:
        """导出配置"""
        return {
            "config": self.runtime_config,
            "exported_at": datetime.now().isoformat(),
            "version": "1.0"
        }
    
    def import_config(self, config_data: Dict[str, Any], updated_by: str = "admin") -> bool:
        """导入配置"""
        try:
            if "config" in config_data:
                self.runtime_config = config_data["config"]
                self.runtime_config["updated_at"] = datetime.now().isoformat()
                self.runtime_config["updated_by"] = updated_by
                
                self.save_runtime_config()
                logger.info(f"配置导入成功，操作者: {updated_by}")
                return True
            else:
                logger.error("配置数据格式错误")
                return False
                
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False
    
    def reset_to_defaults(self, updated_by: str = "admin") -> bool:
        """重置为默认配置"""
        try:
            self.runtime_config = self.get_default_config()
            self.runtime_config["updated_by"] = updated_by
            self.save_runtime_config()
            
            logger.info(f"配置已重置为默认值，操作者: {updated_by}")
            return True
            
        except Exception as e:
            logger.error(f"重置配置失败: {e}")
            return False


# 全局配置服务实例
config_service = ConfigService()
