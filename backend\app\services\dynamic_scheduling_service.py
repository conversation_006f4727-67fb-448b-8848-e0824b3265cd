"""
动态排程调整服务
实现实时响应的智能排程调整系统
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass, field
from enum import Enum
import json
from collections import defaultdict

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_

from ..core.config import settings
from ..models.production import ProductionOrder, WorkOrder, Equipment, ProductionSchedule
from ..services.mes_integration_service import mes_integration_service
from ..services.demand_forecasting_service import demand_forecasting_service

logger = logging.getLogger(__name__)


class DisruptionType(Enum):
    """生产中断类型"""
    EQUIPMENT_FAILURE = "equipment_failure"
    MATERIAL_SHORTAGE = "material_shortage"
    QUALITY_ISSUE = "quality_issue"
    URGENT_ORDER = "urgent_order"
    STAFF_SHORTAGE = "staff_shortage"
    SUPPLIER_DELAY = "supplier_delay"


class ScheduleAction(Enum):
    """排程调整动作"""
    RESCHEDULE = "reschedule"
    PRIORITY_CHANGE = "priority_change"
    RESOURCE_REALLOCATION = "resource_reallocation"
    ORDER_SPLIT = "order_split"
    ORDER_MERGE = "order_merge"
    ALTERNATIVE_ROUTE = "alternative_route"


@dataclass
class DisruptionEvent:
    """生产中断事件"""
    event_id: str
    event_type: DisruptionType
    severity: int  # 1-5, 5最严重
    affected_resources: List[str]
    estimated_duration: timedelta
    description: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    resolved: bool = False


@dataclass
class ScheduleAdjustment:
    """排程调整"""
    adjustment_id: str
    action: ScheduleAction
    affected_orders: List[str]
    original_schedule: Dict[str, Any]
    new_schedule: Dict[str, Any]
    impact_score: float
    reason: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    applied: bool = False


@dataclass
class ReschedulingConfig:
    """重排程配置"""
    max_response_time: int = 300  # 最大响应时间（秒）
    priority_weights: Dict[str, float] = field(default_factory=lambda: {
        'delivery_date': 0.4,
        'customer_priority': 0.3,
        'order_value': 0.2,
        'setup_time': 0.1
    })
    disruption_thresholds: Dict[DisruptionType, int] = field(default_factory=lambda: {
        DisruptionType.EQUIPMENT_FAILURE: 3,
        DisruptionType.MATERIAL_SHORTAGE: 2,
        DisruptionType.QUALITY_ISSUE: 2,
        DisruptionType.URGENT_ORDER: 4,
        DisruptionType.STAFF_SHORTAGE: 2,
        DisruptionType.SUPPLIER_DELAY: 1
    })
    auto_approval_threshold: float = 0.8  # 自动批准阈值


class DynamicSchedulingService:
    """动态排程调整服务"""
    
    def __init__(self, db_session: AsyncSession = None):
        self.db = db_session
        self.config = ReschedulingConfig()
        self.active_disruptions = {}
        self.pending_adjustments = {}
        self.monitoring_tasks = {}
        self.is_running = False
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        self.config.max_response_time = getattr(settings, 'DYNAMIC_SCHEDULING_RESPONSE_TIME', 300)
        self.config.auto_approval_threshold = getattr(settings, 'AUTO_APPROVAL_THRESHOLD', 0.8)
    
    async def start_monitoring(self):
        """启动动态监控"""
        if self.is_running:
            logger.warning("动态排程监控已在运行")
            return
        
        self.is_running = True
        
        # 启动监控任务
        self.monitoring_tasks = {
            'equipment_monitor': asyncio.create_task(self._monitor_equipment_status()),
            'order_monitor': asyncio.create_task(self._monitor_order_changes()),
            'quality_monitor': asyncio.create_task(self._monitor_quality_issues()),
            'material_monitor': asyncio.create_task(self._monitor_material_status())
        }
        
        logger.info("动态排程监控已启动")
    
    async def stop_monitoring(self):
        """停止动态监控"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 取消所有监控任务
        for task_name, task in self.monitoring_tasks.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                logger.info(f"已取消{task_name}监控任务")
        
        logger.info("动态排程监控已停止")
    
    async def _monitor_equipment_status(self):
        """监控设备状态"""
        while self.is_running:
            try:
                # 获取设备状态
                equipment_status = await self._get_equipment_status()
                
                for equipment in equipment_status:
                    if equipment['status'] in ['ERROR', 'MAINTENANCE']:
                        await self._handle_equipment_disruption(equipment)
                
                await asyncio.sleep(30)  # 30秒检查一次
                
            except Exception as e:
                logger.error(f"监控设备状态失败: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_order_changes(self):
        """监控订单变更"""
        while self.is_running:
            try:
                # 检查新的紧急订单
                urgent_orders = await self._get_urgent_orders()
                
                for order in urgent_orders:
                    await self._handle_urgent_order(order)
                
                await asyncio.sleep(60)  # 1分钟检查一次
                
            except Exception as e:
                logger.error(f"监控订单变更失败: {e}")
                await asyncio.sleep(60)
    
    async def _monitor_quality_issues(self):
        """监控质量问题"""
        while self.is_running:
            try:
                # 检查质量问题
                quality_issues = await self._get_quality_issues()
                
                for issue in quality_issues:
                    await self._handle_quality_disruption(issue)
                
                await asyncio.sleep(120)  # 2分钟检查一次
                
            except Exception as e:
                logger.error(f"监控质量问题失败: {e}")
                await asyncio.sleep(120)
    
    async def _monitor_material_status(self):
        """监控物料状态"""
        while self.is_running:
            try:
                # 检查物料短缺
                material_shortages = await self._get_material_shortages()
                
                for shortage in material_shortages:
                    await self._handle_material_disruption(shortage)
                
                await asyncio.sleep(180)  # 3分钟检查一次
                
            except Exception as e:
                logger.error(f"监控物料状态失败: {e}")
                await asyncio.sleep(180)
    
    async def handle_disruption(self, disruption: DisruptionEvent) -> Dict[str, Any]:
        """处理生产中断"""
        try:
            logger.info(f"处理生产中断: {disruption.event_type.value} - {disruption.description}")
            
            # 记录中断事件
            self.active_disruptions[disruption.event_id] = disruption
            
            # 评估影响
            impact_analysis = await self._analyze_disruption_impact(disruption)
            
            # 生成调整方案
            adjustments = await self._generate_schedule_adjustments(disruption, impact_analysis)
            
            # 评估调整方案
            best_adjustment = await self._evaluate_adjustments(adjustments)
            
            if best_adjustment:
                # 检查是否需要自动批准
                if best_adjustment.impact_score >= self.config.auto_approval_threshold:
                    await self._apply_adjustment(best_adjustment)
                    status = "auto_applied"
                else:
                    # 等待人工批准
                    self.pending_adjustments[best_adjustment.adjustment_id] = best_adjustment
                    status = "pending_approval"
                
                return {
                    "status": status,
                    "adjustment_id": best_adjustment.adjustment_id,
                    "impact_score": best_adjustment.impact_score,
                    "affected_orders": best_adjustment.affected_orders,
                    "action": best_adjustment.action.value,
                    "reason": best_adjustment.reason
                }
            else:
                return {
                    "status": "no_adjustment_needed",
                    "reason": "未找到有效的调整方案"
                }
                
        except Exception as e:
            logger.error(f"处理生产中断失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _analyze_disruption_impact(self, disruption: DisruptionEvent) -> Dict[str, Any]:
        """分析中断影响"""
        try:
            # 获取受影响的订单
            affected_orders = await self._get_affected_orders(disruption)
            
            # 计算延误时间
            total_delay = timedelta()
            critical_orders = []
            
            for order in affected_orders:
                order_delay = self._calculate_order_delay(order, disruption)
                total_delay += order_delay
                
                if order_delay > timedelta(hours=24):
                    critical_orders.append(order['order_id'])
            
            # 计算成本影响
            cost_impact = self._calculate_cost_impact(affected_orders, disruption)
            
            return {
                "affected_orders_count": len(affected_orders),
                "critical_orders": critical_orders,
                "total_delay_hours": total_delay.total_seconds() / 3600,
                "cost_impact": cost_impact,
                "severity_score": self._calculate_severity_score(disruption, affected_orders)
            }
            
        except Exception as e:
            logger.error(f"分析中断影响失败: {e}")
            return {}
    
    async def _generate_schedule_adjustments(self, disruption: DisruptionEvent, impact_analysis: Dict[str, Any]) -> List[ScheduleAdjustment]:
        """生成排程调整方案"""
        try:
            adjustments = []
            
            # 根据中断类型生成不同的调整策略
            if disruption.event_type == DisruptionType.EQUIPMENT_FAILURE:
                adjustments.extend(await self._generate_equipment_failure_adjustments(disruption, impact_analysis))
            
            elif disruption.event_type == DisruptionType.URGENT_ORDER:
                adjustments.extend(await self._generate_urgent_order_adjustments(disruption, impact_analysis))
            
            elif disruption.event_type == DisruptionType.MATERIAL_SHORTAGE:
                adjustments.extend(await self._generate_material_shortage_adjustments(disruption, impact_analysis))
            
            elif disruption.event_type == DisruptionType.QUALITY_ISSUE:
                adjustments.extend(await self._generate_quality_issue_adjustments(disruption, impact_analysis))
            
            return adjustments
            
        except Exception as e:
            logger.error(f"生成排程调整方案失败: {e}")
            return []
    
    async def _generate_equipment_failure_adjustments(self, disruption: DisruptionEvent, impact_analysis: Dict[str, Any]) -> List[ScheduleAdjustment]:
        """生成设备故障调整方案"""
        adjustments = []
        
        try:
            # 方案1: 转移到备用设备
            alternative_equipment = await self._find_alternative_equipment(disruption.affected_resources[0])
            if alternative_equipment:
                adjustment = ScheduleAdjustment(
                    adjustment_id=f"eq_fail_{disruption.event_id}_alt",
                    action=ScheduleAction.ALTERNATIVE_ROUTE,
                    affected_orders=impact_analysis.get('critical_orders', []),
                    original_schedule={},
                    new_schedule={'alternative_equipment': alternative_equipment},
                    impact_score=0.8,
                    reason=f"转移到备用设备 {alternative_equipment}"
                )
                adjustments.append(adjustment)
            
            # 方案2: 重新排程
            adjustment = ScheduleAdjustment(
                adjustment_id=f"eq_fail_{disruption.event_id}_reschedule",
                action=ScheduleAction.RESCHEDULE,
                affected_orders=impact_analysis.get('critical_orders', []),
                original_schedule={},
                new_schedule={'delay_hours': disruption.estimated_duration.total_seconds() / 3600},
                impact_score=0.6,
                reason=f"延后排程 {disruption.estimated_duration.total_seconds() / 3600} 小时"
            )
            adjustments.append(adjustment)
            
        except Exception as e:
            logger.error(f"生成设备故障调整方案失败: {e}")
        
        return adjustments
    
    async def _generate_urgent_order_adjustments(self, disruption: DisruptionEvent, impact_analysis: Dict[str, Any]) -> List[ScheduleAdjustment]:
        """生成紧急订单调整方案"""
        adjustments = []
        
        try:
            # 方案1: 提高优先级并重排
            adjustment = ScheduleAdjustment(
                adjustment_id=f"urgent_{disruption.event_id}_priority",
                action=ScheduleAction.PRIORITY_CHANGE,
                affected_orders=[disruption.event_id],
                original_schedule={},
                new_schedule={'new_priority': 1},
                impact_score=0.9,
                reason="紧急订单优先级调整"
            )
            adjustments.append(adjustment)
            
            # 方案2: 插单处理
            adjustment = ScheduleAdjustment(
                adjustment_id=f"urgent_{disruption.event_id}_insert",
                action=ScheduleAction.RESCHEDULE,
                affected_orders=[disruption.event_id],
                original_schedule={},
                new_schedule={'insert_immediately': True},
                impact_score=0.85,
                reason="紧急订单插单处理"
            )
            adjustments.append(adjustment)
            
        except Exception as e:
            logger.error(f"生成紧急订单调整方案失败: {e}")
        
        return adjustments
    
    async def _generate_material_shortage_adjustments(self, disruption: DisruptionEvent, impact_analysis: Dict[str, Any]) -> List[ScheduleAdjustment]:
        """生成物料短缺调整方案"""
        adjustments = []
        
        try:
            # 方案1: 延后相关订单
            adjustment = ScheduleAdjustment(
                adjustment_id=f"material_{disruption.event_id}_delay",
                action=ScheduleAction.RESCHEDULE,
                affected_orders=impact_analysis.get('critical_orders', []),
                original_schedule={},
                new_schedule={'delay_until_material_available': True},
                impact_score=0.7,
                reason="等待物料到货"
            )
            adjustments.append(adjustment)
            
            # 方案2: 寻找替代物料
            adjustment = ScheduleAdjustment(
                adjustment_id=f"material_{disruption.event_id}_substitute",
                action=ScheduleAction.ALTERNATIVE_ROUTE,
                affected_orders=impact_analysis.get('critical_orders', []),
                original_schedule={},
                new_schedule={'use_substitute_material': True},
                impact_score=0.6,
                reason="使用替代物料"
            )
            adjustments.append(adjustment)
            
        except Exception as e:
            logger.error(f"生成物料短缺调整方案失败: {e}")
        
        return adjustments
    
    async def _generate_quality_issue_adjustments(self, disruption: DisruptionEvent, impact_analysis: Dict[str, Any]) -> List[ScheduleAdjustment]:
        """生成质量问题调整方案"""
        adjustments = []
        
        try:
            # 方案1: 返工处理
            adjustment = ScheduleAdjustment(
                adjustment_id=f"quality_{disruption.event_id}_rework",
                action=ScheduleAction.RESCHEDULE,
                affected_orders=impact_analysis.get('critical_orders', []),
                original_schedule={},
                new_schedule={'add_rework_time': True},
                impact_score=0.7,
                reason="安排返工处理"
            )
            adjustments.append(adjustment)
            
            # 方案2: 重新生产
            adjustment = ScheduleAdjustment(
                adjustment_id=f"quality_{disruption.event_id}_reproduce",
                action=ScheduleAction.ORDER_SPLIT,
                affected_orders=impact_analysis.get('critical_orders', []),
                original_schedule={},
                new_schedule={'create_new_production_order': True},
                impact_score=0.5,
                reason="重新安排生产"
            )
            adjustments.append(adjustment)
            
        except Exception as e:
            logger.error(f"生成质量问题调整方案失败: {e}")
        
        return adjustments
    
    async def _evaluate_adjustments(self, adjustments: List[ScheduleAdjustment]) -> Optional[ScheduleAdjustment]:
        """评估调整方案"""
        if not adjustments:
            return None
        
        # 按影响分数排序
        adjustments.sort(key=lambda x: x.impact_score, reverse=True)
        
        # 返回最佳方案
        return adjustments[0]
    
    async def _apply_adjustment(self, adjustment: ScheduleAdjustment) -> bool:
        """应用调整方案"""
        try:
            logger.info(f"应用排程调整: {adjustment.adjustment_id}")
            
            # 根据调整类型执行相应操作
            if adjustment.action == ScheduleAction.RESCHEDULE:
                await self._reschedule_orders(adjustment.affected_orders, adjustment.new_schedule)
            
            elif adjustment.action == ScheduleAction.PRIORITY_CHANGE:
                await self._change_order_priorities(adjustment.affected_orders, adjustment.new_schedule)
            
            elif adjustment.action == ScheduleAction.ALTERNATIVE_ROUTE:
                await self._apply_alternative_route(adjustment.affected_orders, adjustment.new_schedule)
            
            # 标记为已应用
            adjustment.applied = True
            
            # 记录调整历史
            await self._record_adjustment_history(adjustment)
            
            logger.info(f"排程调整已应用: {adjustment.adjustment_id}")
            return True
            
        except Exception as e:
            logger.error(f"应用排程调整失败: {e}")
            return False
    
    async def approve_adjustment(self, adjustment_id: str) -> Dict[str, Any]:
        """批准调整方案"""
        try:
            if adjustment_id not in self.pending_adjustments:
                return {"status": "error", "message": "调整方案不存在"}
            
            adjustment = self.pending_adjustments[adjustment_id]
            success = await self._apply_adjustment(adjustment)
            
            if success:
                del self.pending_adjustments[adjustment_id]
                return {"status": "success", "message": "调整方案已批准并应用"}
            else:
                return {"status": "error", "message": "应用调整方案失败"}
                
        except Exception as e:
            logger.error(f"批准调整方案失败: {e}")
            return {"status": "error", "message": str(e)}
    
    async def reject_adjustment(self, adjustment_id: str, reason: str = "") -> Dict[str, Any]:
        """拒绝调整方案"""
        try:
            if adjustment_id not in self.pending_adjustments:
                return {"status": "error", "message": "调整方案不存在"}
            
            adjustment = self.pending_adjustments[adjustment_id]
            del self.pending_adjustments[adjustment_id]
            
            # 记录拒绝原因
            await self._record_rejection(adjustment, reason)
            
            return {"status": "success", "message": "调整方案已拒绝"}
            
        except Exception as e:
            logger.error(f"拒绝调整方案失败: {e}")
            return {"status": "error", "message": str(e)}
    
    async def get_pending_adjustments(self) -> List[Dict[str, Any]]:
        """获取待批准的调整方案"""
        try:
            return [
                {
                    "adjustment_id": adj.adjustment_id,
                    "action": adj.action.value,
                    "affected_orders": adj.affected_orders,
                    "impact_score": adj.impact_score,
                    "reason": adj.reason,
                    "timestamp": adj.timestamp.isoformat()
                }
                for adj in self.pending_adjustments.values()
            ]
            
        except Exception as e:
            logger.error(f"获取待批准调整方案失败: {e}")
            return []
    
    async def get_disruption_status(self) -> Dict[str, Any]:
        """获取中断状态"""
        try:
            active_count = len([d for d in self.active_disruptions.values() if not d.resolved])
            pending_count = len(self.pending_adjustments)
            
            return {
                "active_disruptions": active_count,
                "pending_adjustments": pending_count,
                "monitoring_status": "running" if self.is_running else "stopped",
                "last_check": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取中断状态失败: {e}")
            return {}
    
    # 辅助方法
    async def _get_equipment_status(self) -> List[Dict[str, Any]]:
        """获取设备状态"""
        # 从MES系统或数据库获取设备状态
        return []
    
    async def _get_urgent_orders(self) -> List[Dict[str, Any]]:
        """获取紧急订单"""
        # 从数据库获取新的紧急订单
        return []
    
    async def _get_quality_issues(self) -> List[Dict[str, Any]]:
        """获取质量问题"""
        # 从质量系统获取质量问题
        return []
    
    async def _get_material_shortages(self) -> List[Dict[str, Any]]:
        """获取物料短缺"""
        # 从库存系统获取物料短缺信息
        return []
    
    async def _handle_equipment_disruption(self, equipment: Dict[str, Any]):
        """处理设备中断"""
        disruption = DisruptionEvent(
            event_id=f"eq_{equipment['equipment_id']}_{datetime.utcnow().timestamp()}",
            event_type=DisruptionType.EQUIPMENT_FAILURE,
            severity=4,
            affected_resources=[equipment['equipment_id']],
            estimated_duration=timedelta(hours=2),
            description=f"设备 {equipment['equipment_id']} 故障"
        )
        await self.handle_disruption(disruption)
    
    async def _handle_urgent_order(self, order: Dict[str, Any]):
        """处理紧急订单"""
        disruption = DisruptionEvent(
            event_id=order['order_id'],
            event_type=DisruptionType.URGENT_ORDER,
            severity=5,
            affected_resources=[],
            estimated_duration=timedelta(hours=0),
            description=f"紧急订单 {order['order_id']}"
        )
        await self.handle_disruption(disruption)
    
    async def _handle_quality_disruption(self, issue: Dict[str, Any]):
        """处理质量问题"""
        disruption = DisruptionEvent(
            event_id=f"quality_{issue['order_id']}_{datetime.utcnow().timestamp()}",
            event_type=DisruptionType.QUALITY_ISSUE,
            severity=3,
            affected_resources=[],
            estimated_duration=timedelta(hours=4),
            description=f"质量问题: {issue['description']}"
        )
        await self.handle_disruption(disruption)
    
    async def _handle_material_disruption(self, shortage: Dict[str, Any]):
        """处理物料短缺"""
        disruption = DisruptionEvent(
            event_id=f"material_{shortage['material_id']}_{datetime.utcnow().timestamp()}",
            event_type=DisruptionType.MATERIAL_SHORTAGE,
            severity=2,
            affected_resources=[],
            estimated_duration=timedelta(days=1),
            description=f"物料短缺: {shortage['material_name']}"
        )
        await self.handle_disruption(disruption)
    
    async def _get_affected_orders(self, disruption: DisruptionEvent) -> List[Dict[str, Any]]:
        """获取受影响的订单"""
        # 实现获取受影响订单的逻辑
        return []
    
    def _calculate_order_delay(self, order: Dict[str, Any], disruption: DisruptionEvent) -> timedelta:
        """计算订单延误时间"""
        # 实现延误时间计算逻辑
        return disruption.estimated_duration
    
    def _calculate_cost_impact(self, affected_orders: List[Dict[str, Any]], disruption: DisruptionEvent) -> float:
        """计算成本影响"""
        # 实现成本影响计算逻辑
        return len(affected_orders) * 1000.0
    
    def _calculate_severity_score(self, disruption: DisruptionEvent, affected_orders: List[Dict[str, Any]]) -> float:
        """计算严重程度分数"""
        base_score = disruption.severity / 5.0
        order_impact = min(len(affected_orders) / 10.0, 1.0)
        return (base_score + order_impact) / 2.0
    
    async def _find_alternative_equipment(self, failed_equipment: str) -> Optional[str]:
        """寻找备用设备"""
        # 实现备用设备查找逻辑
        return None
    
    async def _reschedule_orders(self, order_ids: List[str], new_schedule: Dict[str, Any]):
        """重新排程订单"""
        # 实现订单重排程逻辑
        pass
    
    async def _change_order_priorities(self, order_ids: List[str], new_schedule: Dict[str, Any]):
        """修改订单优先级"""
        # 实现优先级修改逻辑
        pass
    
    async def _apply_alternative_route(self, order_ids: List[str], new_schedule: Dict[str, Any]):
        """应用替代路径"""
        # 实现替代路径应用逻辑
        pass
    
    async def _record_adjustment_history(self, adjustment: ScheduleAdjustment):
        """记录调整历史"""
        # 实现调整历史记录逻辑
        pass
    
    async def _record_rejection(self, adjustment: ScheduleAdjustment, reason: str):
        """记录拒绝原因"""
        # 实现拒绝记录逻辑
        pass


# 全局动态排程服务实例
dynamic_scheduling_service = DynamicSchedulingService()
