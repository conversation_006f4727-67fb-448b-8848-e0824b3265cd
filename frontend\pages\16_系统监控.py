"""
系统监控页面
提供系统性能监控、健康检查、运行统计等功能
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import sys
import os

# 页面配置
st.set_page_config(
    page_title="系统监控 - Smart Planning",
    page_icon="📊",
    layout="wide"
)

# 导入通用组件
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.auth import require_auth
from config.settings import API_BASE_URL

# 认证检查
require_auth()

# 页面标题
st.title("📊 系统监控中心")
st.markdown("### 🔍 系统性能、健康状态、运行统计实时监控")

# 功能说明
with st.expander("💡 监控功能说明"):
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        **📈 性能监控**
        - CPU和内存使用率
        - 数据库连接状态
        - API响应时间
        - 系统负载监控
        """)
    
    with col2:
        st.markdown("""
        **🔍 健康检查**
        - 系统组件状态
        - 服务可用性检查
        - 自动故障检测
        - 异常警报通知
        """)
    
    with col3:
        st.markdown("""
        **📊 运行统计**
        - 用户活动统计
        - 处理任务统计
        - 错误日志分析
        - 性能趋势分析
        """)

st.markdown("---")

# 主要标签页
tab1, tab2, tab3, tab4 = st.tabs([
    "📈 性能监控",
    "🔍 健康检查",
    "📊 运行统计",
    "🚨 警报管理"
])

# ==================== 标签页1: 性能监控 ====================
with tab1:
    st.markdown("#### 📈 系统性能实时监控")
    
    # 实时性能指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="CPU使用率",
            value="45.2%",
            delta="-2.1%",
            help="当前CPU使用率"
        )
    
    with col2:
        st.metric(
            label="内存使用率",
            value="67.8%",
            delta="+1.5%",
            help="当前内存使用率"
        )
    
    with col3:
        st.metric(
            label="磁盘使用率",
            value="34.6%",
            delta="+0.8%",
            help="当前磁盘使用率"
        )
    
    with col4:
        st.metric(
            label="网络延迟",
            value="15ms",
            delta="-3ms",
            help="平均网络响应时间"
        )
    
    st.markdown("---")
    
    # 性能图表
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("##### 📊 CPU和内存使用趋势")
        
        # 模拟性能数据
        time_range = pd.date_range(start=datetime.now() - timedelta(hours=24), end=datetime.now(), freq='H')
        cpu_data = [45 + i*0.5 + (i%3)*2 for i in range(len(time_range))]
        memory_data = [67 + i*0.3 + (i%4)*1.5 for i in range(len(time_range))]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=time_range, y=cpu_data, mode='lines', name='CPU使用率', line=dict(color='#ff6b6b')))
        fig.add_trace(go.Scatter(x=time_range, y=memory_data, mode='lines', name='内存使用率', line=dict(color='#4ecdc4')))
        
        fig.update_layout(
            title="系统资源使用趋势",
            xaxis_title="时间",
            yaxis_title="使用率 (%)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.markdown("##### 🌐 API响应时间监控")
        
        # 模拟API响应时间数据
        api_endpoints = ['数据上传', '生产规划', '设备管理', '数据分析', 'PCI管理']
        response_times = [120, 85, 95, 150, 110]
        
        fig = px.bar(
            x=api_endpoints,
            y=response_times,
            title="各API端点响应时间",
            labels={'x': 'API端点', 'y': '响应时间 (ms)'},
            color=response_times,
            color_continuous_scale='RdYlGn_r'
        )
        
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)
    
    # 数据库连接状态
    st.markdown("##### 🗄️ 数据库连接状态")
    
    db_status = [
        {"数据库": "生产数据库", "类型": "MySQL", "状态": "🟢 正常", "连接数": "15/50", "响应时间": "12ms"},
        {"数据库": "分析数据库", "类型": "SSAS", "状态": "🟢 正常", "连接数": "8/20", "响应时间": "25ms"},
        {"数据库": "历史数据库", "类型": "PostgreSQL", "状态": "🟡 警告", "连接数": "45/50", "响应时间": "35ms"},
        {"数据库": "缓存数据库", "类型": "Redis", "状态": "🟢 正常", "连接数": "12/30", "响应时间": "5ms"}
    ]
    
    df_db = pd.DataFrame(db_status)
    st.dataframe(df_db, use_container_width=True, hide_index=True)

# ==================== 标签页2: 健康检查 ====================
with tab2:
    st.markdown("#### 🔍 系统健康状态检查")
    
    # 系统组件状态
    st.markdown("##### 🔧 系统组件状态")
    
    components = [
        {"组件": "Web服务器", "状态": "🟢 运行中", "版本": "v1.2.3", "启动时间": "2024-01-15 08:00:00", "内存": "256MB"},
        {"组件": "数据处理服务", "状态": "🟢 运行中", "版本": "v2.1.0", "启动时间": "2024-01-15 08:01:30", "内存": "512MB"},
        {"组件": "算法引擎", "状态": "🟢 运行中", "版本": "v1.5.2", "启动时间": "2024-01-15 08:02:15", "内存": "1GB"},
        {"组件": "LLM服务", "状态": "🟡 警告", "版本": "v3.0.1", "启动时间": "2024-01-15 08:03:00", "内存": "2GB"},
        {"组件": "邮件服务", "状态": "🟢 运行中", "版本": "v1.0.5", "启动时间": "2024-01-15 08:00:45", "内存": "128MB"}
    ]
    
    df_components = pd.DataFrame(components)
    st.dataframe(df_components, use_container_width=True, hide_index=True)
    
    # 健康检查操作
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔍 执行全面健康检查", type="primary"):
            with st.spinner("正在执行健康检查..."):
                # 模拟健康检查
                st.success("✅ 系统健康检查完成")
                st.info("📋 检查了5个组件，发现1个警告")
    
    with col2:
        if st.button("🔄 重启异常服务"):
            st.info("🔄 正在重启LLM服务...")
            st.success("✅ LLM服务重启成功")
    
    with col3:
        if st.button("📧 发送健康报告"):
            st.success("✅ 健康报告已发送到管理员邮箱")
    
    # 服务可用性监控
    st.markdown("##### 🌐 服务可用性监控")
    
    # 模拟可用性数据
    services = ['Web界面', 'API服务', '数据库', 'LLM服务', '邮件服务']
    uptime = [99.9, 99.8, 99.95, 98.5, 99.7]
    
    fig = px.bar(
        x=services,
        y=uptime,
        title="服务可用性统计 (过去30天)",
        labels={'x': '服务', 'y': '可用性 (%)'},
        color=uptime,
        color_continuous_scale='RdYlGn'
    )
    
    fig.update_layout(height=400)
    st.plotly_chart(fig, use_container_width=True)

# ==================== 标签页3: 运行统计 ====================
with tab3:
    st.markdown("#### 📊 系统运行统计分析")
    
    # 用户活动统计
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("##### 👥 用户活动统计")
        
        # 模拟用户活动数据
        user_stats = {
            "今日活跃用户": 25,
            "本周活跃用户": 45,
            "本月活跃用户": 78,
            "总注册用户": 120
        }
        
        for stat, value in user_stats.items():
            st.metric(stat, value)
    
    with col2:
        st.markdown("##### 📈 页面访问统计")
        
        # 模拟页面访问数据
        page_visits = {
            "综合仪表板": 156,
            "生产规划": 89,
            "数据分析": 67,
            "设备管理": 45,
            "PCI管理": 34
        }
        
        fig = px.pie(
            values=list(page_visits.values()),
            names=list(page_visits.keys()),
            title="页面访问分布"
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # 任务处理统计
    st.markdown("##### ⚙️ 任务处理统计")
    
    task_stats = [
        {"任务类型": "数据上传", "今日处理": 45, "成功率": "98.5%", "平均耗时": "2.3s"},
        {"任务类型": "生产规划", "今日处理": 23, "成功率": "100%", "平均耗时": "15.6s"},
        {"任务类型": "数据分析", "今日处理": 67, "成功率": "96.8%", "平均耗时": "8.2s"},
        {"任务类型": "算法计算", "今日处理": 12, "成功率": "95.2%", "平均耗时": "45.3s"},
        {"任务类型": "报告生成", "今日处理": 8, "成功率": "100%", "平均耗时": "12.1s"}
    ]
    
    df_tasks = pd.DataFrame(task_stats)
    st.dataframe(df_tasks, use_container_width=True, hide_index=True)
    
    # 错误日志分析
    st.markdown("##### 🚨 错误日志分析")
    
    error_logs = [
        {"时间": "2024-01-15 14:23:15", "级别": "ERROR", "组件": "LLM服务", "消息": "连接超时", "状态": "已处理"},
        {"时间": "2024-01-15 13:45:32", "级别": "WARNING", "组件": "数据库", "消息": "连接池接近满载", "状态": "监控中"},
        {"时间": "2024-01-15 12:18:47", "级别": "ERROR", "组件": "API服务", "消息": "请求频率过高", "状态": "已处理"},
        {"时间": "2024-01-15 11:32:21", "级别": "INFO", "组件": "系统", "消息": "定时备份完成", "状态": "正常"}
    ]
    
    df_errors = pd.DataFrame(error_logs)
    st.dataframe(df_errors, use_container_width=True, hide_index=True)

# ==================== 标签页4: 警报管理 ====================
with tab4:
    st.markdown("#### 🚨 系统警报管理")
    
    # 警报配置
    st.markdown("##### ⚙️ 警报配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**性能警报阈值:**")
        cpu_threshold = st.slider("CPU使用率警报阈值 (%)", 0, 100, 80)
        memory_threshold = st.slider("内存使用率警报阈值 (%)", 0, 100, 85)
        disk_threshold = st.slider("磁盘使用率警报阈值 (%)", 0, 100, 90)
    
    with col2:
        st.markdown("**响应时间警报阈值:**")
        api_threshold = st.number_input("API响应时间阈值 (ms)", min_value=0, value=200)
        db_threshold = st.number_input("数据库响应时间阈值 (ms)", min_value=0, value=100)
        
        st.markdown("**通知设置:**")
        email_alerts = st.checkbox("启用邮件警报", value=True)
        sms_alerts = st.checkbox("启用短信警报", value=False)
    
    if st.button("💾 保存警报配置", type="primary"):
        st.success("✅ 警报配置已保存")
    
    # 当前警报
    st.markdown("##### 🔔 当前警报")
    
    current_alerts = [
        {"时间": "2024-01-15 14:30:00", "级别": "🟡 警告", "类型": "性能", "描述": "LLM服务内存使用率超过80%", "状态": "未处理"},
        {"时间": "2024-01-15 13:15:22", "级别": "🔴 严重", "类型": "连接", "描述": "历史数据库连接池接近满载", "状态": "处理中"},
        {"时间": "2024-01-15 12:45:18", "级别": "🟢 信息", "类型": "系统", "描述": "系统备份成功完成", "状态": "已确认"}
    ]
    
    for alert in current_alerts:
        with st.expander(f"{alert['级别']} {alert['描述']} - {alert['时间']}"):
            col1, col2, col3 = st.columns(3)
            with col1:
                st.write(f"**类型**: {alert['类型']}")
                st.write(f"**状态**: {alert['状态']}")
            with col2:
                if st.button("✅ 确认", key=f"confirm_{alert['时间']}"):
                    st.success("警报已确认")
            with col3:
                if st.button("🔇 忽略", key=f"ignore_{alert['时间']}"):
                    st.info("警报已忽略")

# 页面底部说明
st.markdown("---")
st.markdown("""
### 💡 系统监控中心功能

**📈 实时监控**: CPU、内存、磁盘、网络等系统资源实时监控
**🔍 健康检查**: 自动检测系统组件状态，及时发现问题
**📊 统计分析**: 用户活动、任务处理、错误日志等统计分析
**🚨 智能警报**: 可配置的警报阈值，多种通知方式
**🔧 自动化**: 自动故障检测、自动重启、自动报告生成

通过系统监控中心，管理员可以全面掌握Smart Planning系统的运行状态，确保系统稳定可靠运行。
""")
