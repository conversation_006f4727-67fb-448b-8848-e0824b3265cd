"""
邮件表格提取功能测试
"""

import pytest
import io
from unittest.mock import Mock
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'frontend'))

from utils.file_utils import (
    _extract_latest_reply,
    _clean_email_content,
    _extract_html_tables_from_text,
    _extract_text_tables_from_text,
    _extract_csv_tables_from_text,
    _extract_email_tables
)


class TestEmailLatestReplyExtraction:
    """测试邮件最新回复提取功能"""
    
    def test_outlook_separator(self):
        """测试Outlook分隔符"""
        email_body = """
最新回复内容：
产品A  100  2024-01-15
产品B  200  2024-01-20

-----Original Message-----
From: <EMAIL>
Sent: Monday, January 01, 2024 10:00 AM
To: <EMAIL>
Subject: Re: 生产计划

原始邮件内容...
"""
        result = _extract_latest_reply(email_body)
        assert "最新回复内容" in result
        assert "Original Message" not in result
        assert "原始邮件内容" not in result
    
    def test_gmail_separator(self):
        """测试Gmail分隔符"""
        email_body = """
最新的生产计划：
产品名称,数量,交期
产品A,100,2024-01-15
产品B,200,2024-01-20

On Mon, Jan 1, 2024 at 10:00 AM <EMAIL> wrote:
> 之前的邮件内容
> 更多历史内容
"""
        result = _extract_latest_reply(email_body)
        assert "最新的生产计划" in result
        assert "On Mon, Jan 1" not in result
        assert "之前的邮件内容" not in result
    
    def test_chinese_separator(self):
        """测试中文分隔符"""
        email_body = """
更新的计划如下：

产品名称    数量    交期
产品A      100     2024-01-15
产品B      200     2024-01-20

在 2024年1月1日 上午10:00，<EMAIL> 写道:
> 原来的计划是...
"""
        result = _extract_latest_reply(email_body)
        assert "更新的计划如下" in result
        assert "写道:" not in result
        assert "原来的计划" not in result
    
    def test_quote_line_separator(self):
        """测试引用行分隔符"""
        email_body = """
新的数据：
产品A  100
产品B  200

> 这是引用的内容
> 更多引用内容
"""
        result = _extract_latest_reply(email_body)
        assert "新的数据" in result
        assert "这是引用的内容" not in result
    
    def test_no_separator_fallback(self):
        """测试无分隔符时的回退机制"""
        email_body = """
简单的邮件内容
产品A  100
产品B  200
"""
        result = _extract_latest_reply(email_body)
        assert result == email_body.strip()


class TestEmailContentCleaning:
    """测试邮件内容清理功能"""
    
    def test_remove_headers(self):
        """测试移除邮件头部信息"""
        content = """
From: <EMAIL>
To: <EMAIL>
Subject: 生产计划

实际内容开始
产品A  100
"""
        result = _clean_email_content(content)
        assert "From:" not in result
        assert "To:" not in result
        assert "Subject:" not in result
        assert "实际内容开始" in result
    
    def test_remove_signature(self):
        """测试移除邮件签名"""
        content = """
生产计划数据：
产品A  100
产品B  200

--
Best regards,
John Smith
Manager
"""
        result = _clean_email_content(content)
        assert "生产计划数据" in result
        assert "Best regards" not in result
        assert "John Smith" not in result
    
    def test_remove_chinese_signature(self):
        """测试移除中文签名"""
        content = """
计划更新：
产品A  100

此致
敬礼！
张三
"""
        result = _clean_email_content(content)
        assert "计划更新" in result
        assert "此致" not in result
        assert "张三" not in result


class TestTableExtraction:
    """测试表格提取功能"""
    
    def test_html_table_extraction(self):
        """测试HTML表格提取"""
        html_text = """
<table>
<tr><th>产品名称</th><th>数量</th><th>交期</th></tr>
<tr><td>产品A</td><td>100</td><td>2024-01-15</td></tr>
<tr><td>产品B</td><td>200</td><td>2024-01-20</td></tr>
</table>
"""
        result = _extract_html_tables_from_text(html_text)
        assert len(result) == 1
        assert result[0]["columns"] == ["产品名称", "数量", "交期"]
        assert len(result[0]["data"]) == 2
        assert result[0]["data"][0]["产品名称"] == "产品A"
    
    def test_text_table_extraction(self):
        """测试文本表格提取"""
        text = """
产品名称	数量	交期
产品A	100	2024-01-15
产品B	200	2024-01-20
"""
        result = _extract_text_tables_from_text(text)
        assert len(result) == 1
        assert result[0]["columns"] == ["产品名称", "数量", "交期"]
        assert len(result[0]["data"]) == 2
    
    def test_csv_table_extraction(self):
        """测试CSV表格提取"""
        csv_text = """
产品名称,数量,交期
产品A,100,2024-01-15
产品B,200,2024-01-20
"""
        result = _extract_csv_tables_from_text(csv_text)
        assert len(result) == 1
        assert result[0]["columns"] == ["产品名称", "数量", "交期"]
        assert len(result[0]["data"]) == 2


class TestEmailTableExtraction:
    """测试完整邮件表格提取功能"""
    
    def create_mock_email_file(self, content: str):
        """创建模拟邮件文件"""
        mock_file = Mock()
        mock_file.read.return_value = content.encode('utf-8')
        mock_file.type = "message/rfc822"
        return mock_file
    
    def test_latest_reply_table_extraction(self):
        """测试从最新回复中提取表格"""
        email_content = """From: <EMAIL>
To: <EMAIL>
Subject: 生产计划更新

最新计划：

产品名称	数量	交期
产品A	100	2024-01-15
产品B	200	2024-01-20

-----Original Message-----
From: <EMAIL>
原始计划：
产品C	300	2024-01-25
"""
        
        mock_file = self.create_mock_email_file(email_content)
        result = _extract_email_tables(mock_file)
        
        assert result["success"] == True
        assert result["latest_reply_extracted"] == True
        assert len(result["tables"]) >= 1
        
        # 检查是否提取了最新回复中的表格
        table_data = result["tables"][0]["data"]
        products = [row.get("产品名称", "") for row in table_data]
        assert "产品A" in products
        assert "产品B" in products
        # 不应该包含原始邮件中的产品C
        assert "产品C" not in products
    
    def test_fallback_to_full_email(self):
        """测试回退到完整邮件提取"""
        email_content = """From: <EMAIL>
To: <EMAIL>
Subject: 生产计划

没有表格的最新回复内容。

-----Original Message-----
原始邮件中的表格：
产品名称	数量	交期
产品A	100	2024-01-15
"""
        
        mock_file = self.create_mock_email_file(email_content)
        result = _extract_email_tables(mock_file)
        
        assert result["success"] == True
        # 应该从完整邮件中提取到表格
        assert len(result["tables"]) >= 1
    
    def test_no_table_fallback(self):
        """测试无表格时的回退机制"""
        email_content = """From: <EMAIL>
To: <EMAIL>
Subject: 简单邮件

这是一个没有表格的简单邮件。
"""
        
        mock_file = self.create_mock_email_file(email_content)
        result = _extract_email_tables(mock_file)
        
        assert result["success"] == True
        assert len(result["tables"]) == 1
        assert result["tables"][0]["table_name"] == "邮件信息"
        # 应该包含邮件基本信息
        email_data = result["tables"][0]["data"][0]
        assert "发件人" in email_data
        assert "主题" in email_data


class TestDatabaseConfig:
    """测试数据库配置"""
    
    def test_mysql_connection_string(self):
        """测试MySQL连接字符串生成"""
        from backend.app.core.config import Settings
        
        # 模拟配置
        values = {
            'DB_USER': 'smart_aps',
            'DB_PASSWORD': 'password',
            'DB_HOST': 'localhost',
            'DB_PORT': 3306,
            'DB_NAME': 'smart_aps'
        }
        
        settings = Settings()
        result = settings.assemble_db_connection(None, values)
        
        assert "mysql+aiomysql" in result
        assert "localhost:3306" in result
        assert "smart_aps" in result
        assert "charset=utf8mb4" in result


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
