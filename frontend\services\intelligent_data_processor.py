"""
智能数据验证与清洗模块
提供高级的数据质量检查、异常检测和智能清洗功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import logging
from datetime import datetime, timedelta
import re
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

@dataclass
class DataQualityIssue:
    """数据质量问题"""
    issue_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    affected_columns: List[str]
    affected_rows: List[int]
    suggestion: str
    auto_fixable: bool

@dataclass
class DataCleaningResult:
    """数据清洗结果"""
    original_shape: Tuple[int, int]
    cleaned_shape: Tuple[int, int]
    issues_found: List[DataQualityIssue]
    issues_fixed: List[str]
    quality_score: float
    cleaning_summary: Dict[str, Any]

class IntelligentDataProcessor:
    """智能数据处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data_patterns = self._initialize_patterns()
        self.cleaning_rules = self._initialize_cleaning_rules()
        
    def _initialize_patterns(self) -> Dict:
        """初始化数据模式识别"""
        return {
            'date_patterns': [
                r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
                r'\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY
                r'\d{4}/\d{2}/\d{2}',  # YYYY/MM/DD
                r'\d{2}-\d{2}-\d{4}',  # MM-DD-YYYY
            ],
            'numeric_patterns': [
                r'^-?\d+\.?\d*$',      # 数字
                r'^-?\d{1,3}(,\d{3})*\.?\d*$',  # 带千分位的数字
            ],
            'id_patterns': [
                r'^[A-Z]\d+$',         # 字母+数字ID
                r'^\d{6,}$',           # 纯数字ID
                r'^[A-Z]{2,}\d+$',     # 多字母+数字
            ]
        }
    
    def _initialize_cleaning_rules(self) -> Dict:
        """初始化清洗规则"""
        return {
            'remove_duplicates': True,
            'handle_missing_values': True,
            'detect_outliers': True,
            'standardize_formats': True,
            'validate_data_types': True,
            'check_consistency': True
        }
    
    def process_data(self, data: pd.DataFrame, data_type: str = 'general') -> Tuple[pd.DataFrame, DataCleaningResult]:
        """智能数据处理主入口"""
        self.logger.info(f"开始智能数据处理，数据形状: {data.shape}")
        
        original_shape = data.shape
        issues_found = []
        issues_fixed = []
        
        # 1. 数据质量检查
        quality_issues = self._comprehensive_quality_check(data)
        issues_found.extend(quality_issues)
        
        # 2. 智能数据清洗
        cleaned_data = data.copy()
        
        # 2.1 处理重复数据
        if self.cleaning_rules['remove_duplicates']:
            cleaned_data, duplicate_info = self._handle_duplicates(cleaned_data)
            if duplicate_info['removed_count'] > 0:
                issues_fixed.append(f"移除了 {duplicate_info['removed_count']} 行重复数据")
        
        # 2.2 处理缺失值
        if self.cleaning_rules['handle_missing_values']:
            cleaned_data, missing_info = self._handle_missing_values(cleaned_data, data_type)
            if missing_info['filled_count'] > 0:
                issues_fixed.append(f"填充了 {missing_info['filled_count']} 个缺失值")
        
        # 2.3 异常值检测和处理
        if self.cleaning_rules['detect_outliers']:
            cleaned_data, outlier_info = self._handle_outliers(cleaned_data)
            if outlier_info['handled_count'] > 0:
                issues_fixed.append(f"处理了 {outlier_info['handled_count']} 个异常值")
        
        # 2.4 数据格式标准化
        if self.cleaning_rules['standardize_formats']:
            cleaned_data, format_info = self._standardize_formats(cleaned_data)
            if format_info['standardized_columns']:
                issues_fixed.append(f"标准化了 {len(format_info['standardized_columns'])} 列的格式")
        
        # 2.5 数据类型验证和转换
        if self.cleaning_rules['validate_data_types']:
            cleaned_data, type_info = self._validate_and_convert_types(cleaned_data)
            if type_info['converted_columns']:
                issues_fixed.append(f"转换了 {len(type_info['converted_columns'])} 列的数据类型")
        
        # 3. 计算最终质量分数
        final_quality_score = self._calculate_quality_score(cleaned_data, issues_found)
        
        # 4. 生成清洗摘要
        cleaning_summary = {
            'processing_time': datetime.now().isoformat(),
            'data_type': data_type,
            'rows_removed': original_shape[0] - cleaned_data.shape[0],
            'columns_processed': cleaned_data.shape[1],
            'quality_improvement': final_quality_score - self._calculate_quality_score(data, quality_issues)
        }
        
        result = DataCleaningResult(
            original_shape=original_shape,
            cleaned_shape=cleaned_data.shape,
            issues_found=issues_found,
            issues_fixed=issues_fixed,
            quality_score=final_quality_score,
            cleaning_summary=cleaning_summary
        )
        
        self.logger.info(f"数据处理完成，质量分数: {final_quality_score:.2f}")
        return cleaned_data, result
    
    def _comprehensive_quality_check(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """综合数据质量检查"""
        issues = []
        
        # 检查缺失值
        missing_issues = self._check_missing_values(data)
        issues.extend(missing_issues)
        
        # 检查重复数据
        duplicate_issues = self._check_duplicates(data)
        issues.extend(duplicate_issues)
        
        # 检查数据类型一致性
        type_issues = self._check_data_types(data)
        issues.extend(type_issues)
        
        # 检查异常值
        outlier_issues = self._check_outliers(data)
        issues.extend(outlier_issues)
        
        # 检查数据格式
        format_issues = self._check_data_formats(data)
        issues.extend(format_issues)
        
        # 检查业务逻辑
        logic_issues = self._check_business_logic(data)
        issues.extend(logic_issues)
        
        return issues
    
    def _check_missing_values(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """检查缺失值"""
        issues = []
        
        for column in data.columns:
            missing_count = data[column].isnull().sum()
            missing_rate = missing_count / len(data)
            
            if missing_rate > 0.5:
                severity = 'critical'
            elif missing_rate > 0.2:
                severity = 'high'
            elif missing_rate > 0.05:
                severity = 'medium'
            elif missing_rate > 0:
                severity = 'low'
            else:
                continue
            
            missing_rows = data[data[column].isnull()].index.tolist()
            
            issues.append(DataQualityIssue(
                issue_type='missing_values',
                severity=severity,
                description=f"列 '{column}' 有 {missing_count} 个缺失值 ({missing_rate:.1%})",
                affected_columns=[column],
                affected_rows=missing_rows,
                suggestion=self._get_missing_value_suggestion(column, missing_rate),
                auto_fixable=True
            ))
        
        return issues
    
    def _check_duplicates(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """检查重复数据"""
        issues = []
        
        duplicate_rows = data.duplicated()
        duplicate_count = duplicate_rows.sum()
        
        if duplicate_count > 0:
            duplicate_rate = duplicate_count / len(data)
            
            if duplicate_rate > 0.1:
                severity = 'high'
            elif duplicate_rate > 0.05:
                severity = 'medium'
            else:
                severity = 'low'
            
            duplicate_indices = data[duplicate_rows].index.tolist()
            
            issues.append(DataQualityIssue(
                issue_type='duplicates',
                severity=severity,
                description=f"发现 {duplicate_count} 行重复数据 ({duplicate_rate:.1%})",
                affected_columns=list(data.columns),
                affected_rows=duplicate_indices,
                suggestion="建议删除重复行，保留第一次出现的记录",
                auto_fixable=True
            ))
        
        return issues
    
    def _check_outliers(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """检查异常值"""
        issues = []
        
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for column in numeric_columns:
            if data[column].isnull().all():
                continue
                
            # 使用IQR方法检测异常值
            Q1 = data[column].quantile(0.25)
            Q3 = data[column].quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = data[(data[column] < lower_bound) | (data[column] > upper_bound)]
            outlier_count = len(outliers)
            
            if outlier_count > 0:
                outlier_rate = outlier_count / len(data)
                
                if outlier_rate > 0.1:
                    severity = 'high'
                elif outlier_rate > 0.05:
                    severity = 'medium'
                else:
                    severity = 'low'
                
                issues.append(DataQualityIssue(
                    issue_type='outliers',
                    severity=severity,
                    description=f"列 '{column}' 有 {outlier_count} 个异常值 ({outlier_rate:.1%})",
                    affected_columns=[column],
                    affected_rows=outliers.index.tolist(),
                    suggestion=f"建议检查范围 [{lower_bound:.2f}, {upper_bound:.2f}] 之外的值",
                    auto_fixable=True
                ))
        
        return issues
    
    def _handle_duplicates(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
        """处理重复数据"""
        original_count = len(data)
        cleaned_data = data.drop_duplicates()
        removed_count = original_count - len(cleaned_data)
        
        return cleaned_data, {'removed_count': removed_count}
    
    def _handle_missing_values(self, data: pd.DataFrame, data_type: str) -> Tuple[pd.DataFrame, Dict]:
        """智能处理缺失值"""
        cleaned_data = data.copy()
        filled_count = 0
        
        for column in data.columns:
            missing_mask = cleaned_data[column].isnull()
            missing_count = missing_mask.sum()
            
            if missing_count == 0:
                continue
            
            # 根据数据类型选择填充策略
            if cleaned_data[column].dtype in ['object', 'category']:
                # 分类变量：使用众数
                mode_value = cleaned_data[column].mode()
                if len(mode_value) > 0:
                    cleaned_data[column].fillna(mode_value[0], inplace=True)
                    filled_count += missing_count
            else:
                # 数值变量：根据分布选择策略
                if self._is_normal_distribution(cleaned_data[column].dropna()):
                    # 正态分布：使用均值
                    mean_value = cleaned_data[column].mean()
                    cleaned_data[column].fillna(mean_value, inplace=True)
                else:
                    # 非正态分布：使用中位数
                    median_value = cleaned_data[column].median()
                    cleaned_data[column].fillna(median_value, inplace=True)
                filled_count += missing_count
        
        return cleaned_data, {'filled_count': filled_count}
    
    def _handle_outliers(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
        """处理异常值"""
        cleaned_data = data.copy()
        handled_count = 0
        
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for column in numeric_columns:
            if cleaned_data[column].isnull().all():
                continue
            
            # 使用IQR方法
            Q1 = cleaned_data[column].quantile(0.25)
            Q3 = cleaned_data[column].quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 将异常值限制在合理范围内
            outlier_mask = (cleaned_data[column] < lower_bound) | (cleaned_data[column] > upper_bound)
            outlier_count = outlier_mask.sum()
            
            if outlier_count > 0:
                cleaned_data[column] = cleaned_data[column].clip(lower=lower_bound, upper=upper_bound)
                handled_count += outlier_count
        
        return cleaned_data, {'handled_count': handled_count}
    
    def _standardize_formats(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
        """标准化数据格式"""
        cleaned_data = data.copy()
        standardized_columns = []
        
        for column in data.columns:
            if cleaned_data[column].dtype == 'object':
                # 尝试标准化日期格式
                if self._is_date_column(cleaned_data[column]):
                    try:
                        cleaned_data[column] = pd.to_datetime(cleaned_data[column], errors='coerce')
                        standardized_columns.append(column)
                    except:
                        pass
                
                # 标准化文本格式
                elif self._is_text_column(cleaned_data[column]):
                    # 去除多余空格，统一大小写
                    cleaned_data[column] = cleaned_data[column].astype(str).str.strip().str.title()
                    standardized_columns.append(column)
        
        return cleaned_data, {'standardized_columns': standardized_columns}
    
    def _validate_and_convert_types(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
        """验证和转换数据类型"""
        cleaned_data = data.copy()
        converted_columns = []
        
        for column in data.columns:
            if cleaned_data[column].dtype == 'object':
                # 尝试转换为数值类型
                if self._is_numeric_column(cleaned_data[column]):
                    try:
                        # 清理数值格式
                        numeric_series = cleaned_data[column].astype(str).str.replace(',', '').str.replace('$', '')
                        cleaned_data[column] = pd.to_numeric(numeric_series, errors='coerce')
                        converted_columns.append(column)
                    except:
                        pass
        
        return cleaned_data, {'converted_columns': converted_columns}
    
    def _calculate_quality_score(self, data: pd.DataFrame, issues: List[DataQualityIssue]) -> float:
        """计算数据质量分数"""
        if len(data) == 0:
            return 0.0
        
        # 基础分数
        base_score = 100.0
        
        # 根据问题严重程度扣分
        for issue in issues:
            if issue.severity == 'critical':
                base_score -= 20
            elif issue.severity == 'high':
                base_score -= 10
            elif issue.severity == 'medium':
                base_score -= 5
            elif issue.severity == 'low':
                base_score -= 2
        
        # 确保分数在0-100之间
        return max(0.0, min(100.0, base_score))
    
    # 辅助方法
    def _get_missing_value_suggestion(self, column: str, missing_rate: float) -> str:
        """获取缺失值处理建议"""
        if missing_rate > 0.5:
            return f"列 '{column}' 缺失率过高，建议考虑删除该列或收集更多数据"
        elif missing_rate > 0.2:
            return f"列 '{column}' 缺失率较高，建议使用插值或预测方法填充"
        else:
            return f"列 '{column}' 可以使用均值/中位数/众数填充"
    
    def _is_normal_distribution(self, series: pd.Series) -> bool:
        """检查是否为正态分布"""
        if len(series) < 8:
            return False
        try:
            _, p_value = stats.normaltest(series)
            return p_value > 0.05
        except:
            return False
    
    def _is_date_column(self, series: pd.Series) -> bool:
        """检查是否为日期列"""
        sample = series.dropna().astype(str).head(10)
        date_count = 0
        
        for value in sample:
            for pattern in self.data_patterns['date_patterns']:
                if re.match(pattern, value):
                    date_count += 1
                    break
        
        return date_count / len(sample) > 0.5 if len(sample) > 0 else False
    
    def _is_numeric_column(self, series: pd.Series) -> bool:
        """检查是否为数值列"""
        sample = series.dropna().astype(str).head(10)
        numeric_count = 0
        
        for value in sample:
            for pattern in self.data_patterns['numeric_patterns']:
                if re.match(pattern, value.replace(' ', '')):
                    numeric_count += 1
                    break
        
        return numeric_count / len(sample) > 0.7 if len(sample) > 0 else False
    
    def _is_text_column(self, series: pd.Series) -> bool:
        """检查是否为文本列"""
        sample = series.dropna().astype(str).head(10)
        return any(len(str(value)) > 10 and any(c.isalpha() for c in str(value)) for value in sample)
    
    def _check_data_types(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """检查数据类型一致性"""
        # 简化实现，实际可以更复杂
        return []
    
    def _check_data_formats(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """检查数据格式"""
        # 简化实现，实际可以更复杂
        return []
    
    def _check_business_logic(self, data: pd.DataFrame) -> List[DataQualityIssue]:
        """检查业务逻辑"""
        # 简化实现，实际可以更复杂
        return []
