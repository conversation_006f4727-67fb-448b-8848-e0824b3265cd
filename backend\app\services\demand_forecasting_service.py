"""
AI需求预测服务
基于机器学习的智能需求预测系统
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum
import pickle
import json

from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.model_selection import train_test_split, cross_val_score
import joblib

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from ..core.config import settings
from ..models.production import ProductionOrder
from ..models.sales import SalesOrder, SalesHistory

logger = logging.getLogger(__name__)


class ForecastModel(Enum):
    """预测模型类型"""
    LINEAR_REGRESSION = "linear_regression"
    RANDOM_FOREST = "random_forest"
    GRADIENT_BOOSTING = "gradient_boosting"
    ENSEMBLE = "ensemble"


@dataclass
class ForecastConfig:
    """预测配置"""
    forecast_horizon: int = 30  # 预测天数
    training_window: int = 365  # 训练数据窗口（天）
    min_training_samples: int = 50  # 最小训练样本数
    model_update_interval: int = 7  # 模型更新间隔（天）
    confidence_level: float = 0.95  # 置信水平
    seasonal_periods: List[int] = None  # 季节性周期
    
    def __post_init__(self):
        if self.seasonal_periods is None:
            self.seasonal_periods = [7, 30, 365]  # 周、月、年季节性


@dataclass
class ForecastResult:
    """预测结果"""
    product_code: str
    forecast_date: datetime
    predicted_demand: float
    confidence_interval_lower: float
    confidence_interval_upper: float
    model_accuracy: float
    trend: str  # UP, DOWN, STABLE
    seasonality_factor: float


class DemandForecastingService:
    """需求预测服务"""
    
    def __init__(self, db_session: AsyncSession = None):
        self.db = db_session
        self.config = ForecastConfig()
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.model_metrics = {}
        
        # 加载配置
        self._load_config()
        
        # 初始化模型
        self._initialize_models()
    
    def _load_config(self):
        """加载预测配置"""
        self.config.forecast_horizon = getattr(settings, 'FORECAST_HORIZON', 30)
        self.config.training_window = getattr(settings, 'FORECAST_TRAINING_WINDOW', 365)
        self.config.min_training_samples = getattr(settings, 'FORECAST_MIN_SAMPLES', 50)
    
    def _initialize_models(self):
        """初始化预测模型"""
        self.models = {
            ForecastModel.LINEAR_REGRESSION: LinearRegression(),
            ForecastModel.RANDOM_FOREST: RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            ),
            ForecastModel.GRADIENT_BOOSTING: GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
        }
        
        self.scalers = {model_type: StandardScaler() for model_type in self.models.keys()}
    
    async def train_demand_models(self, product_codes: List[str] = None) -> Dict[str, Any]:
        """训练需求预测模型"""
        try:
            # 获取历史销售数据
            sales_data = await self._get_historical_sales_data(product_codes)
            
            if sales_data.empty:
                logger.warning("没有足够的历史销售数据进行训练")
                return {"status": "failed", "reason": "insufficient_data"}
            
            training_results = {}
            
            # 按产品分别训练模型
            for product_code in sales_data['product_code'].unique():
                product_data = sales_data[sales_data['product_code'] == product_code]
                
                if len(product_data) < self.config.min_training_samples:
                    logger.warning(f"产品 {product_code} 的训练数据不足")
                    continue
                
                # 特征工程
                features, targets = self._prepare_features(product_data)
                
                if len(features) == 0:
                    continue
                
                # 训练模型
                model_results = await self._train_product_models(product_code, features, targets)
                training_results[product_code] = model_results
            
            # 保存模型
            await self._save_models()
            
            return {
                "status": "success",
                "trained_products": len(training_results),
                "results": training_results,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"训练需求预测模型失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def _get_historical_sales_data(self, product_codes: List[str] = None) -> pd.DataFrame:
        """获取历史销售数据"""
        try:
            # 计算数据范围
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=self.config.training_window)
            
            # 构建查询
            query = select(
                SalesHistory.product_code,
                SalesHistory.sale_date,
                SalesHistory.quantity,
                SalesHistory.unit_price,
                SalesHistory.customer_id,
                SalesHistory.sales_channel
            ).where(
                SalesHistory.sale_date >= start_date,
                SalesHistory.sale_date <= end_date
            )
            
            if product_codes:
                query = query.where(SalesHistory.product_code.in_(product_codes))
            
            result = await self.db.execute(query)
            rows = result.fetchall()
            
            # 转换为DataFrame
            df = pd.DataFrame([
                {
                    'product_code': row.product_code,
                    'sale_date': row.sale_date,
                    'quantity': row.quantity,
                    'unit_price': row.unit_price,
                    'customer_id': row.customer_id,
                    'sales_channel': row.sales_channel
                }
                for row in rows
            ])
            
            return df
            
        except Exception as e:
            logger.error(f"获取历史销售数据失败: {e}")
            return pd.DataFrame()
    
    def _prepare_features(self, product_data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """准备特征数据"""
        try:
            # 按日期聚合数据
            daily_data = product_data.groupby('sale_date').agg({
                'quantity': 'sum',
                'unit_price': 'mean'
            }).reset_index()
            
            # 排序并填充缺失日期
            daily_data = daily_data.sort_values('sale_date')
            date_range = pd.date_range(
                start=daily_data['sale_date'].min(),
                end=daily_data['sale_date'].max(),
                freq='D'
            )
            
            full_data = pd.DataFrame({'sale_date': date_range})
            daily_data = full_data.merge(daily_data, on='sale_date', how='left')
            daily_data['quantity'] = daily_data['quantity'].fillna(0)
            daily_data['unit_price'] = daily_data['unit_price'].fillna(method='ffill')
            
            # 创建时间特征
            daily_data['day_of_week'] = daily_data['sale_date'].dt.dayofweek
            daily_data['day_of_month'] = daily_data['sale_date'].dt.day
            daily_data['month'] = daily_data['sale_date'].dt.month
            daily_data['quarter'] = daily_data['sale_date'].dt.quarter
            daily_data['is_weekend'] = daily_data['day_of_week'].isin([5, 6]).astype(int)
            
            # 创建滞后特征
            for lag in [1, 7, 30]:
                daily_data[f'quantity_lag_{lag}'] = daily_data['quantity'].shift(lag)
            
            # 创建移动平均特征
            for window in [7, 14, 30]:
                daily_data[f'quantity_ma_{window}'] = daily_data['quantity'].rolling(window=window).mean()
            
            # 创建趋势特征
            daily_data['quantity_trend'] = daily_data['quantity'].rolling(window=7).apply(
                lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 7 else 0
            )
            
            # 删除包含NaN的行
            daily_data = daily_data.dropna()
            
            if len(daily_data) < self.config.min_training_samples:
                return np.array([]), np.array([])
            
            # 准备特征矩阵
            feature_columns = [
                'day_of_week', 'day_of_month', 'month', 'quarter', 'is_weekend',
                'unit_price', 'quantity_lag_1', 'quantity_lag_7', 'quantity_lag_30',
                'quantity_ma_7', 'quantity_ma_14', 'quantity_ma_30', 'quantity_trend'
            ]
            
            features = daily_data[feature_columns].values
            targets = daily_data['quantity'].values
            
            return features, targets
            
        except Exception as e:
            logger.error(f"准备特征数据失败: {e}")
            return np.array([]), np.array([])
    
    async def _train_product_models(self, product_code: str, features: np.ndarray, targets: np.ndarray) -> Dict[str, Any]:
        """为单个产品训练模型"""
        try:
            # 分割训练和测试数据
            X_train, X_test, y_train, y_test = train_test_split(
                features, targets, test_size=0.2, random_state=42, shuffle=False
            )
            
            model_results = {}
            
            for model_type, model in self.models.items():
                try:
                    # 标准化特征
                    scaler = self.scalers[model_type]
                    X_train_scaled = scaler.fit_transform(X_train)
                    X_test_scaled = scaler.transform(X_test)
                    
                    # 训练模型
                    model.fit(X_train_scaled, y_train)
                    
                    # 预测和评估
                    y_pred = model.predict(X_test_scaled)
                    
                    # 计算评估指标
                    mae = mean_absolute_error(y_test, y_pred)
                    mse = mean_squared_error(y_test, y_pred)
                    rmse = np.sqrt(mse)
                    r2 = r2_score(y_test, y_pred)
                    
                    # 交叉验证
                    cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring='neg_mean_absolute_error')
                    cv_mae = -cv_scores.mean()
                    
                    model_results[model_type.value] = {
                        'mae': mae,
                        'mse': mse,
                        'rmse': rmse,
                        'r2': r2,
                        'cv_mae': cv_mae,
                        'accuracy': max(0, 1 - mae / (y_test.mean() + 1e-8))
                    }
                    
                    # 保存模型和缩放器
                    self.models[f"{product_code}_{model_type.value}"] = model
                    self.scalers[f"{product_code}_{model_type.value}"] = scaler
                    
                except Exception as e:
                    logger.error(f"训练模型 {model_type.value} 失败: {e}")
                    continue
            
            return model_results
            
        except Exception as e:
            logger.error(f"训练产品模型失败: {e}")
            return {}
    
    async def predict_demand(self, product_code: str, forecast_days: int = None) -> List[ForecastResult]:
        """预测产品需求"""
        try:
            if forecast_days is None:
                forecast_days = self.config.forecast_horizon
            
            # 获取最新的历史数据
            recent_data = await self._get_recent_data(product_code)
            
            if recent_data.empty:
                logger.warning(f"产品 {product_code} 没有足够的历史数据")
                return []
            
            # 准备预测特征
            forecast_features = self._prepare_forecast_features(recent_data, forecast_days)
            
            # 使用最佳模型进行预测
            best_model_key = self._get_best_model(product_code)
            
            if not best_model_key:
                logger.warning(f"产品 {product_code} 没有可用的预测模型")
                return []
            
            model = self.models.get(best_model_key)
            scaler = self.scalers.get(best_model_key)
            
            if not model or not scaler:
                logger.warning(f"模型或缩放器不存在: {best_model_key}")
                return []
            
            # 进行预测
            predictions = []
            base_date = datetime.utcnow().date()
            
            for i in range(forecast_days):
                forecast_date = base_date + timedelta(days=i+1)
                
                # 获取该日期的特征
                day_features = forecast_features[i:i+1]
                day_features_scaled = scaler.transform(day_features)
                
                # 预测
                predicted_demand = model.predict(day_features_scaled)[0]
                predicted_demand = max(0, predicted_demand)  # 确保非负
                
                # 计算置信区间（简化版本）
                model_accuracy = self.model_metrics.get(best_model_key, {}).get('accuracy', 0.8)
                uncertainty = predicted_demand * (1 - model_accuracy)
                
                confidence_lower = max(0, predicted_demand - uncertainty)
                confidence_upper = predicted_demand + uncertainty
                
                # 判断趋势
                trend = self._determine_trend(recent_data, predicted_demand)
                
                # 计算季节性因子
                seasonality_factor = self._calculate_seasonality_factor(forecast_date, recent_data)
                
                predictions.append(ForecastResult(
                    product_code=product_code,
                    forecast_date=datetime.combine(forecast_date, datetime.min.time()),
                    predicted_demand=round(predicted_demand, 2),
                    confidence_interval_lower=round(confidence_lower, 2),
                    confidence_interval_upper=round(confidence_upper, 2),
                    model_accuracy=round(model_accuracy, 3),
                    trend=trend,
                    seasonality_factor=round(seasonality_factor, 3)
                ))
            
            return predictions
            
        except Exception as e:
            logger.error(f"预测需求失败: {e}")
            return []
    
    async def _get_recent_data(self, product_code: str, days: int = 90) -> pd.DataFrame:
        """获取最近的销售数据"""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            query = select(
                SalesHistory.sale_date,
                SalesHistory.quantity,
                SalesHistory.unit_price
            ).where(
                SalesHistory.product_code == product_code,
                SalesHistory.sale_date >= start_date,
                SalesHistory.sale_date <= end_date
            ).order_by(SalesHistory.sale_date)
            
            result = await self.db.execute(query)
            rows = result.fetchall()
            
            df = pd.DataFrame([
                {
                    'sale_date': row.sale_date,
                    'quantity': row.quantity,
                    'unit_price': row.unit_price
                }
                for row in rows
            ])
            
            return df
            
        except Exception as e:
            logger.error(f"获取最近数据失败: {e}")
            return pd.DataFrame()
    
    def _prepare_forecast_features(self, recent_data: pd.DataFrame, forecast_days: int) -> np.ndarray:
        """准备预测特征"""
        try:
            # 聚合日数据
            daily_data = recent_data.groupby('sale_date').agg({
                'quantity': 'sum',
                'unit_price': 'mean'
            }).reset_index()
            
            # 创建预测日期范围
            last_date = daily_data['sale_date'].max()
            forecast_dates = pd.date_range(
                start=last_date + timedelta(days=1),
                periods=forecast_days,
                freq='D'
            )
            
            forecast_features = []
            
            for forecast_date in forecast_dates:
                # 时间特征
                day_of_week = forecast_date.dayofweek
                day_of_month = forecast_date.day
                month = forecast_date.month
                quarter = forecast_date.quarter
                is_weekend = int(day_of_week in [5, 6])
                
                # 使用最近的价格
                unit_price = daily_data['unit_price'].iloc[-1] if not daily_data.empty else 0
                
                # 滞后特征（使用最近的数据）
                recent_quantities = daily_data['quantity'].tail(30).values
                quantity_lag_1 = recent_quantities[-1] if len(recent_quantities) >= 1 else 0
                quantity_lag_7 = recent_quantities[-7] if len(recent_quantities) >= 7 else 0
                quantity_lag_30 = recent_quantities[-30] if len(recent_quantities) >= 30 else 0
                
                # 移动平均特征
                quantity_ma_7 = recent_quantities[-7:].mean() if len(recent_quantities) >= 7 else 0
                quantity_ma_14 = recent_quantities[-14:].mean() if len(recent_quantities) >= 14 else 0
                quantity_ma_30 = recent_quantities.mean() if len(recent_quantities) > 0 else 0
                
                # 趋势特征
                if len(recent_quantities) >= 7:
                    quantity_trend = np.polyfit(range(7), recent_quantities[-7:], 1)[0]
                else:
                    quantity_trend = 0
                
                features = [
                    day_of_week, day_of_month, month, quarter, is_weekend,
                    unit_price, quantity_lag_1, quantity_lag_7, quantity_lag_30,
                    quantity_ma_7, quantity_ma_14, quantity_ma_30, quantity_trend
                ]
                
                forecast_features.append(features)
            
            return np.array(forecast_features)
            
        except Exception as e:
            logger.error(f"准备预测特征失败: {e}")
            return np.array([])
    
    def _get_best_model(self, product_code: str) -> Optional[str]:
        """获取最佳模型"""
        best_model = None
        best_accuracy = 0
        
        for model_type in self.models.keys():
            model_key = f"{product_code}_{model_type.value}"
            if model_key in self.models:
                accuracy = self.model_metrics.get(model_key, {}).get('accuracy', 0)
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    best_model = model_key
        
        return best_model
    
    def _determine_trend(self, recent_data: pd.DataFrame, predicted_demand: float) -> str:
        """判断需求趋势"""
        if recent_data.empty:
            return "STABLE"
        
        recent_avg = recent_data['quantity'].tail(7).mean()
        
        if predicted_demand > recent_avg * 1.1:
            return "UP"
        elif predicted_demand < recent_avg * 0.9:
            return "DOWN"
        else:
            return "STABLE"
    
    def _calculate_seasonality_factor(self, forecast_date: datetime, recent_data: pd.DataFrame) -> float:
        """计算季节性因子"""
        if recent_data.empty:
            return 1.0
        
        # 简化的季节性计算
        month = forecast_date.month
        day_of_week = forecast_date.weekday()
        
        # 基于月份的季节性
        month_factor = 1.0
        if month in [11, 12, 1]:  # 冬季
            month_factor = 1.1
        elif month in [6, 7, 8]:  # 夏季
            month_factor = 0.9
        
        # 基于星期的季节性
        week_factor = 1.0
        if day_of_week in [5, 6]:  # 周末
            week_factor = 0.8
        
        return month_factor * week_factor
    
    async def _save_models(self):
        """保存模型到文件"""
        try:
            model_dir = "models/demand_forecasting"
            import os
            os.makedirs(model_dir, exist_ok=True)
            
            # 保存模型
            for key, model in self.models.items():
                if isinstance(key, str) and "_" in key:  # 产品特定模型
                    model_path = f"{model_dir}/{key}_model.pkl"
                    joblib.dump(model, model_path)
            
            # 保存缩放器
            for key, scaler in self.scalers.items():
                if isinstance(key, str) and "_" in key:  # 产品特定缩放器
                    scaler_path = f"{model_dir}/{key}_scaler.pkl"
                    joblib.dump(scaler, scaler_path)
            
            # 保存模型指标
            metrics_path = f"{model_dir}/model_metrics.json"
            with open(metrics_path, 'w') as f:
                json.dump(self.model_metrics, f, indent=2)
            
            logger.info("模型保存成功")
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
    
    async def get_forecast_summary(self, product_codes: List[str] = None) -> Dict[str, Any]:
        """获取预测摘要"""
        try:
            if not product_codes:
                # 获取所有有模型的产品
                product_codes = list(set([
                    key.split('_')[0] for key in self.models.keys() 
                    if isinstance(key, str) and "_" in key
                ]))
            
            summary = {
                'total_products': len(product_codes),
                'forecast_horizon': self.config.forecast_horizon,
                'products': {},
                'overall_trends': {'UP': 0, 'DOWN': 0, 'STABLE': 0},
                'generated_at': datetime.utcnow().isoformat()
            }
            
            for product_code in product_codes:
                forecasts = await self.predict_demand(product_code, 7)  # 7天预测
                
                if forecasts:
                    total_demand = sum(f.predicted_demand for f in forecasts)
                    avg_accuracy = sum(f.model_accuracy for f in forecasts) / len(forecasts)
                    trend = forecasts[0].trend
                    
                    summary['products'][product_code] = {
                        'weekly_forecast': round(total_demand, 2),
                        'average_accuracy': round(avg_accuracy, 3),
                        'trend': trend,
                        'confidence': 'HIGH' if avg_accuracy > 0.8 else 'MEDIUM' if avg_accuracy > 0.6 else 'LOW'
                    }
                    
                    summary['overall_trends'][trend] += 1
            
            return summary
            
        except Exception as e:
            logger.error(f"获取预测摘要失败: {e}")
            return {}


# 全局需求预测服务实例
demand_forecasting_service = DemandForecastingService()
