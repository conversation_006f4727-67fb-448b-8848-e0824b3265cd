# 📧 Smart Planning 邮件配置指南

## 📋 概述

Smart Planning系统提供完整的邮件功能，支持系统通知、报告发送、异常警报等邮件服务。本指南详细介绍邮件功能的配置和使用方法。

## 🔧 邮件功能特性

### ✅ **核心功能**
- **邮件发送**: 支持文本和HTML格式邮件
- **附件支持**: 支持多种文件格式附件
- **模板系统**: 预定义邮件模板
- **异步发送**: 高性能异步邮件发送
- **连接测试**: 邮件服务器连接测试
- **配置管理**: 可视化配置界面

### ✅ **通知类型**
- **异常警报**: 系统检测到异常时自动发送
- **报告就绪**: 报告生成完成后通知
- **系统警报**: 系统监控发现问题时警报
- **备份完成**: 系统备份完成后通知

## 🛠️ 配置方法

### 1. **环境变量配置**

在`.env`文件中配置邮件参数：

```bash
# 企业内部SMTP服务器配置
SMTP_HOST=mail.company.com
SMTP_PORT=25
SMTP_USER=<EMAIL>
SMTP_PASSWORD=smtp_password
SMTP_TLS=false
SMTP_SSL=false
EMAIL_FROM=Smart planning System <<EMAIL>>
EMAIL_ENABLED=true

# 邮件通知收件人配置
EMAIL_ADMIN_RECIPIENTS=<EMAIL>,<EMAIL>
EMAIL_ALERT_RECIPIENTS=<EMAIL>
EMAIL_REPORT_RECIPIENTS=<EMAIL>
```

### 2. **前端界面配置**

访问系统的"邮件配置"页面进行可视化配置：

1. **SMTP服务器设置**
   - 服务器地址
   - 端口号
   - 安全协议（TLS/SSL）

2. **认证信息**
   - 用户名/邮箱地址
   - 密码/应用专用密码

3. **发件人信息**
   - 发件人显示名称
   - 发件人邮箱地址

4. **功能开关**
   - 启用/禁用邮件功能

## 📧 企业邮件服务器配置

### 企业内部SMTP服务器配置
```
SMTP服务器: mail.company.com（联系IT管理员确认）
端口: 25（标准端口）或 587（如果启用TLS）
用户名: 企业邮箱地址（如：<EMAIL>）
密码: 企业邮箱密码
安全: 企业内部通常不需要TLS/SSL
```

### 收件人配置说明
- **管理员邮箱**: 接收系统管理、备份等通知
- **警报邮箱**: 接收生产、设备、系统等警报信息
- **报告邮箱**: 接收系统生成的各类报告

### 邮件模板类型
- **异常警报**: 系统检测到异常时自动发送
- **生产警报**: 生产线问题通知
- **设备警报**: 设备状态异常通知
- **系统警报**: 系统监控发现问题时警报
- **报告就绪**: 报告生成完成后通知
- **备份完成**: 系统备份完成后通知

## 🔌 API接口

### 发送邮件
```http
POST /api/v1/email/send
Content-Type: application/json

{
  "to_emails": ["<EMAIL>"],
  "subject": "邮件主题",
  "body": "邮件正文",
  "html_body": "<h1>HTML邮件内容</h1>",
  "cc_emails": ["<EMAIL>"],
  "bcc_emails": ["<EMAIL>"]
}
```

### 发送通知邮件
```http
POST /api/v1/email/notification
Content-Type: application/json

{
  "notification_type": "anomaly_alert",
  "recipients": ["<EMAIL>"],
  "data": {
    "anomaly_type": "设备故障",
    "severity": "高",
    "timestamp": "2024-01-01 10:00:00",
    "description": "设备L01出现异常"
  }
}
```

### 测试连接
```http
GET /api/v1/email/test
```

### 获取配置
```http
GET /api/v1/email/config
```

### 更新配置
```http
POST /api/v1/email/config
Content-Type: application/json

{
  "smtp_host": "smtp.gmail.com",
  "smtp_port": 587,
  "smtp_user": "<EMAIL>",
  "smtp_password": "password",
  "smtp_tls": true,
  "smtp_ssl": false,
  "email_from": "Smart planning <<EMAIL>>",
  "enabled": true
}
```

## 📝 邮件模板

### 异常警报模板
```html
<h2>🚨 异常警报</h2>
<p>检测到系统异常，请及时处理：</p>
<ul>
  <li><strong>异常类型</strong>: {anomaly_type}</li>
  <li><strong>严重程度</strong>: {severity}</li>
  <li><strong>发生时间</strong>: {timestamp}</li>
  <li><strong>影响范围</strong>: {affected_area}</li>
</ul>
<p><strong>详细描述</strong>: {description}</p>
<p><strong>建议措施</strong>: {recommended_action}</p>
```

### 报告就绪模板
```html
<h2>📊 报告已生成</h2>
<p>您请求的报告已经生成完成：</p>
<ul>
  <li><strong>报告名称</strong>: {report_name}</li>
  <li><strong>报告类型</strong>: {report_type}</li>
  <li><strong>生成时间</strong>: {generated_at}</li>
  <li><strong>数据范围</strong>: {data_range}</li>
</ul>
<p>请登录系统查看详细报告内容。</p>
```

## 🔒 安全配置

### 密码安全
- 使用应用专用密码而不是邮箱登录密码
- 定期更换密码
- 避免在代码中硬编码密码

### 连接安全
- 优先使用TLS加密连接
- 避免使用不安全的明文连接
- 验证SMTP服务器证书

### 权限控制
- 邮件配置需要管理员权限
- 邮件发送需要相应权限
- 敏感信息不在日志中记录

## 🚀 使用示例

### Python代码示例
```python
from app.services.email_service import email_service

# 发送简单邮件
success = email_service.send_email(
    to_emails=["<EMAIL>"],
    subject="测试邮件",
    body="这是一封测试邮件"
)

# 发送通知邮件
success = email_service.send_notification(
    notification_type="anomaly_alert",
    recipients=["<EMAIL>"],
    data={
        "anomaly_type": "设备故障",
        "severity": "高",
        "timestamp": "2024-01-01 10:00:00",
        "description": "设备异常"
    }
)

# 测试连接
result = email_service.test_connection()
print(result)
```

### 前端使用示例
```javascript
// 发送邮件
const response = await fetch('/api/v1/email/send', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    to_emails: ['<EMAIL>'],
    subject: '测试邮件',
    body: '邮件内容'
  })
});

// 测试连接
const testResult = await fetch('/api/v1/email/test');
const result = await testResult.json();
```

## 🔧 故障排除

### 常见问题

#### 1. 连接失败
- 检查SMTP服务器地址和端口
- 确认网络连接正常
- 验证防火墙设置

#### 2. 认证失败
- 检查用户名和密码
- 确认是否需要应用专用密码
- 验证邮箱安全设置

#### 3. 发送失败
- 检查收件人邮箱地址格式
- 确认邮件内容不包含敏感词
- 验证发件人邮箱设置

#### 4. 邮件被拒绝
- 检查SPF、DKIM、DMARC设置
- 确认发件人域名信誉
- 避免发送垃圾邮件

### 调试方法
1. 查看系统日志
2. 使用连接测试功能
3. 发送测试邮件验证
4. 检查邮件服务商限制

## 📊 监控和维护

### 邮件发送监控
- 发送成功率统计
- 失败原因分析
- 性能指标监控

### 定期维护
- 更新邮箱密码
- 检查配置有效性
- 清理邮件日志

## 🎯 最佳实践

1. **配置管理**
   - 使用环境变量管理敏感信息
   - 定期备份邮件配置
   - 测试配置变更

2. **性能优化**
   - 使用异步发送
   - 控制并发数量
   - 优化邮件内容大小

3. **安全防护**
   - 启用邮件加密
   - 限制发送频率
   - 监控异常活动

4. **用户体验**
   - 提供清晰的邮件模板
   - 及时发送重要通知
   - 避免垃圾邮件特征

**📧 Smart Planning邮件功能为系统提供了完整的通知和报告能力，确保重要信息及时传达给相关人员！**
