"""
高级数据配置页面
支持精细化数据筛选、自定义SQL查询和字段级别配置
"""

import streamlit as st
import pandas as pd
import json
import sys
import os

# 添加服务路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

try:
    from advanced_data_filter_service import advanced_data_filter_service
    from extensible_data_source_service import extensible_data_source_service
except ImportError as e:
    st.error(f"无法导入数据服务: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="高级数据配置",
    page_icon="🎛️",
    layout="wide"
)

st.title("🎛️ 高级数据配置")
st.markdown("### 精细化数据筛选、自定义SQL查询和字段级别配置")

# 主要标签页
tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
    "🔍 精细化数据筛选",
    "📝 自定义SQL查询",
    "🎯 字段级别配置",
    "🗄️ 自定义数据源",
    "🔧 字段扩展配置",
    "📊 配置预览测试"
])

with tab1:
    st.markdown("#### 🔍 精细化数据筛选配置")

    # 数据源选择
    col1, col2 = st.columns([1, 2])

    with col1:
        data_source = st.selectbox(
            "选择数据源",
            ["PCI数据", "设备数据", "上传文件", "用户输入", "自定义数据源"],
            help="选择要配置过滤条件的数据源"
        )

    with col2:
        st.info(f"当前配置数据源: {data_source}")

    if data_source == "PCI数据":
        st.markdown("##### 📦 PCI数据过滤配置")

        col1, col2, col3 = st.columns(3)

        with col1:
            # 容器类型过滤
            container_types = st.multiselect(
                "容器类型",
                ["箱子", "托盘", "散装", "桶装"],
                default=["箱子"],
                help="选择要包含的容器类型"
            )

            # 库龄过滤
            min_days_old = st.number_input("最小库龄(天)", min_value=0, value=0)
            max_days_old = st.number_input("最大库龄(天)", min_value=0, value=365)

        with col2:
            # 优先级过滤
            priority_threshold = st.slider(
                "优先级阈值",
                min_value=0, max_value=100, value=50,
                help="只包含优先级高于此值的物料"
            )

            # 数量范围
            quantity_min = st.number_input("最小数量", min_value=0, value=1)
            quantity_max = st.number_input("最大数量", min_value=1, value=1000)

        with col3:
            # 特定FS ID
            specific_fs_ids = st.text_area(
                "特定FS ID (每行一个)",
                placeholder="FS001\nFS002\nFS003",
                help="只包含指定的FS ID，留空表示包含所有"
            )

            # 其他选项
            exclude_empty = st.checkbox("排除空箱", value=True)

            # 返回字段选择
            available_fields = ["fs_id", "container_type", "material_code", "quantity",
                              "days_old", "consumption_priority", "location", "batch_number"]
            selected_fields = st.multiselect(
                "返回字段",
                available_fields,
                default=available_fields,
                help="选择要返回的字段，留空表示返回所有字段"
            )

        # 构建过滤配置
        pci_filter_config = {
            "container_types": container_types if container_types else None,
            "min_days_old": min_days_old if min_days_old > 0 else None,
            "max_days_old": max_days_old if max_days_old < 365 else None,
            "priority_threshold": priority_threshold if priority_threshold > 0 else None,
            "quantity_range": [quantity_min, quantity_max] if quantity_min < quantity_max else None,
            "specific_fs_ids": specific_fs_ids.strip().split('\n') if specific_fs_ids.strip() else None,
            "exclude_empty": exclude_empty,
            "fields": selected_fields if selected_fields != available_fields else None
        }

        # 保存配置按钮
        col1, col2, col3 = st.columns(3)

        with col1:
            filter_name = st.text_input("过滤器名称", value="PCI_箱子过滤器")

        with col2:
            if st.button("💾 保存过滤配置", type="primary"):
                success = advanced_data_filter_service.add_data_source_filter(
                    "pci", filter_name, pci_filter_config
                )
                if success:
                    st.success(f"✅ 过滤配置 '{filter_name}' 已保存")
                else:
                    st.error("❌ 保存过滤配置失败")

        with col3:
            if st.button("🔍 测试过滤效果"):
                st.session_state.test_pci_filter = pci_filter_config
                st.rerun()

    elif data_source == "设备数据":
        st.markdown("##### ⚙️ 设备数据过滤配置")

        col1, col2, col3 = st.columns(3)

        with col1:
            # 特定设备ID
            equipment_ids = st.multiselect(
                "设备ID",
                ["L01", "L02", "L03", "L04", "Tank01", "Tank02", "Tank03", "Tank04", "Tank05"],
                default=["L01"],
                help="选择特定设备，留空表示包含所有设备"
            )

            # 设备类型
            equipment_types = st.multiselect(
                "设备类型",
                ["生产线", "储罐", "包装线", "检测设备"],
                help="选择设备类型"
            )

        with col2:
            # 状态过滤
            status_filter = st.multiselect(
                "设备状态",
                ["运行中", "停机", "维护中", "故障", "空闲"],
                default=["运行中"],
                help="选择要包含的设备状态"
            )

            # 车间过滤
            workshop_filter = st.multiselect(
                "车间",
                ["A车间", "B车间", "C车间", "D车间"],
                help="选择车间"
            )

        with col3:
            # 产能范围
            capacity_min = st.number_input("最小产能", min_value=0, value=0)
            capacity_max = st.number_input("最大产能", min_value=1, value=1000)

            # 利用率范围
            utilization_min = st.slider("最小利用率(%)", 0, 100, 0)
            utilization_max = st.slider("最大利用率(%)", 0, 100, 100)

            # 返回字段
            equipment_fields = ["equipment_id", "equipment_name", "equipment_type",
                              "status", "workshop", "capacity", "current_utilization", "maintenance_status"]
            selected_equipment_fields = st.multiselect(
                "返回字段",
                equipment_fields,
                default=equipment_fields
            )

        # 构建设备过滤配置
        equipment_filter_config = {
            "equipment_ids": equipment_ids if equipment_ids else None,
            "equipment_types": equipment_types if equipment_types else None,
            "status_filter": status_filter if status_filter else None,
            "workshop_filter": workshop_filter if workshop_filter else None,
            "capacity_range": [capacity_min, capacity_max] if capacity_min < capacity_max else None,
            "utilization_range": [utilization_min, utilization_max] if utilization_min < utilization_max else None,
            "fields": selected_equipment_fields if selected_equipment_fields != equipment_fields else None
        }

        # 保存配置
        col1, col2, col3 = st.columns(3)

        with col1:
            equipment_filter_name = st.text_input("过滤器名称", value="设备01过滤器")

        with col2:
            if st.button("💾 保存设备过滤配置", type="primary"):
                success = advanced_data_filter_service.add_data_source_filter(
                    "equipment", equipment_filter_name, equipment_filter_config
                )
                if success:
                    st.success(f"✅ 设备过滤配置 '{equipment_filter_name}' 已保存")
                else:
                    st.error("❌ 保存设备过滤配置失败")

        with col3:
            if st.button("🔍 测试设备过滤"):
                st.session_state.test_equipment_filter = equipment_filter_config
                st.rerun()

with tab2:
    st.markdown("#### 📝 自定义SQL查询配置")

    # SQL查询配置
    col1, col2 = st.columns([1, 1])

    with col1:
        query_name = st.text_input("查询名称", placeholder="例如: MES生产订单查询")

        target_source = st.selectbox(
            "目标数据源",
            ["mes_database", "erp_database", "pci_database", "wms_database"],
            help="选择要查询的数据库"
        )

        query_description = st.text_area(
            "查询描述",
            placeholder="描述这个查询的用途和返回的数据...",
            height=100
        )

    with col2:
        st.markdown("##### 📋 SQL查询示例")

        if target_source == "mes_database":
            example_sql = """
-- MES生产订单查询示例
SELECT
    order_id,
    product_code,
    quantity,
    start_time,
    end_time,
    status
FROM production_orders
WHERE start_time >= :start_date
    AND status = :order_status
ORDER BY start_time DESC
LIMIT :limit_count
"""
        elif target_source == "erp_database":
            example_sql = """
-- ERP物料库存查询示例
SELECT
    material_code,
    material_name,
    current_stock,
    safety_stock,
    unit_cost
FROM materials
WHERE current_stock > :min_stock
    AND material_type = :material_type
ORDER BY current_stock DESC
"""
        elif target_source == "pci_database":
            example_sql = """
-- PCI库存查询示例
SELECT
    fs_id,
    material_code,
    quantity,
    location,
    receive_date,
    DATEDIFF(NOW(), receive_date) as days_old
FROM fs_inventory
WHERE quantity > 0
    AND location LIKE :location_pattern
    AND DATEDIFF(NOW(), receive_date) > :min_days_old
ORDER BY receive_date ASC
"""
        else:
            example_sql = """
-- 自定义SQL查询示例
SELECT * FROM your_table
WHERE your_condition = :parameter
"""

        st.code(example_sql, language="sql")

    # SQL查询输入
    st.markdown("##### ✏️ SQL查询语句")
    sql_query = st.text_area(
        "SQL查询",
        value=example_sql.strip(),
        height=200,
        help="输入SQL查询语句，使用 :parameter_name 格式定义参数"
    )

    # 参数配置
    st.markdown("##### ⚙️ 查询参数配置")

    col1, col2, col3 = st.columns(3)

    with col1:
        param1_name = st.text_input("参数1名称", value="start_date")
        param1_value = st.text_input("参数1默认值", value="2024-01-01")
        param1_type = st.selectbox("参数1类型", ["string", "integer", "float", "date"], key="param1_type")

    with col2:
        param2_name = st.text_input("参数2名称", value="order_status")
        param2_value = st.text_input("参数2默认值", value="completed")
        param2_type = st.selectbox("参数2类型", ["string", "integer", "float", "date"], key="param2_type")

    with col3:
        param3_name = st.text_input("参数3名称", value="limit_count")
        param3_value = st.text_input("参数3默认值", value="100")
        param3_type = st.selectbox("参数3类型", ["string", "integer", "float", "date"], key="param3_type")

    # 构建参数配置
    parameters = {}
    if param1_name:
        parameters[param1_name] = {"value": param1_value, "type": param1_type}
    if param2_name:
        parameters[param2_name] = {"value": param2_value, "type": param2_type}
    if param3_name:
        parameters[param3_name] = {"value": param3_value, "type": param3_type}

    # 保存SQL查询配置
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("💾 保存SQL查询", type="primary"):
            if query_name and sql_query:
                success = advanced_data_filter_service.add_custom_sql_query(
                    query_name, target_source, sql_query, parameters, query_description
                )
                if success:
                    st.success(f"✅ SQL查询 '{query_name}' 已保存")
                else:
                    st.error("❌ 保存SQL查询失败")
            else:
                st.warning("请填写查询名称和SQL语句")

    with col2:
        if st.button("🔍 测试SQL查询"):
            if sql_query:
                st.session_state.test_sql_query = {
                    "name": query_name or "测试查询",
                    "sql": sql_query,
                    "parameters": parameters
                }
                st.rerun()
            else:
                st.warning("请输入SQL查询语句")

    with col3:
        if st.button("📋 查看已保存查询"):
            st.session_state.show_saved_queries = True
            st.rerun()

with tab3:
    st.markdown("#### 🎯 字段级别配置")

    # 数据源选择
    available_types = extensible_data_source_service.get_available_data_source_types()
    type_options = {t["display_name"]: t["type_name"] for t in available_types}

    selected_type_display = st.selectbox(
        "选择数据源类型",
        list(type_options.keys()),
        help="选择要配置字段的数据源类型"
    )

    if selected_type_display:
        selected_type = type_options[selected_type_display]

        # 获取当前字段配置
        current_fields = extensible_data_source_service.get_custom_fields_for_source(selected_type)

        st.markdown(f"##### 📋 {selected_type_display} 字段配置")

        # 显示现有字段
        if current_fields:
            st.markdown("**现有字段:**")
            for field in current_fields:
                col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
                with col1:
                    st.text(f"{field['display_name']} ({field['field_name']})")
                with col2:
                    st.text(field['field_type'])
                with col3:
                    st.text("必需" if field['is_required'] else "可选")
                with col4:
                    if st.button("🗑️", key=f"del_{field['field_name']}", help="删除字段"):
                        st.warning("删除功能待实现")

        # 添加新字段
        st.markdown("**添加新字段:**")
        col1, col2, col3 = st.columns(3)

        with col1:
            new_field_name = st.text_input("字段名称", placeholder="例如: custom_field")
            new_display_name = st.text_input("显示名称", placeholder="例如: 自定义字段")

        with col2:
            new_field_type = st.selectbox(
                "字段类型",
                ["string", "integer", "float", "date", "boolean", "json"]
            )
            new_is_required = st.checkbox("必需字段")

        with col3:
            new_default_value = st.text_input("默认值", placeholder="可选")
            new_description = st.text_area("字段描述", placeholder="描述字段用途...", height=100)

        if st.button("➕ 添加字段", type="primary"):
            if new_field_name and new_display_name:
                success = extensible_data_source_service.add_custom_field_to_source(
                    selected_type, new_field_name, new_field_type,
                    new_display_name, new_description, None, new_default_value, new_is_required
                )
                if success:
                    st.success(f"✅ 字段 '{new_display_name}' 已添加")
                    st.rerun()
                else:
                    st.error("❌ 添加字段失败")
            else:
                st.warning("请填写字段名称和显示名称")

with tab4:
    st.markdown("#### 🗄️ 自定义数据源配置")

    # 添加新数据源类型
    st.markdown("##### ➕ 添加新数据源类型")

    col1, col2 = st.columns(2)

    with col1:
        new_type_name = st.text_input("数据源类型名称", placeholder="例如: ppd_data")
        new_display_name = st.text_input("显示名称", placeholder="例如: PPD数据")
        new_description = st.text_area("描述", placeholder="描述数据源的用途和特点...", height=100)

    with col2:
        st.markdown("**支持的数据库类型:**")
        db_type = st.selectbox(
            "数据库类型",
            [
                "mysql", "postgresql", "sqlserver", "oracle", "sqlite",
                "ssas", "mongodb", "redis", "elasticsearch", "clickhouse"
            ]
        )

        st.markdown("**连接配置模板:**")
        if db_type == "ssas":
            connection_template = {
                "database_type": "ssas",
                "server": "localhost",
                "database": "ProductionCube",
                "cube": "ProductionPlan",
                "username": "",
                "password": ""
            }
        elif db_type in ["mysql", "postgresql", "sqlserver", "oracle"]:
            connection_template = {
                "database_type": db_type,
                "host": "localhost",
                "port": 3306 if db_type == "mysql" else 5432,
                "database": "database_name",
                "username": "",
                "password": ""
            }
        elif db_type == "mongodb":
            connection_template = {
                "database_type": "mongodb",
                "host": "localhost",
                "port": 27017,
                "database": "database_name",
                "collection": "collection_name",
                "username": "",
                "password": ""
            }
        else:
            connection_template = {
                "database_type": db_type,
                "host": "localhost",
                "port": 9200,
                "index": "index_name",
                "username": "",
                "password": ""
            }

        st.json(connection_template)

    # 字段定义
    st.markdown("##### 📋 字段定义")

    if "new_source_fields" not in st.session_state:
        st.session_state.new_source_fields = []

    # 添加字段
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        field_name = st.text_input("字段名称", key="new_source_field_name")
    with col2:
        field_display = st.text_input("显示名称", key="new_source_field_display")
    with col3:
        field_type = st.selectbox("类型", ["string", "integer", "float", "date", "boolean"], key="new_source_field_type")
    with col4:
        field_required = st.checkbox("必需", key="new_source_field_required")

    if st.button("➕ 添加字段到列表"):
        if field_name and field_display:
            st.session_state.new_source_fields.append({
                "name": field_name,
                "type": field_type,
                "display": field_display,
                "required": field_required
            })
            st.rerun()

    # 显示已添加的字段
    if st.session_state.new_source_fields:
        st.markdown("**已定义字段:**")
        for i, field in enumerate(st.session_state.new_source_fields):
            col1, col2, col3, col4, col5 = st.columns([2, 2, 1, 1, 1])
            with col1:
                st.text(field["name"])
            with col2:
                st.text(field["display"])
            with col3:
                st.text(field["type"])
            with col4:
                st.text("✓" if field["required"] else "")
            with col5:
                if st.button("🗑️", key=f"del_field_{i}"):
                    st.session_state.new_source_fields.pop(i)
                    st.rerun()

    # 保存数据源类型
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("💾 保存数据源类型", type="primary"):
            if new_type_name and new_display_name and st.session_state.new_source_fields:
                success = extensible_data_source_service.add_custom_data_source_type(
                    new_type_name, new_display_name, new_description,
                    connection_template, st.session_state.new_source_fields
                )
                if success:
                    st.success(f"✅ 数据源类型 '{new_display_name}' 已保存")
                    st.session_state.new_source_fields = []
                    st.rerun()
                else:
                    st.error("❌ 保存数据源类型失败")
            else:
                st.warning("请填写完整信息并至少添加一个字段")

    with col2:
        if st.button("🔄 清空字段列表"):
            st.session_state.new_source_fields = []
            st.rerun()

    with col3:
        if st.button("📋 查看现有数据源"):
            st.session_state.show_existing_sources = True
            st.rerun()

    # 显示现有数据源
    if st.session_state.get('show_existing_sources'):
        st.markdown("##### 📋 现有数据源类型")
        existing_types = extensible_data_source_service.get_available_data_source_types()

        for source_type in existing_types:
            with st.expander(f"🗄️ {source_type['display_name']} ({source_type['type_name']})"):
                st.write(f"**描述**: {source_type['description']}")
                st.write(f"**连接模板**: ")
                st.json(source_type['connection_template'])
                st.write(f"**字段定义**: ")
                for field in source_type['field_schema']:
                    st.write(f"- {field['display']} ({field['name']}): {field['type']} {'[必需]' if field['required'] else '[可选]'}")

        if st.button("关闭数据源列表"):
            del st.session_state.show_existing_sources
            st.rerun()

with tab5:
    st.markdown("#### 🔧 字段扩展配置")

    st.markdown("##### 🎯 为现有数据源添加自定义字段")

    # 选择数据源
    available_types = extensible_data_source_service.get_available_data_source_types()
    if available_types:
        type_options = {f"{t['display_name']} ({t['type_name']})": t['type_name'] for t in available_types}

        selected_source = st.selectbox(
            "选择数据源",
            list(type_options.keys()),
            help="选择要扩展字段的数据源"
        )

        if selected_source:
            source_type = type_options[selected_source]

            # 显示现有字段
            existing_fields = extensible_data_source_service.get_custom_fields_for_source(source_type)

            if existing_fields:
                st.markdown("**现有自定义字段:**")
                for field in existing_fields:
                    st.write(f"- {field['display_name']} ({field['field_name']}): {field['field_type']} {'[必需]' if field['is_required'] else '[可选]'}")

            # 添加新字段表单
            st.markdown("**添加新字段:**")

            col1, col2 = st.columns(2)

            with col1:
                ext_field_name = st.text_input("字段名称", placeholder="例如: production_line")
                ext_display_name = st.text_input("显示名称", placeholder="例如: 生产线")
                ext_field_type = st.selectbox("字段类型", ["string", "integer", "float", "date", "boolean", "json"])

            with col2:
                ext_default_value = st.text_input("默认值", placeholder="可选")
                ext_is_required = st.checkbox("必需字段", key="ext_required")
                ext_description = st.text_area("字段描述", placeholder="描述字段的用途...", height=100)

            # 验证规则配置
            st.markdown("**验证规则 (可选):**")
            col1, col2, col3 = st.columns(3)

            with col1:
                min_length = st.number_input("最小长度", min_value=0, value=0, key="ext_min_length")
                max_length = st.number_input("最大长度", min_value=0, value=0, key="ext_max_length")

            with col2:
                min_value = st.number_input("最小值", value=0.0, key="ext_min_value")
                max_value = st.number_input("最大值", value=0.0, key="ext_max_value")

            with col3:
                pattern = st.text_input("正则表达式", placeholder="例如: ^[A-Z]{2}\\d{3}$", key="ext_pattern")
                enum_values = st.text_input("枚举值", placeholder="用逗号分隔，例如: A,B,C", key="ext_enum")

            # 构建验证规则
            validation_rules = {}
            if min_length > 0:
                validation_rules["min_length"] = min_length
            if max_length > 0:
                validation_rules["max_length"] = max_length
            if min_value != 0:
                validation_rules["min_value"] = min_value
            if max_value != 0:
                validation_rules["max_value"] = max_value
            if pattern:
                validation_rules["pattern"] = pattern
            if enum_values:
                validation_rules["enum"] = [v.strip() for v in enum_values.split(",")]

            # 保存字段
            if st.button("💾 添加字段", type="primary"):
                if ext_field_name and ext_display_name:
                    success = extensible_data_source_service.add_custom_field_to_source(
                        source_type, ext_field_name, ext_field_type,
                        ext_display_name, ext_description, validation_rules,
                        ext_default_value, ext_is_required
                    )
                    if success:
                        st.success(f"✅ 字段 '{ext_display_name}' 已添加到 {selected_source}")
                        st.rerun()
                    else:
                        st.error("❌ 添加字段失败")
                else:
                    st.warning("请填写字段名称和显示名称")
    else:
        st.info("暂无可用的数据源类型，请先在'自定义数据源'标签页添加数据源类型")

with tab6:
    st.markdown("#### 📊 配置预览测试")

    # 测试PCI过滤
    if st.session_state.get('test_pci_filter'):
        st.markdown("##### 🔍 PCI数据过滤测试结果")

        filter_config = st.session_state.test_pci_filter
        filtered_data = advanced_data_filter_service.get_filtered_pci_data(filter_config)

        if filtered_data:
            df = pd.DataFrame(filtered_data)
            st.dataframe(df, use_container_width=True)
            st.success(f"✅ 过滤后返回 {len(filtered_data)} 条记录")
        else:
            st.warning("⚠️ 过滤条件未匹配到任何数据")

        if st.button("清除测试结果"):
            del st.session_state.test_pci_filter
            st.rerun()

    # 测试设备过滤
    if st.session_state.get('test_equipment_filter'):
        st.markdown("##### ⚙️ 设备数据过滤测试结果")

        filter_config = st.session_state.test_equipment_filter
        filtered_data = advanced_data_filter_service.get_filtered_equipment_data(filter_config)

        if filtered_data:
            df = pd.DataFrame(filtered_data)
            st.dataframe(df, use_container_width=True)
            st.success(f"✅ 过滤后返回 {len(filtered_data)} 条记录")
        else:
            st.warning("⚠️ 过滤条件未匹配到任何数据")

        if st.button("清除设备测试结果"):
            del st.session_state.test_equipment_filter
            st.rerun()

    # 测试SQL查询
    if st.session_state.get('test_sql_query'):
        st.markdown("##### 📝 SQL查询测试结果")

        query_info = st.session_state.test_sql_query
        st.code(query_info["sql"], language="sql")

        # 模拟执行查询
        st.info("🔄 正在执行查询...")

        # 这里应该调用实际的查询执行
        mock_result = [
            {"order_id": "MO001", "product_code": "PROD001", "quantity": 100, "status": "completed"},
            {"order_id": "MO002", "product_code": "PROD002", "quantity": 150, "status": "in_progress"}
        ]

        if mock_result:
            df = pd.DataFrame(mock_result)
            st.dataframe(df, use_container_width=True)
            st.success(f"✅ 查询返回 {len(mock_result)} 条记录")

        if st.button("清除SQL测试结果"):
            del st.session_state.test_sql_query
            st.rerun()

    # 显示已保存的查询
    if st.session_state.get('show_saved_queries'):
        st.markdown("##### 📋 已保存的SQL查询")

        saved_queries = advanced_data_filter_service.get_available_sql_queries()

        if saved_queries:
            for query in saved_queries:
                with st.expander(f"📝 {query['name']} ({query['source']})"):
                    st.write(f"**描述**: {query['description']}")
                    st.write(f"**数据源**: {query['source']}")
                    st.write(f"**状态**: {'✅ 启用' if query['enabled'] else '❌ 禁用'}")
        else:
            st.info("暂无已保存的SQL查询")

        if st.button("关闭查询列表"):
            del st.session_state.show_saved_queries
            st.rerun()

    # 如果没有测试结果，显示提示
    if (not st.session_state.get('test_pci_filter') and
        not st.session_state.get('test_equipment_filter') and
        not st.session_state.get('test_sql_query') and
        not st.session_state.get('show_saved_queries')):

        st.info("💡 在其他标签页配置过滤条件或SQL查询后，可以在这里查看测试结果")
