"""
高级数据配置页面
支持精细化数据筛选、自定义SQL查询和字段级别配置
"""

import streamlit as st
import pandas as pd
import json
import sys
import os

# 添加服务路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

try:
    from advanced_data_filter_service import advanced_data_filter_service
except ImportError as e:
    st.error(f"无法导入高级数据过滤服务: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="高级数据配置",
    page_icon="🎛️",
    layout="wide"
)

st.title("🎛️ 高级数据配置")
st.markdown("### 精细化数据筛选、自定义SQL查询和字段级别配置")

# 主要标签页
tab1, tab2, tab3, tab4 = st.tabs([
    "🔍 精细化数据筛选",
    "📝 自定义SQL查询", 
    "🎯 字段级别配置",
    "📊 配置预览测试"
])

with tab1:
    st.markdown("#### 🔍 精细化数据筛选配置")
    
    # 数据源选择
    col1, col2 = st.columns([1, 2])
    
    with col1:
        data_source = st.selectbox(
            "选择数据源",
            ["PCI数据", "设备数据", "上传文件", "用户输入", "自定义数据源"],
            help="选择要配置过滤条件的数据源"
        )
    
    with col2:
        st.info(f"当前配置数据源: {data_source}")
    
    if data_source == "PCI数据":
        st.markdown("##### 📦 PCI数据过滤配置")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # 容器类型过滤
            container_types = st.multiselect(
                "容器类型",
                ["箱子", "托盘", "散装", "桶装"],
                default=["箱子"],
                help="选择要包含的容器类型"
            )
            
            # 库龄过滤
            min_days_old = st.number_input("最小库龄(天)", min_value=0, value=0)
            max_days_old = st.number_input("最大库龄(天)", min_value=0, value=365)
        
        with col2:
            # 优先级过滤
            priority_threshold = st.slider(
                "优先级阈值",
                min_value=0, max_value=100, value=50,
                help="只包含优先级高于此值的物料"
            )
            
            # 数量范围
            quantity_min = st.number_input("最小数量", min_value=0, value=1)
            quantity_max = st.number_input("最大数量", min_value=1, value=1000)
        
        with col3:
            # 特定FS ID
            specific_fs_ids = st.text_area(
                "特定FS ID (每行一个)",
                placeholder="FS001\nFS002\nFS003",
                help="只包含指定的FS ID，留空表示包含所有"
            )
            
            # 其他选项
            exclude_empty = st.checkbox("排除空箱", value=True)
            
            # 返回字段选择
            available_fields = ["fs_id", "container_type", "material_code", "quantity", 
                              "days_old", "consumption_priority", "location", "batch_number"]
            selected_fields = st.multiselect(
                "返回字段",
                available_fields,
                default=available_fields,
                help="选择要返回的字段，留空表示返回所有字段"
            )
        
        # 构建过滤配置
        pci_filter_config = {
            "container_types": container_types if container_types else None,
            "min_days_old": min_days_old if min_days_old > 0 else None,
            "max_days_old": max_days_old if max_days_old < 365 else None,
            "priority_threshold": priority_threshold if priority_threshold > 0 else None,
            "quantity_range": [quantity_min, quantity_max] if quantity_min < quantity_max else None,
            "specific_fs_ids": specific_fs_ids.strip().split('\n') if specific_fs_ids.strip() else None,
            "exclude_empty": exclude_empty,
            "fields": selected_fields if selected_fields != available_fields else None
        }
        
        # 保存配置按钮
        col1, col2, col3 = st.columns(3)
        
        with col1:
            filter_name = st.text_input("过滤器名称", value="PCI_箱子过滤器")
        
        with col2:
            if st.button("💾 保存过滤配置", type="primary"):
                success = advanced_data_filter_service.add_data_source_filter(
                    "pci", filter_name, pci_filter_config
                )
                if success:
                    st.success(f"✅ 过滤配置 '{filter_name}' 已保存")
                else:
                    st.error("❌ 保存过滤配置失败")
        
        with col3:
            if st.button("🔍 测试过滤效果"):
                st.session_state.test_pci_filter = pci_filter_config
                st.rerun()
    
    elif data_source == "设备数据":
        st.markdown("##### ⚙️ 设备数据过滤配置")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # 特定设备ID
            equipment_ids = st.multiselect(
                "设备ID",
                ["L01", "L02", "L03", "L04", "Tank01", "Tank02", "Tank03", "Tank04", "Tank05"],
                default=["L01"],
                help="选择特定设备，留空表示包含所有设备"
            )
            
            # 设备类型
            equipment_types = st.multiselect(
                "设备类型",
                ["生产线", "储罐", "包装线", "检测设备"],
                help="选择设备类型"
            )
        
        with col2:
            # 状态过滤
            status_filter = st.multiselect(
                "设备状态",
                ["运行中", "停机", "维护中", "故障", "空闲"],
                default=["运行中"],
                help="选择要包含的设备状态"
            )
            
            # 车间过滤
            workshop_filter = st.multiselect(
                "车间",
                ["A车间", "B车间", "C车间", "D车间"],
                help="选择车间"
            )
        
        with col3:
            # 产能范围
            capacity_min = st.number_input("最小产能", min_value=0, value=0)
            capacity_max = st.number_input("最大产能", min_value=1, value=1000)
            
            # 利用率范围
            utilization_min = st.slider("最小利用率(%)", 0, 100, 0)
            utilization_max = st.slider("最大利用率(%)", 0, 100, 100)
            
            # 返回字段
            equipment_fields = ["equipment_id", "equipment_name", "equipment_type", 
                              "status", "workshop", "capacity", "current_utilization", "maintenance_status"]
            selected_equipment_fields = st.multiselect(
                "返回字段",
                equipment_fields,
                default=equipment_fields
            )
        
        # 构建设备过滤配置
        equipment_filter_config = {
            "equipment_ids": equipment_ids if equipment_ids else None,
            "equipment_types": equipment_types if equipment_types else None,
            "status_filter": status_filter if status_filter else None,
            "workshop_filter": workshop_filter if workshop_filter else None,
            "capacity_range": [capacity_min, capacity_max] if capacity_min < capacity_max else None,
            "utilization_range": [utilization_min, utilization_max] if utilization_min < utilization_max else None,
            "fields": selected_equipment_fields if selected_equipment_fields != equipment_fields else None
        }
        
        # 保存配置
        col1, col2, col3 = st.columns(3)
        
        with col1:
            equipment_filter_name = st.text_input("过滤器名称", value="设备01过滤器")
        
        with col2:
            if st.button("💾 保存设备过滤配置", type="primary"):
                success = advanced_data_filter_service.add_data_source_filter(
                    "equipment", equipment_filter_name, equipment_filter_config
                )
                if success:
                    st.success(f"✅ 设备过滤配置 '{equipment_filter_name}' 已保存")
                else:
                    st.error("❌ 保存设备过滤配置失败")
        
        with col3:
            if st.button("🔍 测试设备过滤"):
                st.session_state.test_equipment_filter = equipment_filter_config
                st.rerun()

with tab2:
    st.markdown("#### 📝 自定义SQL查询配置")
    
    # SQL查询配置
    col1, col2 = st.columns([1, 1])
    
    with col1:
        query_name = st.text_input("查询名称", placeholder="例如: MES生产订单查询")
        
        target_source = st.selectbox(
            "目标数据源",
            ["mes_database", "erp_database", "pci_database", "wms_database"],
            help="选择要查询的数据库"
        )
        
        query_description = st.text_area(
            "查询描述",
            placeholder="描述这个查询的用途和返回的数据...",
            height=100
        )
    
    with col2:
        st.markdown("##### 📋 SQL查询示例")
        
        if target_source == "mes_database":
            example_sql = """
-- MES生产订单查询示例
SELECT 
    order_id,
    product_code,
    quantity,
    start_time,
    end_time,
    status
FROM production_orders 
WHERE start_time >= :start_date 
    AND status = :order_status
ORDER BY start_time DESC
LIMIT :limit_count
"""
        elif target_source == "erp_database":
            example_sql = """
-- ERP物料库存查询示例
SELECT 
    material_code,
    material_name,
    current_stock,
    safety_stock,
    unit_cost
FROM materials 
WHERE current_stock > :min_stock
    AND material_type = :material_type
ORDER BY current_stock DESC
"""
        elif target_source == "pci_database":
            example_sql = """
-- PCI库存查询示例
SELECT 
    fs_id,
    material_code,
    quantity,
    location,
    receive_date,
    DATEDIFF(NOW(), receive_date) as days_old
FROM fs_inventory 
WHERE quantity > 0
    AND location LIKE :location_pattern
    AND DATEDIFF(NOW(), receive_date) > :min_days_old
ORDER BY receive_date ASC
"""
        else:
            example_sql = """
-- 自定义SQL查询示例
SELECT * FROM your_table 
WHERE your_condition = :parameter
"""
        
        st.code(example_sql, language="sql")
    
    # SQL查询输入
    st.markdown("##### ✏️ SQL查询语句")
    sql_query = st.text_area(
        "SQL查询",
        value=example_sql.strip(),
        height=200,
        help="输入SQL查询语句，使用 :parameter_name 格式定义参数"
    )
    
    # 参数配置
    st.markdown("##### ⚙️ 查询参数配置")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        param1_name = st.text_input("参数1名称", value="start_date")
        param1_value = st.text_input("参数1默认值", value="2024-01-01")
        param1_type = st.selectbox("参数1类型", ["string", "integer", "float", "date"], key="param1_type")
    
    with col2:
        param2_name = st.text_input("参数2名称", value="order_status")
        param2_value = st.text_input("参数2默认值", value="completed")
        param2_type = st.selectbox("参数2类型", ["string", "integer", "float", "date"], key="param2_type")
    
    with col3:
        param3_name = st.text_input("参数3名称", value="limit_count")
        param3_value = st.text_input("参数3默认值", value="100")
        param3_type = st.selectbox("参数3类型", ["string", "integer", "float", "date"], key="param3_type")
    
    # 构建参数配置
    parameters = {}
    if param1_name:
        parameters[param1_name] = {"value": param1_value, "type": param1_type}
    if param2_name:
        parameters[param2_name] = {"value": param2_value, "type": param2_type}
    if param3_name:
        parameters[param3_name] = {"value": param3_value, "type": param3_type}
    
    # 保存SQL查询配置
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("💾 保存SQL查询", type="primary"):
            if query_name and sql_query:
                success = advanced_data_filter_service.add_custom_sql_query(
                    query_name, target_source, sql_query, parameters, query_description
                )
                if success:
                    st.success(f"✅ SQL查询 '{query_name}' 已保存")
                else:
                    st.error("❌ 保存SQL查询失败")
            else:
                st.warning("请填写查询名称和SQL语句")
    
    with col2:
        if st.button("🔍 测试SQL查询"):
            if sql_query:
                st.session_state.test_sql_query = {
                    "name": query_name or "测试查询",
                    "sql": sql_query,
                    "parameters": parameters
                }
                st.rerun()
            else:
                st.warning("请输入SQL查询语句")
    
    with col3:
        if st.button("📋 查看已保存查询"):
            st.session_state.show_saved_queries = True
            st.rerun()

with tab3:
    st.markdown("#### 🎯 字段级别配置")
    
    st.info("🚧 字段级别配置功能正在开发中，将支持字段映射、数据转换、计算字段等高级功能")
    
    # 字段映射配置预览
    st.markdown("##### 📋 字段映射配置 (预览)")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**原始字段**")
        original_fields = ["equipment_id", "status", "capacity", "utilization"]
        for field in original_fields:
            st.text_input(f"原始字段", value=field, disabled=True, key=f"orig_{field}")
    
    with col2:
        st.markdown("**映射字段**")
        mapped_fields = ["设备编号", "设备状态", "设备产能", "利用率"]
        for i, field in enumerate(mapped_fields):
            st.text_input(f"映射字段", value=field, key=f"mapped_{i}")

with tab4:
    st.markdown("#### 📊 配置预览测试")
    
    # 测试PCI过滤
    if st.session_state.get('test_pci_filter'):
        st.markdown("##### 🔍 PCI数据过滤测试结果")
        
        filter_config = st.session_state.test_pci_filter
        filtered_data = advanced_data_filter_service.get_filtered_pci_data(filter_config)
        
        if filtered_data:
            df = pd.DataFrame(filtered_data)
            st.dataframe(df, use_container_width=True)
            st.success(f"✅ 过滤后返回 {len(filtered_data)} 条记录")
        else:
            st.warning("⚠️ 过滤条件未匹配到任何数据")
        
        if st.button("清除测试结果"):
            del st.session_state.test_pci_filter
            st.rerun()
    
    # 测试设备过滤
    if st.session_state.get('test_equipment_filter'):
        st.markdown("##### ⚙️ 设备数据过滤测试结果")
        
        filter_config = st.session_state.test_equipment_filter
        filtered_data = advanced_data_filter_service.get_filtered_equipment_data(filter_config)
        
        if filtered_data:
            df = pd.DataFrame(filtered_data)
            st.dataframe(df, use_container_width=True)
            st.success(f"✅ 过滤后返回 {len(filtered_data)} 条记录")
        else:
            st.warning("⚠️ 过滤条件未匹配到任何数据")
        
        if st.button("清除设备测试结果"):
            del st.session_state.test_equipment_filter
            st.rerun()
    
    # 测试SQL查询
    if st.session_state.get('test_sql_query'):
        st.markdown("##### 📝 SQL查询测试结果")
        
        query_info = st.session_state.test_sql_query
        st.code(query_info["sql"], language="sql")
        
        # 模拟执行查询
        st.info("🔄 正在执行查询...")
        
        # 这里应该调用实际的查询执行
        mock_result = [
            {"order_id": "MO001", "product_code": "PROD001", "quantity": 100, "status": "completed"},
            {"order_id": "MO002", "product_code": "PROD002", "quantity": 150, "status": "in_progress"}
        ]
        
        if mock_result:
            df = pd.DataFrame(mock_result)
            st.dataframe(df, use_container_width=True)
            st.success(f"✅ 查询返回 {len(mock_result)} 条记录")
        
        if st.button("清除SQL测试结果"):
            del st.session_state.test_sql_query
            st.rerun()
    
    # 显示已保存的查询
    if st.session_state.get('show_saved_queries'):
        st.markdown("##### 📋 已保存的SQL查询")
        
        saved_queries = advanced_data_filter_service.get_available_sql_queries()
        
        if saved_queries:
            for query in saved_queries:
                with st.expander(f"📝 {query['name']} ({query['source']})"):
                    st.write(f"**描述**: {query['description']}")
                    st.write(f"**数据源**: {query['source']}")
                    st.write(f"**状态**: {'✅ 启用' if query['enabled'] else '❌ 禁用'}")
        else:
            st.info("暂无已保存的SQL查询")
        
        if st.button("关闭查询列表"):
            del st.session_state.show_saved_queries
            st.rerun()
    
    # 如果没有测试结果，显示提示
    if (not st.session_state.get('test_pci_filter') and 
        not st.session_state.get('test_equipment_filter') and 
        not st.session_state.get('test_sql_query') and
        not st.session_state.get('show_saved_queries')):
        
        st.info("💡 在其他标签页配置过滤条件或SQL查询后，可以在这里查看测试结果")
