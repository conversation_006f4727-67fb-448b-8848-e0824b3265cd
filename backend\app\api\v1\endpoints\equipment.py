"""
设备管理API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.logging_config import business_logger
from app.schemas.production import (
    EquipmentResponse, EquipmentCreate, EquipmentUpdate,
    EquipmentListResponse
)
from app.services.equipment_service import EquipmentService
from app.api.dependencies import (
    get_current_user, require_permission, get_pagination_params, PaginationParams
)

router = APIRouter()


@router.get("", response_model=EquipmentListResponse, summary="获取设备列表")
async def get_equipment_list(
    pagination: PaginationParams = Depends(get_pagination_params),
    equipment_type: Optional[str] = Query(None, description="设备类型过滤"),
    status_filter: Optional[str] = Query(None, description="状态过滤"),
    workshop: Optional[str] = Query(None, description="车间过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: dict = Depends(require_permission("equipment.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取设备列表"""
    try:
        equipment_service = EquipmentService(db)
        equipment_list, total = await equipment_service.get_equipment_list(
            page=pagination.page,
            page_size=pagination.page_size,
            equipment_type=equipment_type,
            status_filter=status_filter,
            workshop=workshop,
            search=search
        )
        
        total_pages = (total + pagination.page_size - 1) // pagination.page_size
        
        return {
            "equipment": equipment_list,
            "total": total,
            "page": pagination.page,
            "page_size": pagination.page_size,
            "total_pages": total_pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取设备列表失败"
        )


@router.get("/{equipment_id}", response_model=EquipmentResponse, summary="获取设备详情")
async def get_equipment(
    equipment_id: str,
    current_user: dict = Depends(require_permission("equipment.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取设备详情"""
    try:
        equipment_service = EquipmentService(db)
        equipment = await equipment_service.get_equipment_by_id(equipment_id)
        
        if not equipment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )
        
        return equipment
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取设备详情失败"
        )


@router.post("", response_model=EquipmentResponse, summary="创建设备")
async def create_equipment(
    equipment_data: EquipmentCreate,
    current_user: dict = Depends(require_permission("equipment.create")),
    db: AsyncSession = Depends(get_db)
):
    """创建设备"""
    try:
        equipment_service = EquipmentService(db)
        
        # 检查设备编码是否已存在
        existing_equipment = await equipment_service.get_equipment_by_code(equipment_data.equipment_code)
        if existing_equipment:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="设备编码已存在"
            )
        
        equipment = await equipment_service.create_equipment(
            equipment_data=equipment_data,
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="equipment_create",
            details={
                "equipment_id": equipment.id,
                "equipment_code": equipment.equipment_code,
                "equipment_name": equipment.equipment_name
            }
        )
        
        return equipment
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="equipment_create_error",
            error_message=str(e),
            details={"equipment_code": equipment_data.equipment_code}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建设备失败"
        )


@router.put("/{equipment_id}", response_model=EquipmentResponse, summary="更新设备")
async def update_equipment(
    equipment_id: str,
    equipment_data: EquipmentUpdate,
    current_user: dict = Depends(require_permission("equipment.update")),
    db: AsyncSession = Depends(get_db)
):
    """更新设备"""
    try:
        equipment_service = EquipmentService(db)
        
        # 检查设备是否存在
        existing_equipment = await equipment_service.get_equipment_by_id(equipment_id)
        if not existing_equipment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )
        
        equipment = await equipment_service.update_equipment(
            equipment_id=equipment_id,
            equipment_data=equipment_data,
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="equipment_update",
            details={
                "equipment_id": equipment_id,
                "changes": equipment_data.dict(exclude_unset=True)
            }
        )
        
        return equipment
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="equipment_update_error",
            error_message=str(e),
            details={"equipment_id": equipment_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新设备失败"
        )


@router.delete("/{equipment_id}", summary="删除设备")
async def delete_equipment(
    equipment_id: str,
    current_user: dict = Depends(require_permission("equipment.delete")),
    db: AsyncSession = Depends(get_db)
):
    """删除设备"""
    try:
        equipment_service = EquipmentService(db)
        
        # 检查设备是否存在
        existing_equipment = await equipment_service.get_equipment_by_id(equipment_id)
        if not existing_equipment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )
        
        # 检查设备是否正在使用
        is_in_use = await equipment_service.check_equipment_in_use(equipment_id)
        if is_in_use:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="设备正在使用中，无法删除"
            )
        
        success = await equipment_service.delete_equipment(equipment_id)
        
        if success:
            business_logger.log_user_action(
                user_id=current_user["id"],
                action="equipment_delete",
                details={
                    "equipment_id": equipment_id,
                    "equipment_code": existing_equipment.equipment_code
                }
            )
            
            return {"success": True, "message": "设备删除成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="设备删除失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="equipment_delete_error",
            error_message=str(e),
            details={"equipment_id": equipment_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除设备失败"
        )


@router.get("/status/overview", summary="获取设备状态概览")
async def get_equipment_status_overview(
    current_user: dict = Depends(require_permission("equipment.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取设备状态概览"""
    try:
        equipment_service = EquipmentService(db)
        status_overview = await equipment_service.get_status_overview()
        
        return {
            "success": True,
            "data": status_overview
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取设备状态概览失败"
        )


@router.get("/utilization/statistics", summary="获取设备利用率统计")
async def get_equipment_utilization_statistics(
    days: int = Query(7, ge=1, le=90, description="统计天数"),
    current_user: dict = Depends(require_permission("equipment.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取设备利用率统计"""
    try:
        equipment_service = EquipmentService(db)
        utilization_stats = await equipment_service.get_utilization_statistics(days)
        
        return {
            "success": True,
            "data": utilization_stats
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取设备利用率统计失败"
        )


@router.post("/{equipment_id}/maintenance", summary="设备维护")
async def schedule_equipment_maintenance(
    equipment_id: str,
    maintenance_data: dict,
    current_user: dict = Depends(require_permission("equipment.update")),
    db: AsyncSession = Depends(get_db)
):
    """安排设备维护"""
    try:
        equipment_service = EquipmentService(db)
        
        # 检查设备是否存在
        equipment = await equipment_service.get_equipment_by_id(equipment_id)
        if not equipment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备不存在"
            )
        
        # 安排维护
        maintenance = await equipment_service.schedule_maintenance(
            equipment_id=equipment_id,
            maintenance_data=maintenance_data,
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="equipment_maintenance_schedule",
            details={
                "equipment_id": equipment_id,
                "maintenance_type": maintenance_data.get("maintenance_type"),
                "scheduled_date": maintenance_data.get("scheduled_date")
            }
        )
        
        return {
            "success": True,
            "message": "设备维护已安排",
            "data": maintenance
        }
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="equipment_maintenance_error",
            error_message=str(e),
            details={"equipment_id": equipment_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="安排设备维护失败"
        )
