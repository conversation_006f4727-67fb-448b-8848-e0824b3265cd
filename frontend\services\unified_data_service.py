"""
统一数据服务 - 整合所有数据管理功能
包含数据上传、数据集成、数据源管理、数据处理等功能
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np
import uuid
import os

# 导入现有数据服务
from .data_integration_service import data_integration_service
from .data_source_plugins import DataSourcePlugin
from .equipment_data_service import equipment_data_service
from .pci_service import pci_service

logger = logging.getLogger(__name__)


class DataSourceType(Enum):
    """数据源类型"""
    FILE_UPLOAD = "file_upload"
    EMAIL_IMPORT = "email_import"
    DATABASE = "database"
    API_ENDPOINT = "api_endpoint"
    EQUIPMENT_SENSOR = "equipment_sensor"
    PCI_SYSTEM = "pci_system"
    ERP_SYSTEM = "erp_system"
    MES_SYSTEM = "mes_system"


class DataProcessingType(Enum):
    """数据处理类型"""
    EXTRACTION = "extraction"
    TRANSFORMATION = "transformation"
    VALIDATION = "validation"
    INTEGRATION = "integration"
    ANALYSIS = "analysis"
    EXPORT = "export"


@dataclass
class DataRequest:
    """统一数据请求"""
    source_type: DataSourceType
    processing_type: DataProcessingType
    data_content: Any
    metadata: Optional[Dict[str, Any]] = None
    user_id: str = "system"
    request_id: Optional[str] = None


@dataclass
class DataResult:
    """统一数据结果"""
    success: bool
    source_type: DataSourceType
    processing_type: DataProcessingType
    processed_data: Dict[str, Any]
    metadata: Dict[str, Any]
    processing_time: float
    timestamp: datetime
    request_id: str
    error_message: Optional[str] = None


class UnifiedDataService:
    """统一数据服务 - 整合所有数据功能"""
    
    def __init__(self):
        # 初始化各个数据组件
        self.data_integration = data_integration_service
        self.equipment_service = equipment_data_service
        self.pci_service = pci_service
        
        # 数据源插件注册表
        self.data_source_plugins = {}
        
        # 数据处理状态
        self.processing_status = {
            DataSourceType.FILE_UPLOAD: True,
            DataSourceType.EMAIL_IMPORT: True,
            DataSourceType.DATABASE: True,
            DataSourceType.API_ENDPOINT: True,
            DataSourceType.EQUIPMENT_SENSOR: True,
            DataSourceType.PCI_SYSTEM: True,
            DataSourceType.ERP_SYSTEM: True,
            DataSourceType.MES_SYSTEM: True
        }
        
        # 服务配置
        self.config = {
            "max_file_size": 100 * 1024 * 1024,  # 100MB
            "supported_formats": [".xlsx", ".csv", ".json", ".xml", ".txt"],
            "auto_processing": True,
            "data_validation": True,
            "backup_enabled": True,
            "retention_days": 90
        }
        
        # 处理历史和统计
        self.processing_history = []
        self.data_statistics = {}
        self.active_processes = {}
        
        # 初始化数据源插件
        self._initialize_data_source_plugins()
    
    def _initialize_data_source_plugins(self):
        """初始化数据源插件"""
        # 注册默认数据源插件
        self.register_data_source_plugin("excel_processor", ExcelDataProcessor())
        self.register_data_source_plugin("email_processor", EmailDataProcessor())
        self.register_data_source_plugin("equipment_processor", EquipmentDataProcessor())
        self.register_data_source_plugin("pci_processor", PCIDataProcessor())
    
    def register_data_source_plugin(self, plugin_name: str, plugin_instance):
        """注册数据源插件"""
        self.data_source_plugins[plugin_name] = {
            "instance": plugin_instance,
            "enabled": True,
            "registered_at": datetime.now().isoformat()
        }
    
    async def process_data(self, request: DataRequest) -> DataResult:
        """处理数据 - 统一入口"""
        start_time = datetime.now()
        request_id = request.request_id or str(uuid.uuid4())
        
        try:
            # 验证数据源状态
            if not self.processing_status.get(request.source_type, False):
                return DataResult(
                    success=False,
                    source_type=request.source_type,
                    processing_type=request.processing_type,
                    processed_data={},
                    metadata={},
                    processing_time=0,
                    timestamp=datetime.now(),
                    request_id=request_id,
                    error_message=f"数据源 {request.source_type.value} 当前不可用"
                )
            
            # 添加到活跃处理列表
            self.active_processes[request_id] = {
                "source_type": request.source_type,
                "processing_type": request.processing_type,
                "start_time": start_time,
                "status": "processing"
            }
            
            # 根据数据源类型和处理类型路由
            if request.source_type == DataSourceType.FILE_UPLOAD:
                processed_data = await self._process_file_upload(request)
            elif request.source_type == DataSourceType.EMAIL_IMPORT:
                processed_data = await self._process_email_import(request)
            elif request.source_type == DataSourceType.EQUIPMENT_SENSOR:
                processed_data = await self._process_equipment_data(request)
            elif request.source_type == DataSourceType.PCI_SYSTEM:
                processed_data = await self._process_pci_data(request)
            elif request.source_type == DataSourceType.DATABASE:
                processed_data = await self._process_database_data(request)
            elif request.source_type == DataSourceType.API_ENDPOINT:
                processed_data = await self._process_api_data(request)
            else:
                processed_data = await self._process_generic_data(request)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 生成元数据
            metadata = self._generate_metadata(request, processed_data, processing_time)
            
            # 创建结果对象
            result = DataResult(
                success=True,
                source_type=request.source_type,
                processing_type=request.processing_type,
                processed_data=processed_data,
                metadata=metadata,
                processing_time=processing_time,
                timestamp=datetime.now(),
                request_id=request_id
            )
            
            # 记录处理历史
            self._record_processing(request, result)
            
            # 触发数据集成
            if self.config["auto_processing"]:
                await self._trigger_data_integration(request, result)
            
            return result
            
        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return DataResult(
                success=False,
                source_type=request.source_type,
                processing_type=request.processing_type,
                processed_data={},
                metadata={"processing_time": processing_time},
                processing_time=processing_time,
                timestamp=datetime.now(),
                request_id=request_id,
                error_message=str(e)
            )
        
        finally:
            # 从活跃处理列表中移除
            if request_id in self.active_processes:
                del self.active_processes[request_id]
    
    async def _process_file_upload(self, request: DataRequest) -> Dict[str, Any]:
        """处理文件上传"""
        file_data = request.data_content
        
        if request.processing_type == DataProcessingType.EXTRACTION:
            # 文件内容提取
            if file_data.get("file_type") == "excel":
                plugin = self.data_source_plugins["excel_processor"]["instance"]
                return await plugin.extract_data(file_data)
            else:
                return {"raw_data": file_data, "format": "generic"}
        
        elif request.processing_type == DataProcessingType.VALIDATION:
            # 数据验证
            return self._validate_file_data(file_data)
        
        elif request.processing_type == DataProcessingType.INTEGRATION:
            # 数据集成
            return await self._integrate_file_data(file_data)
        
        else:
            return {"processed": True, "data": file_data}
    
    async def _process_email_import(self, request: DataRequest) -> Dict[str, Any]:
        """处理邮件导入"""
        email_data = request.data_content
        
        plugin = self.data_source_plugins["email_processor"]["instance"]
        
        if request.processing_type == DataProcessingType.EXTRACTION:
            return await plugin.extract_email_data(email_data)
        elif request.processing_type == DataProcessingType.TRANSFORMATION:
            return await plugin.transform_email_data(email_data)
        else:
            return {"email_processed": True, "data": email_data}
    
    async def _process_equipment_data(self, request: DataRequest) -> Dict[str, Any]:
        """处理设备数据"""
        equipment_data = request.data_content
        
        plugin = self.data_source_plugins["equipment_processor"]["instance"]
        
        if request.processing_type == DataProcessingType.EXTRACTION:
            return await plugin.extract_equipment_data(equipment_data)
        elif request.processing_type == DataProcessingType.ANALYSIS:
            return await plugin.analyze_equipment_data(equipment_data)
        else:
            return {"equipment_processed": True, "data": equipment_data}
    
    async def _process_pci_data(self, request: DataRequest) -> Dict[str, Any]:
        """处理PCI数据"""
        pci_data = request.data_content
        
        plugin = self.data_source_plugins["pci_processor"]["instance"]
        
        if request.processing_type == DataProcessingType.EXTRACTION:
            return await plugin.extract_pci_data(pci_data)
        elif request.processing_type == DataProcessingType.VALIDATION:
            return await plugin.validate_pci_data(pci_data)
        else:
            return {"pci_processed": True, "data": pci_data}
    
    async def _process_database_data(self, request: DataRequest) -> Dict[str, Any]:
        """处理数据库数据"""
        db_data = request.data_content
        
        # 模拟数据库数据处理
        return {
            "database_query_result": db_data,
            "records_count": len(db_data.get("records", [])),
            "processing_type": request.processing_type.value
        }
    
    async def _process_api_data(self, request: DataRequest) -> Dict[str, Any]:
        """处理API数据"""
        api_data = request.data_content
        
        # 模拟API数据处理
        return {
            "api_response": api_data,
            "status_code": 200,
            "processing_type": request.processing_type.value
        }
    
    async def _process_generic_data(self, request: DataRequest) -> Dict[str, Any]:
        """处理通用数据"""
        return {
            "generic_data": request.data_content,
            "processing_type": request.processing_type.value,
            "processed_at": datetime.now().isoformat()
        }
    
    def _validate_file_data(self, file_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证文件数据"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "statistics": {}
        }
        
        # 文件大小验证
        file_size = file_data.get("size", 0)
        if file_size > self.config["max_file_size"]:
            validation_result["valid"] = False
            validation_result["errors"].append(f"文件大小超过限制: {file_size} > {self.config['max_file_size']}")
        
        # 文件格式验证
        file_extension = file_data.get("extension", "")
        if file_extension not in self.config["supported_formats"]:
            validation_result["valid"] = False
            validation_result["errors"].append(f"不支持的文件格式: {file_extension}")
        
        # 数据内容验证
        if "content" in file_data:
            content = file_data["content"]
            if isinstance(content, pd.DataFrame):
                validation_result["statistics"] = {
                    "rows": len(content),
                    "columns": len(content.columns),
                    "null_values": content.isnull().sum().sum()
                }
                
                if len(content) == 0:
                    validation_result["warnings"].append("文件内容为空")
        
        return validation_result
    
    async def _integrate_file_data(self, file_data: Dict[str, Any]) -> Dict[str, Any]:
        """集成文件数据"""
        # 使用数据集成服务
        try:
            integration_result = self.data_integration.integrate_uploaded_data(file_data)
            return {
                "integration_success": True,
                "integrated_data": integration_result,
                "integration_timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "integration_success": False,
                "error": str(e),
                "integration_timestamp": datetime.now().isoformat()
            }
    
    def _generate_metadata(self, request: DataRequest, processed_data: Dict[str, Any], 
                          processing_time: float) -> Dict[str, Any]:
        """生成元数据"""
        return {
            "source_type": request.source_type.value,
            "processing_type": request.processing_type.value,
            "processing_time": processing_time,
            "data_size": len(str(processed_data)),
            "user_id": request.user_id,
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0"
        }
    
    def _record_processing(self, request: DataRequest, result: DataResult):
        """记录处理历史"""
        self.processing_history.append({
            "timestamp": result.timestamp,
            "source_type": request.source_type.value,
            "processing_type": request.processing_type.value,
            "success": result.success,
            "processing_time": result.processing_time,
            "user_id": request.user_id,
            "data_size": len(str(result.processed_data))
        })
        
        # 保持最近1000条记录
        if len(self.processing_history) > 1000:
            self.processing_history = self.processing_history[-1000:]
    
    async def _trigger_data_integration(self, request: DataRequest, result: DataResult):
        """触发数据集成"""
        if result.success:
            integration_data = {
                "source_type": request.source_type.value,
                "processed_data": result.processed_data,
                "metadata": result.metadata,
                "timestamp": result.timestamp
            }
            
            # 异步集成，不阻塞主流程
            asyncio.create_task(self._async_data_integration(integration_data))
    
    async def _async_data_integration(self, integration_data: Dict[str, Any]):
        """异步数据集成"""
        try:
            # 这里可以调用数据集成服务的集成方法
            # self.data_integration.integrate_processed_data(integration_data)
            pass
        except Exception as e:
            logger.error(f"异步数据集成失败: {e}")
    
    def get_data_service_status(self) -> Dict[str, Any]:
        """获取数据服务状态"""
        return {
            "processing_status": {k.value: v for k, v in self.processing_status.items()},
            "active_processes": len(self.active_processes),
            "total_processed": len(self.processing_history),
            "data_source_plugins": len(self.data_source_plugins),
            "config": self.config
        }
    
    def get_processing_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取处理历史"""
        return self.processing_history[-limit:]
    
    def get_data_statistics(self) -> Dict[str, Any]:
        """获取数据统计"""
        if not self.processing_history:
            return {"message": "暂无处理历史"}
        
        # 计算各数据源的处理统计
        source_stats = {}
        for record in self.processing_history:
            source_type = record["source_type"]
            if source_type not in source_stats:
                source_stats[source_type] = {
                    "total_processed": 0,
                    "successful_processed": 0,
                    "total_time": 0,
                    "avg_time": 0,
                    "success_rate": 0
                }
            
            stats = source_stats[source_type]
            stats["total_processed"] += 1
            stats["total_time"] += record["processing_time"]
            
            if record["success"]:
                stats["successful_processed"] += 1
        
        # 计算平均值和成功率
        for source_type, stats in source_stats.items():
            if stats["total_processed"] > 0:
                stats["avg_time"] = stats["total_time"] / stats["total_processed"]
                stats["success_rate"] = stats["successful_processed"] / stats["total_processed"]
        
        return {
            "source_statistics": source_stats,
            "total_processed": len(self.processing_history),
            "overall_success_rate": sum(1 for r in self.processing_history if r["success"]) / len(self.processing_history)
        }


# 数据处理插件基类和实现
class DataProcessor:
    """数据处理器基类"""
    
    async def extract_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        raise NotImplementedError
    
    async def transform_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        raise NotImplementedError
    
    async def validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        raise NotImplementedError


class ExcelDataProcessor(DataProcessor):
    """Excel数据处理器"""
    
    async def extract_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # 模拟Excel数据提取
        return {
            "extracted_sheets": ["Sheet1", "Sheet2"],
            "total_rows": 100,
            "total_columns": 10,
            "data_preview": "Excel数据预览..."
        }


class EmailDataProcessor(DataProcessor):
    """邮件数据处理器"""
    
    async def extract_email_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # 模拟邮件数据提取
        return {
            "sender": data.get("sender", "unknown"),
            "subject": data.get("subject", ""),
            "attachments": data.get("attachments", []),
            "extracted_tables": []
        }
    
    async def transform_email_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # 模拟邮件数据转换
        return {
            "transformed_data": data,
            "format": "standardized"
        }


class EquipmentDataProcessor(DataProcessor):
    """设备数据处理器"""
    
    async def extract_equipment_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # 模拟设备数据提取
        return {
            "equipment_count": len(data.get("equipment", [])),
            "sensor_data": data.get("sensors", {}),
            "status_summary": "设备状态正常"
        }
    
    async def analyze_equipment_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # 模拟设备数据分析
        return {
            "utilization_rate": 85.5,
            "efficiency_score": 92.3,
            "maintenance_alerts": []
        }


class PCIDataProcessor(DataProcessor):
    """PCI数据处理器"""
    
    async def extract_pci_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # 模拟PCI数据提取
        return {
            "fs_items_count": len(data.get("fs_items", [])),
            "priority_items": data.get("priority_items", []),
            "fifo_analysis": "FIFO分析结果"
        }
    
    async def validate_pci_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # 模拟PCI数据验证
        return {
            "validation_passed": True,
            "data_quality_score": 95.2,
            "issues_found": []
        }


# 全局统一数据服务实例
unified_data_service = UnifiedDataService()
