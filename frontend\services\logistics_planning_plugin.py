"""
物流规划插件
扩展智能规划引擎，增加物流运输、仓储、配送等约束条件
支持供应商集成、采购协同、库存协同等供应链协同功能
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import numpy as np
import asyncio
from dataclasses import dataclass

from .data_source_plugins import DataSourcePlugin


@dataclass
class SupplierInfo:
    """供应商信息"""
    supplier_id: str
    name: str
    contact_info: str
    api_endpoint: str
    api_key: str
    status: str
    performance_score: float
    lead_time_days: int


@dataclass
class PurchaseOrder:
    """采购订单"""
    order_id: str
    supplier_id: str
    material_code: str
    quantity: int
    unit_price: float
    total_amount: float
    order_date: datetime
    required_date: datetime
    status: str
    priority: str


@dataclass
class InventoryItem:
    """库存项目"""
    material_code: str
    current_stock: int
    reserved_stock: int
    available_stock: int
    safety_stock: int
    max_stock: int
    unit_cost: float
    last_updated: datetime


class LogisticsPlanningPlugin(DataSourcePlugin):
    """物流规划插件"""

    def __init__(self):
        super().__init__()
        self.name = "logistics_planning"
        self.description = "物流运输和仓储规划插件"
        self.version = "1.0.0"
        self.enabled = True

        # 物流数据库
        self.logistics_db_path = "logistics_data.db"
        self._init_logistics_database()

    def _init_logistics_database(self):
        """初始化物流数据库"""
        conn = sqlite3.connect(self.logistics_db_path)
        cursor = conn.cursor()

        # 运输路线表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transport_routes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                route_name TEXT NOT NULL,
                origin TEXT NOT NULL,
                destination TEXT NOT NULL,
                distance_km REAL,
                transport_mode TEXT,
                capacity_tons_per_day REAL,
                cost_per_ton REAL,
                lead_time_hours REAL,
                availability TEXT DEFAULT 'available',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 仓库信息表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS warehouses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                warehouse_code TEXT UNIQUE NOT NULL,
                warehouse_name TEXT NOT NULL,
                location TEXT,
                total_capacity_m3 REAL,
                available_capacity_m3 REAL,
                storage_cost_per_m3 REAL,
                handling_capacity_tons_per_day REAL,
                warehouse_type TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 配送计划表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS delivery_schedules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                delivery_id TEXT NOT NULL,
                route_id INTEGER,
                warehouse_id INTEGER,
                planned_date DATE,
                planned_quantity REAL,
                delivery_status TEXT DEFAULT 'planned',
                priority_level INTEGER DEFAULT 3,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (route_id) REFERENCES transport_routes (id),
                FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
            )
        ''')

        # 物流成本表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS logistics_costs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cost_type TEXT NOT NULL,
                cost_category TEXT,
                unit_cost REAL,
                currency TEXT DEFAULT 'CNY',
                effective_date DATE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 供应商表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id TEXT UNIQUE NOT NULL,
                supplier_name TEXT NOT NULL,
                contact_info TEXT,
                api_endpoint TEXT,
                api_key TEXT,
                status TEXT DEFAULT 'active',
                performance_score REAL DEFAULT 0.0,
                lead_time_days INTEGER DEFAULT 7,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 供应商物料表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS supplier_materials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id TEXT NOT NULL,
                material_code TEXT NOT NULL,
                unit_price REAL,
                min_order_quantity INTEGER DEFAULT 1,
                lead_time_days INTEGER DEFAULT 7,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (supplier_id)
            )
        ''')

        # 采购订单表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id TEXT UNIQUE NOT NULL,
                supplier_id TEXT NOT NULL,
                material_code TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_amount REAL NOT NULL,
                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                required_date DATE,
                delivery_date TIMESTAMP,
                status TEXT DEFAULT 'pending',
                priority TEXT DEFAULT 'normal',
                FOREIGN KEY (supplier_id) REFERENCES suppliers (supplier_id)
            )
        ''')

        # 库存项目表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                material_code TEXT UNIQUE NOT NULL,
                material_name TEXT,
                current_stock INTEGER DEFAULT 0,
                reserved_stock INTEGER DEFAULT 0,
                available_stock INTEGER DEFAULT 0,
                safety_stock INTEGER DEFAULT 0,
                max_stock INTEGER DEFAULT 1000,
                unit_cost REAL DEFAULT 0.0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()

        # 插入示例数据
        self._insert_sample_data(cursor)
        conn.commit()
        conn.close()

    def _insert_sample_data(self, cursor):
        """插入示例物流数据"""
        # 示例运输路线
        routes = [
            ("工厂-华东仓", "生产工厂", "华东配送中心", 350, "公路运输", 50, 120, 8, "available"),
            ("工厂-华南仓", "生产工厂", "华南配送中心", 800, "公路运输", 40, 180, 18, "available"),
            ("工厂-华北仓", "生产工厂", "华北配送中心", 600, "铁路运输", 80, 100, 24, "available"),
            ("华东-客户A", "华东配送中心", "客户A", 120, "公路运输", 20, 80, 4, "available"),
            ("华南-客户B", "华南配送中心", "客户B", 200, "公路运输", 25, 90, 6, "available")
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO transport_routes
            (route_name, origin, destination, distance_km, transport_mode,
             capacity_tons_per_day, cost_per_ton, lead_time_hours, availability)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', routes)

        # 示例仓库信息
        warehouses = [
            ("WH001", "华东配送中心", "上海", 5000, 3200, 15, 100, "配送中心", "active"),
            ("WH002", "华南配送中心", "广州", 4000, 2800, 18, 80, "配送中心", "active"),
            ("WH003", "华北配送中心", "北京", 6000, 4500, 12, 120, "配送中心", "active"),
            ("WH004", "原料仓库", "工厂内", 2000, 1500, 8, 200, "原料仓", "active"),
            ("WH005", "成品仓库", "工厂内", 3000, 2000, 10, 150, "成品仓", "active")
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO warehouses
            (warehouse_code, warehouse_name, location, total_capacity_m3,
             available_capacity_m3, storage_cost_per_m3, handling_capacity_tons_per_day,
             warehouse_type, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', warehouses)

        # 示例物流成本
        costs = [
            ("运输成本", "公路运输", 120, "CNY", "2024-01-01", "公路运输每吨公里成本"),
            ("运输成本", "铁路运输", 80, "CNY", "2024-01-01", "铁路运输每吨公里成本"),
            ("仓储成本", "标准仓储", 15, "CNY", "2024-01-01", "标准仓储每立方米每天成本"),
            ("装卸成本", "机械装卸", 25, "CNY", "2024-01-01", "机械装卸每吨成本"),
            ("包装成本", "标准包装", 8, "CNY", "2024-01-01", "标准包装每件成本")
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO logistics_costs
            (cost_type, cost_category, unit_cost, currency, effective_date, description)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', costs)

        # 示例供应商数据
        suppliers = [
            ("SUP001", "优质材料供应商", "021-12345678", "https://api.supplier1.com", "key123", "active", 4.5, 7),
            ("SUP002", "快速配送供应商", "021-87654321", "https://api.supplier2.com", "key456", "active", 4.3, 3),
            ("SUP003", "绿色环保供应商", "021-11223344", "https://api.supplier3.com", "key789", "active", 4.7, 10)
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO suppliers
            (supplier_id, supplier_name, contact_info, api_endpoint, api_key, status, performance_score, lead_time_days)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', suppliers)

        # 示例供应商物料数据
        supplier_materials = [
            ("SUP001", "MAT001", 25.0, 100, 7),
            ("SUP001", "MAT002", 18.5, 50, 7),
            ("SUP002", "MAT003", 32.0, 20, 3),
            ("SUP002", "MAT004", 15.8, 30, 3),
            ("SUP003", "MAT005", 28.5, 80, 10),
            ("SUP003", "MAT006", 22.0, 60, 10)
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO supplier_materials
            (supplier_id, material_code, unit_price, min_order_quantity, lead_time_days)
            VALUES (?, ?, ?, ?, ?)
        ''', supplier_materials)

        # 示例库存数据
        inventory_items = [
            ("MAT001", "钢材原料", 150, 20, 130, 50, 500, 25.0),
            ("MAT002", "铝材原料", 80, 10, 70, 30, 300, 18.5),
            ("MAT003", "电子元件", 25, 5, 20, 15, 100, 32.0),
            ("MAT004", "传感器", 40, 8, 32, 20, 150, 15.8),
            ("MAT005", "环保材料", 60, 12, 48, 25, 200, 28.5),
            ("MAT006", "可回收包装", 100, 15, 85, 40, 400, 22.0)
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO inventory_items
            (material_code, material_name, current_stock, reserved_stock, available_stock,
             safety_stock, max_stock, unit_cost)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', inventory_items)

    def get_data_context(self) -> Dict[str, Any]:
        """获取物流数据上下文"""
        conn = sqlite3.connect(self.logistics_db_path)
        cursor = conn.cursor()

        # 获取运输路线信息
        cursor.execute('''
            SELECT route_name, origin, destination, capacity_tons_per_day,
                   cost_per_ton, lead_time_hours, availability
            FROM transport_routes WHERE availability = 'available'
        ''')

        transport_routes = []
        for row in cursor.fetchall():
            transport_routes.append({
                "route_name": row[0],
                "origin": row[1],
                "destination": row[2],
                "capacity": row[3],
                "cost_per_ton": row[4],
                "lead_time": row[5],
                "status": row[6]
            })

        # 获取仓库信息
        cursor.execute('''
            SELECT warehouse_code, warehouse_name, location, total_capacity_m3,
                   available_capacity_m3, storage_cost_per_m3, handling_capacity_tons_per_day
            FROM warehouses WHERE status = 'active'
        ''')

        warehouses = []
        for row in cursor.fetchall():
            utilization = ((row[3] - row[4]) / row[3] * 100) if row[3] > 0 else 0
            warehouses.append({
                "code": row[0],
                "name": row[1],
                "location": row[2],
                "total_capacity": row[3],
                "available_capacity": row[4],
                "utilization_rate": utilization,
                "storage_cost": row[5],
                "handling_capacity": row[6]
            })

        # 获取配送计划
        cursor.execute('''
            SELECT COUNT(*) as total_deliveries,
                   SUM(CASE WHEN delivery_status = 'completed' THEN 1 ELSE 0 END) as completed,
                   SUM(CASE WHEN delivery_status = 'delayed' THEN 1 ELSE 0 END) as delayed
            FROM delivery_schedules
            WHERE planned_date >= date('now', '-30 days')
        ''')

        delivery_stats = cursor.fetchone()

        conn.close()

        return {
            "transport_routes": transport_routes,
            "warehouses": warehouses,
            "delivery_performance": {
                "total_deliveries": delivery_stats[0] if delivery_stats[0] else 0,
                "completed_deliveries": delivery_stats[1] if delivery_stats[1] else 0,
                "delayed_deliveries": delivery_stats[2] if delivery_stats[2] else 0,
                "on_time_rate": (delivery_stats[1] / delivery_stats[0] * 100) if delivery_stats[0] > 0 else 100
            },
            "capacity_summary": {
                "total_transport_capacity": sum(route["capacity"] for route in transport_routes),
                "total_warehouse_capacity": sum(wh["total_capacity"] for wh in warehouses),
                "available_warehouse_capacity": sum(wh["available_capacity"] for wh in warehouses)
            }
        }

    def get_constraints(self, data_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取物流约束条件"""
        constraints = []
        logistics_data = data_context.get("logistics_planning", {})

        # 运输能力约束
        transport_routes = logistics_data.get("transport_routes", [])
        for route in transport_routes:
            if route["capacity"] > 0:
                constraints.append({
                    "type": "transport_capacity",
                    "route": route["route_name"],
                    "max_capacity": route["capacity"],
                    "cost_per_ton": route["cost_per_ton"],
                    "lead_time": route["lead_time"],
                    "description": f"运输路线{route['route_name']}日运力限制{route['capacity']}吨"
                })

        # 仓储容量约束
        warehouses = logistics_data.get("warehouses", [])
        for warehouse in warehouses:
            if warehouse["available_capacity"] < warehouse["total_capacity"] * 0.1:  # 可用容量低于10%
                constraints.append({
                    "type": "warehouse_capacity",
                    "warehouse": warehouse["code"],
                    "available_capacity": warehouse["available_capacity"],
                    "urgency": "high",
                    "description": f"仓库{warehouse['code']}可用容量不足，仅剩{warehouse['available_capacity']:.1f}立方米"
                })

        # 配送时效约束
        delivery_performance = logistics_data.get("delivery_performance", {})
        if delivery_performance.get("on_time_rate", 100) < 90:
            constraints.append({
                "type": "delivery_performance",
                "on_time_rate": delivery_performance["on_time_rate"],
                "urgency": "medium",
                "description": f"配送准时率{delivery_performance['on_time_rate']:.1f}%，低于90%标准"
            })

        return constraints

    def get_recommendations(self, data_context: Dict[str, Any]) -> List[str]:
        """获取物流优化建议"""
        recommendations = []
        logistics_data = data_context.get("logistics_planning", {})

        # 分析运输优化机会
        transport_routes = logistics_data.get("transport_routes", [])
        high_cost_routes = [r for r in transport_routes if r["cost_per_ton"] > 150]
        if high_cost_routes:
            recommendations.append(f"发现{len(high_cost_routes)}条高成本运输路线，建议优化运输方式或寻找替代路线")

        # 分析仓储优化机会
        warehouses = logistics_data.get("warehouses", [])
        high_utilization_warehouses = [w for w in warehouses if w["utilization_rate"] > 85]
        if high_utilization_warehouses:
            recommendations.append(f"仓库{', '.join([w['code'] for w in high_utilization_warehouses])}利用率超过85%，建议扩容或优化库存周转")

        # 分析配送优化机会
        delivery_performance = logistics_data.get("delivery_performance", {})
        if delivery_performance.get("on_time_rate", 100) < 95:
            recommendations.append("配送准时率有待提升，建议优化配送路线规划和时间安排")

        # 成本优化建议
        capacity_summary = logistics_data.get("capacity_summary", {})
        if capacity_summary.get("total_transport_capacity", 0) > 0:
            utilization = (capacity_summary.get("total_transport_capacity", 0) -
                          capacity_summary.get("available_warehouse_capacity", 0)) / capacity_summary.get("total_transport_capacity", 1)
            if utilization < 0.7:
                recommendations.append("运输资源利用率偏低，建议整合运输计划，提高装载率")

        return recommendations

    def optimize_logistics_plan(self, production_plan: Dict[str, Any],
                               optimization_objectives: Dict[str, float]) -> Dict[str, Any]:
        """优化物流计划"""

        # 获取生产计划中的物流需求
        logistics_requirements = self._extract_logistics_requirements(production_plan)

        # 运输路线优化
        transport_optimization = self._optimize_transport_routes(
            logistics_requirements, optimization_objectives
        )

        # 仓储分配优化
        warehouse_optimization = self._optimize_warehouse_allocation(
            logistics_requirements, optimization_objectives
        )

        # 配送计划优化
        delivery_optimization = self._optimize_delivery_schedule(
            logistics_requirements, optimization_objectives
        )

        return {
            "transport_plan": transport_optimization,
            "warehouse_plan": warehouse_optimization,
            "delivery_plan": delivery_optimization,
            "total_logistics_cost": self._calculate_total_logistics_cost(
                transport_optimization, warehouse_optimization, delivery_optimization
            ),
            "performance_metrics": {
                "cost_efficiency": transport_optimization.get("cost_efficiency", 0),
                "delivery_reliability": delivery_optimization.get("reliability_score", 0),
                "resource_utilization": warehouse_optimization.get("utilization_score", 0)
            }
        }

    def _extract_logistics_requirements(self, production_plan: Dict[str, Any]) -> Dict[str, Any]:
        """从生产计划中提取物流需求"""
        # 模拟物流需求提取
        schedule = production_plan.get("plan_data", {}).get("schedule", [])

        total_volume = 0
        delivery_points = set()

        for task in schedule:
            # 假设每个订单有对应的配送需求
            total_volume += 10  # 模拟每个任务10立方米
            delivery_points.add(f"客户_{task.get('order_id', 'unknown')}")

        return {
            "total_volume": total_volume,
            "delivery_points": list(delivery_points),
            "time_windows": [
                {"start": "2024-01-15", "end": "2024-01-22", "volume": total_volume}
            ]
        }

    def _optimize_transport_routes(self, requirements: Dict[str, Any],
                                  objectives: Dict[str, float]) -> Dict[str, Any]:
        """优化运输路线"""
        return {
            "selected_routes": ["工厂-华东仓", "华东-客户A"],
            "total_distance": 470,
            "total_cost": 15600,
            "estimated_time": 12,
            "cost_efficiency": 85.2
        }

    def _optimize_warehouse_allocation(self, requirements: Dict[str, Any],
                                     objectives: Dict[str, float]) -> Dict[str, Any]:
        """优化仓储分配"""
        return {
            "warehouse_allocation": {
                "WH001": {"allocated_volume": 150, "utilization": 78.5},
                "WH005": {"allocated_volume": 100, "utilization": 65.2}
            },
            "storage_cost": 3750,
            "utilization_score": 71.8
        }

    def _optimize_delivery_schedule(self, requirements: Dict[str, Any],
                                   objectives: Dict[str, float]) -> Dict[str, Any]:
        """优化配送计划"""
        return {
            "delivery_schedule": [
                {"date": "2024-01-16", "route": "华东-客户A", "volume": 80},
                {"date": "2024-01-17", "route": "华东-客户A", "volume": 70}
            ],
            "on_time_probability": 94.5,
            "reliability_score": 92.1
        }

    def _calculate_total_logistics_cost(self, transport: Dict, warehouse: Dict, delivery: Dict) -> float:
        """计算总物流成本"""
        transport_cost = transport.get("total_cost", 0)
        storage_cost = warehouse.get("storage_cost", 0)
        delivery_cost = 2500  # 模拟配送成本

        return transport_cost + storage_cost + delivery_cost

    # ==================== 供应链协同功能 ====================

    async def sync_supplier_data(self, supplier_id: str) -> Dict[str, Any]:
        """同步供应商数据"""
        try:
            conn = sqlite3.connect(self.logistics_db_path)
            cursor = conn.cursor()

            # 获取供应商信息
            cursor.execute('''
                SELECT api_endpoint, api_key FROM suppliers WHERE supplier_id = ?
            ''', (supplier_id,))

            supplier_info = cursor.fetchone()
            if not supplier_info:
                return {"success": False, "error": "供应商不存在"}

            api_endpoint, api_key = supplier_info

            # 模拟API调用同步数据
            headers = {"Authorization": f"Bearer {api_key}"}

            # 这里应该是真实的API调用，现在用模拟数据
            sync_data = {
                "inventory_updates": [
                    {"material_code": "MAT001", "available_quantity": 500},
                    {"material_code": "MAT002", "available_quantity": 300}
                ],
                "price_updates": [
                    {"material_code": "MAT001", "new_price": 25.5},
                    {"material_code": "MAT002", "new_price": 18.8}
                ],
                "delivery_updates": [
                    {"order_id": "PO001", "status": "shipped", "tracking_number": "TN123456"}
                ]
            }

            # 更新本地数据
            for update in sync_data["price_updates"]:
                cursor.execute('''
                    UPDATE supplier_materials SET unit_price = ?, last_updated = ?
                    WHERE supplier_id = ? AND material_code = ?
                ''', (update["new_price"], datetime.now(), supplier_id, update["material_code"]))

            conn.commit()
            conn.close()

            return {
                "success": True,
                "synced_items": len(sync_data["inventory_updates"]) + len(sync_data["price_updates"]),
                "sync_time": datetime.now().isoformat()
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def create_purchase_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建采购订单"""
        try:
            conn = sqlite3.connect(self.logistics_db_path)
            cursor = conn.cursor()

            order_id = f"PO{datetime.now().strftime('%Y%m%d%H%M%S')}"

            cursor.execute('''
                INSERT INTO purchase_orders
                (order_id, supplier_id, material_code, quantity, unit_price,
                 total_amount, order_date, required_date, status, priority)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                order_id,
                order_data["supplier_id"],
                order_data["material_code"],
                order_data["quantity"],
                order_data["unit_price"],
                order_data["quantity"] * order_data["unit_price"],
                datetime.now(),
                order_data["required_date"],
                "pending",
                order_data.get("priority", "normal")
            ))

            conn.commit()
            conn.close()

            return {
                "success": True,
                "order_id": order_id,
                "message": "采购订单创建成功"
            }

        except Exception as e:
            return {"success": False, "error": str(e)}

    def generate_reorder_suggestions(self) -> List[Dict[str, Any]]:
        """生成补货建议"""
        try:
            conn = sqlite3.connect(self.logistics_db_path)
            cursor = conn.cursor()

            # 查找需要补货的物料
            cursor.execute('''
                SELECT i.material_code, i.current_stock, i.safety_stock, i.max_stock, i.unit_cost
                FROM inventory_items i
                WHERE i.current_stock <= i.safety_stock
            ''')

            suggestions = []
            for row in cursor.fetchall():
                material_code, current_stock, safety_stock, max_stock, unit_cost = row

                # 查找对应的供应商信息
                cursor.execute('''
                    SELECT supplier_id, unit_price, lead_time_days
                    FROM supplier_materials
                    WHERE material_code = ?
                    ORDER BY unit_price ASC
                    LIMIT 1
                ''', (material_code,))

                supplier_info = cursor.fetchone()
                if supplier_info:
                    supplier_id, supplier_price, lead_time = supplier_info
                    # 使用供应商价格，如果没有则使用库存成本
                    actual_price = supplier_price if supplier_price else unit_cost
                else:
                    # 如果没有找到供应商，使用默认值
                    supplier_id = "SUP001"
                    actual_price = unit_cost
                    lead_time = 7

                # 计算建议采购量
                suggested_quantity = max_stock - current_stock
                estimated_cost = suggested_quantity * actual_price

                suggestions.append({
                    "material_code": material_code,
                    "current_stock": current_stock,
                    "safety_stock": safety_stock,
                    "suggested_quantity": suggested_quantity,
                    "estimated_cost": estimated_cost,
                    "supplier_id": supplier_id,
                    "lead_time_days": lead_time,
                    "urgency": "high" if current_stock < safety_stock * 0.5 else "medium"
                })

            conn.close()
            return suggestions

        except Exception as e:
            print(f"生成补货建议错误: {e}")
            return []

    def update_inventory_from_delivery(self, delivery_data: Dict[str, Any]) -> Dict[str, Any]:
        """根据交付更新库存"""
        try:
            conn = sqlite3.connect(self.logistics_db_path)
            cursor = conn.cursor()

            # 更新库存
            cursor.execute('''
                UPDATE inventory_items
                SET current_stock = current_stock + ?, last_updated = ?
                WHERE material_code = ?
            ''', (
                delivery_data["delivered_quantity"],
                datetime.now(),
                delivery_data["material_code"]
            ))

            # 更新订单状态
            cursor.execute('''
                UPDATE purchase_orders
                SET status = 'delivered', delivery_date = ?
                WHERE order_id = ?
            ''', (datetime.now(), delivery_data["order_id"]))

            conn.commit()
            conn.close()

            return {"success": True, "message": "库存更新成功"}

        except Exception as e:
            return {"success": False, "error": str(e)}


class SupplyChainCollaborationService:
    """供应链协同服务"""

    def __init__(self):
        self.logistics_plugin = LogisticsPlanningPlugin()
        self.collaboration_config = {
            "sync_interval_minutes": 15,
            "auto_reorder_enabled": True,
            "reorder_threshold_ratio": 0.2,
            "collaboration_mode": "deep",
            "data_sharing_level": "operational"
        }

    async def start_collaboration_monitoring(self):
        """启动协同监控"""
        while True:
            try:
                # 定期同步供应商数据
                await self._sync_all_suppliers()

                # 检查补货需求
                if self.collaboration_config["auto_reorder_enabled"]:
                    await self._auto_reorder_check()

                # 更新协同指标
                await self._update_collaboration_metrics()

                # 等待下次同步
                await asyncio.sleep(self.collaboration_config["sync_interval_minutes"] * 60)

            except Exception as e:
                print(f"协同监控错误: {e}")
                await asyncio.sleep(300)  # 出错时等待5分钟

    async def _sync_all_suppliers(self):
        """同步所有供应商数据"""
        suppliers = ["SUP001", "SUP002", "SUP003"]  # 从数据库获取

        for supplier_id in suppliers:
            result = await self.logistics_plugin.sync_supplier_data(supplier_id)
            if result["success"]:
                print(f"供应商 {supplier_id} 数据同步成功")
            else:
                print(f"供应商 {supplier_id} 数据同步失败: {result['error']}")

    async def _auto_reorder_check(self):
        """自动补货检查"""
        suggestions = self.logistics_plugin.generate_reorder_suggestions()

        for suggestion in suggestions:
            if suggestion["urgency"] == "high":
                # 自动创建紧急采购订单
                order_data = {
                    "supplier_id": suggestion["supplier_id"],
                    "material_code": suggestion["material_code"],
                    "quantity": suggestion["suggested_quantity"],
                    "unit_price": suggestion["estimated_cost"] / suggestion["suggested_quantity"],
                    "required_date": datetime.now() + timedelta(days=suggestion["lead_time_days"]),
                    "priority": "urgent"
                }

                result = self.logistics_plugin.create_purchase_order(order_data)
                if result["success"]:
                    print(f"自动创建紧急采购订单: {result['order_id']}")

    async def _update_collaboration_metrics(self):
        """更新协同指标"""
        # 计算协同效率、成本节约等指标
        pass


# 全局实例
logistics_plugin = LogisticsPlanningPlugin()
supply_chain_service = SupplyChainCollaborationService()
