"""
Smart Planning 决策支持增强服务
实现方案对比、敏感性分析、风险评估和ROI计算
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import json
from pathlib import Path

@dataclass
class DecisionScenario:
    """决策方案"""
    id: str
    name: str
    description: str
    parameters: Dict[str, Any]
    expected_outcomes: Dict[str, float]
    costs: Dict[str, float]
    benefits: Dict[str, float]
    risks: Dict[str, float]
    implementation_time: int  # days
    created_at: str = None

@dataclass
class SensitivityAnalysis:
    """敏感性分析结果"""
    parameter_name: str
    base_value: float
    sensitivity_range: Tuple[float, float]
    impact_on_outcome: Dict[str, List[float]]
    elasticity: float
    risk_level: str

@dataclass
class RiskAssessment:
    """风险评估结果"""
    scenario_id: str
    risk_factors: List[Dict[str, Any]]
    overall_risk_score: float
    risk_level: str
    mitigation_strategies: List[str]
    probability_distribution: Dict[str, float]

@dataclass
class ROICalculation:
    """ROI计算结果"""
    scenario_id: str
    initial_investment: float
    annual_benefits: List[float]
    annual_costs: List[float]
    net_present_value: float
    roi_percentage: float
    payback_period: float
    break_even_point: float

class DecisionSupportService:
    """决策支持增强服务"""

    def __init__(self, storage_dir: str = "data/decisions"):
        self.logger = logging.getLogger(__name__)
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)

        # 存储
        self.scenarios = {}
        self.analysis_results = {}

        # 配置参数
        self.discount_rate = 0.08  # 8% 折现率
        self.risk_free_rate = 0.03  # 3% 无风险利率
        self.market_volatility = 0.15  # 15% 市场波动率

        # 风险因子权重
        self.risk_weights = {
            "market_risk": 0.3,
            "operational_risk": 0.25,
            "financial_risk": 0.2,
            "technical_risk": 0.15,
            "regulatory_risk": 0.1
        }

        # 加载历史数据
        self._load_historical_data()

    def _load_historical_data(self):
        """加载历史数据"""
        try:
            scenarios_file = self.storage_dir / "scenarios.json"
            if scenarios_file.exists():
                with open(scenarios_file, 'r', encoding='utf-8') as f:
                    scenarios_data = json.load(f)
                    self.scenarios = {
                        k: DecisionScenario(**v) for k, v in scenarios_data.items()
                    }
        except Exception as e:
            self.logger.error(f"加载历史数据失败: {str(e)}")

    def create_scenario(self, scenario_data: Dict[str, Any]) -> DecisionScenario:
        """创建决策方案"""
        scenario_id = scenario_data.get("id") or f"scenario_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        scenario = DecisionScenario(
            id=scenario_id,
            name=scenario_data["name"],
            description=scenario_data.get("description", ""),
            parameters=scenario_data.get("parameters", {}),
            expected_outcomes=scenario_data.get("expected_outcomes", {}),
            costs=scenario_data.get("costs", {}),
            benefits=scenario_data.get("benefits", {}),
            risks=scenario_data.get("risks", {}),
            implementation_time=scenario_data.get("implementation_time", 30),
            created_at=datetime.now().isoformat()
        )

        self.scenarios[scenario_id] = scenario
        self._save_scenarios()

        self.logger.info(f"创建决策方案: {scenario_id}")
        return scenario

    def compare_scenarios(self, scenario_ids: List[str],
                         comparison_criteria: List[str] = None) -> Dict[str, Any]:
        """方案对比分析"""
        if not scenario_ids or len(scenario_ids) < 2:
            raise ValueError("至少需要2个方案进行对比")

        scenarios = [self.scenarios.get(sid) for sid in scenario_ids]
        scenarios = [s for s in scenarios if s is not None]

        if len(scenarios) < 2:
            raise ValueError("找不到足够的有效方案")

        # 默认对比标准
        if comparison_criteria is None:
            comparison_criteria = [
                "roi", "npv", "payback_period", "risk_score",
                "implementation_time", "total_cost", "total_benefit"
            ]

        comparison_result = {
            "scenarios": [asdict(s) for s in scenarios],
            "comparison_matrix": {},
            "rankings": {},
            "recommendations": []
        }

        # 计算各项指标
        for criterion in comparison_criteria:
            values = []
            for scenario in scenarios:
                if criterion == "roi":
                    roi_calc = self.calculate_roi(scenario.id)
                    values.append(roi_calc.roi_percentage if roi_calc else 0)
                elif criterion == "npv":
                    roi_calc = self.calculate_roi(scenario.id)
                    values.append(roi_calc.net_present_value if roi_calc else 0)
                elif criterion == "payback_period":
                    roi_calc = self.calculate_roi(scenario.id)
                    values.append(roi_calc.payback_period if roi_calc else float('inf'))
                elif criterion == "risk_score":
                    risk_assessment = self.assess_risk(scenario.id)
                    values.append(risk_assessment.overall_risk_score if risk_assessment else 0.5)
                elif criterion == "implementation_time":
                    values.append(scenario.implementation_time)
                elif criterion == "total_cost":
                    values.append(sum(scenario.costs.values()))
                elif criterion == "total_benefit":
                    values.append(sum(scenario.benefits.values()))
                else:
                    values.append(0)

            comparison_result["comparison_matrix"][criterion] = {
                "values": values,
                "best_scenario": scenario_ids[values.index(max(values) if criterion in ["roi", "npv", "total_benefit"] else min(values))],
                "normalized_scores": self._normalize_scores(values, criterion)
            }

        # 计算综合排名
        comparison_result["rankings"] = self._calculate_overall_rankings(
            scenarios, comparison_result["comparison_matrix"]
        )

        # 生成建议
        comparison_result["recommendations"] = self._generate_comparison_recommendations(
            scenarios, comparison_result["rankings"]
        )

        return comparison_result

    def sensitivity_analysis(self, scenario_id: str,
                           parameters: List[str] = None,
                           variation_range: float = 0.2) -> List[SensitivityAnalysis]:
        """敏感性分析"""
        scenario = self.scenarios.get(scenario_id)
        if not scenario:
            raise ValueError(f"方案不存在: {scenario_id}")

        if parameters is None:
            parameters = list(scenario.parameters.keys())

        sensitivity_results = []

        for param in parameters:
            if param not in scenario.parameters:
                continue

            base_value = scenario.parameters[param]
            if not isinstance(base_value, (int, float)):
                continue

            # 计算变化范围
            variation = abs(base_value * variation_range)
            min_value = base_value - variation
            max_value = base_value + variation

            # 测试不同参数值对结果的影响
            test_values = np.linspace(min_value, max_value, 11)
            impact_results = {}

            for outcome_key in scenario.expected_outcomes.keys():
                outcome_values = []

                for test_value in test_values:
                    # 创建临时方案
                    temp_params = scenario.parameters.copy()
                    temp_params[param] = test_value

                    # 计算影响（简化模型）
                    impact = self._calculate_parameter_impact(
                        param, test_value, base_value, outcome_key, scenario
                    )
                    outcome_values.append(impact)

                impact_results[outcome_key] = outcome_values

            # 计算弹性系数
            elasticity = self._calculate_elasticity(
                test_values, impact_results.get(list(scenario.expected_outcomes.keys())[0], [])
            )

            # 确定风险等级
            risk_level = "高" if abs(elasticity) > 2 else "中" if abs(elasticity) > 1 else "低"

            sensitivity_results.append(SensitivityAnalysis(
                parameter_name=param,
                base_value=base_value,
                sensitivity_range=(min_value, max_value),
                impact_on_outcome=impact_results,
                elasticity=elasticity,
                risk_level=risk_level
            ))

        return sensitivity_results

    def assess_risk(self, scenario_id: str) -> RiskAssessment:
        """风险评估"""
        scenario = self.scenarios.get(scenario_id)
        if not scenario:
            raise ValueError(f"方案不存在: {scenario_id}")

        risk_factors = []

        # 市场风险
        market_risk = self._assess_market_risk(scenario)
        risk_factors.append({
            "type": "market_risk",
            "name": "市场风险",
            "score": market_risk,
            "description": "市场需求变化、竞争加剧等风险"
        })

        # 运营风险
        operational_risk = self._assess_operational_risk(scenario)
        risk_factors.append({
            "type": "operational_risk",
            "name": "运营风险",
            "score": operational_risk,
            "description": "生产效率、质量控制等运营风险"
        })

        # 财务风险
        financial_risk = self._assess_financial_risk(scenario)
        risk_factors.append({
            "type": "financial_risk",
            "name": "财务风险",
            "score": financial_risk,
            "description": "资金流动性、成本控制等财务风险"
        })

        # 技术风险
        technical_risk = self._assess_technical_risk(scenario)
        risk_factors.append({
            "type": "technical_risk",
            "name": "技术风险",
            "score": technical_risk,
            "description": "技术实施、系统集成等技术风险"
        })

        # 监管风险
        regulatory_risk = self._assess_regulatory_risk(scenario)
        risk_factors.append({
            "type": "regulatory_risk",
            "name": "监管风险",
            "score": regulatory_risk,
            "description": "政策变化、合规要求等监管风险"
        })

        # 计算综合风险分数
        overall_risk_score = sum(
            factor["score"] * self.risk_weights.get(factor["type"], 0.2)
            for factor in risk_factors
        )

        # 确定风险等级
        if overall_risk_score >= 0.7:
            risk_level = "高风险"
        elif overall_risk_score >= 0.4:
            risk_level = "中风险"
        else:
            risk_level = "低风险"

        # 生成缓解策略
        mitigation_strategies = self._generate_mitigation_strategies(risk_factors)

        # 概率分布（简化模型）
        probability_distribution = {
            "成功": max(0.1, 1 - overall_risk_score),
            "部分成功": min(0.8, overall_risk_score * 0.8),
            "失败": min(0.1, overall_risk_score * 0.2)
        }

        return RiskAssessment(
            scenario_id=scenario_id,
            risk_factors=risk_factors,
            overall_risk_score=overall_risk_score,
            risk_level=risk_level,
            mitigation_strategies=mitigation_strategies,
            probability_distribution=probability_distribution
        )

    def calculate_roi(self, scenario_id: str, analysis_period: int = 5) -> ROICalculation:
        """ROI计算"""
        scenario = self.scenarios.get(scenario_id)
        if not scenario:
            raise ValueError(f"方案不存在: {scenario_id}")

        # 初始投资
        initial_investment = sum(scenario.costs.values())

        # 年度收益和成本
        annual_benefits = []
        annual_costs = []

        for year in range(analysis_period):
            # 计算年度收益（考虑增长率）
            growth_rate = 0.05  # 假设5%年增长率
            yearly_benefit = sum(scenario.benefits.values()) * (1 + growth_rate) ** year
            annual_benefits.append(yearly_benefit)

            # 计算年度运营成本
            inflation_rate = 0.03  # 假设3%通胀率
            yearly_cost = sum(scenario.costs.values()) * 0.2 * (1 + inflation_rate) ** year  # 20%的初始投资作为年运营成本
            annual_costs.append(yearly_cost)

        # 计算净现值 (NPV)
        npv = -initial_investment
        for year in range(analysis_period):
            net_cash_flow = annual_benefits[year] - annual_costs[year]
            npv += net_cash_flow / ((1 + self.discount_rate) ** (year + 1))

        # 计算ROI百分比
        total_benefits = sum(annual_benefits)
        total_costs = initial_investment + sum(annual_costs)
        roi_percentage = ((total_benefits - total_costs) / total_costs) * 100 if total_costs > 0 else 0

        # 计算回收期
        payback_period = self._calculate_payback_period(
            initial_investment, annual_benefits, annual_costs
        )

        # 计算盈亏平衡点
        break_even_point = self._calculate_break_even_point(
            initial_investment, annual_benefits, annual_costs
        )

        return ROICalculation(
            scenario_id=scenario_id,
            initial_investment=initial_investment,
            annual_benefits=annual_benefits,
            annual_costs=annual_costs,
            net_present_value=npv,
            roi_percentage=roi_percentage,
            payback_period=payback_period,
            break_even_point=break_even_point
        )

    def _normalize_scores(self, values: List[float], criterion: str) -> List[float]:
        """标准化分数"""
        if not values or all(v == values[0] for v in values):
            return [0.5] * len(values)

        min_val, max_val = min(values), max(values)

        # 对于"越大越好"的指标
        if criterion in ["roi", "npv", "total_benefit"]:
            return [(v - min_val) / (max_val - min_val) for v in values]
        # 对于"越小越好"的指标
        else:
            return [(max_val - v) / (max_val - min_val) for v in values]

    def _calculate_overall_rankings(self, scenarios: List[DecisionScenario],
                                  comparison_matrix: Dict[str, Any]) -> Dict[str, Any]:
        """计算综合排名"""
        # 权重设置
        weights = {
            "roi": 0.25,
            "npv": 0.2,
            "risk_score": 0.2,
            "payback_period": 0.15,
            "implementation_time": 0.1,
            "total_cost": 0.05,
            "total_benefit": 0.05
        }

        scenario_scores = {}

        for i, scenario in enumerate(scenarios):
            total_score = 0
            for criterion, weight in weights.items():
                if criterion in comparison_matrix:
                    normalized_scores = comparison_matrix[criterion]["normalized_scores"]
                    if i < len(normalized_scores):
                        total_score += normalized_scores[i] * weight

            scenario_scores[scenario.id] = {
                "scenario_name": scenario.name,
                "total_score": total_score,
                "rank": 0  # 将在下面计算
            }

        # 计算排名
        sorted_scenarios = sorted(scenario_scores.items(), key=lambda x: x[1]["total_score"], reverse=True)
        for rank, (scenario_id, score_data) in enumerate(sorted_scenarios, 1):
            scenario_scores[scenario_id]["rank"] = rank

        return scenario_scores

    def _generate_comparison_recommendations(self, scenarios: List[DecisionScenario],
                                           rankings: Dict[str, Any]) -> List[str]:
        """生成对比建议"""
        recommendations = []

        # 获取排名第一的方案
        best_scenario_id = min(rankings.keys(), key=lambda x: rankings[x]["rank"])
        best_scenario = next(s for s in scenarios if s.id == best_scenario_id)

        recommendations.append(f"推荐方案：{best_scenario.name}")
        recommendations.append(f"该方案在综合评估中得分最高，综合得分：{rankings[best_scenario_id]['total_score']:.2f}")

        # 分析优势
        roi_calc = self.calculate_roi(best_scenario_id)
        if roi_calc.roi_percentage > 20:
            recommendations.append(f"该方案具有较高的投资回报率（{roi_calc.roi_percentage:.1f}%）")

        if roi_calc.payback_period < 2:
            recommendations.append(f"该方案回收期较短（{roi_calc.payback_period:.1f}年）")

        # 风险提醒
        risk_assessment = self.assess_risk(best_scenario_id)
        if risk_assessment.overall_risk_score > 0.6:
            recommendations.append(f"注意：该方案风险等级为{risk_assessment.risk_level}，建议制定详细的风险缓解计划")

        return recommendations

    def _calculate_parameter_impact(self, param_name: str, test_value: float,
                                  base_value: float, outcome_key: str,
                                  scenario: DecisionScenario) -> float:
        """计算参数变化对结果的影响"""
        # 简化的影响模型
        change_ratio = (test_value - base_value) / base_value if base_value != 0 else 0
        base_outcome = scenario.expected_outcomes.get(outcome_key, 0)

        # 根据参数类型设置影响系数
        impact_coefficients = {
            "production_capacity": 0.8,
            "efficiency": 0.6,
            "cost_reduction": -0.5,  # 成本降低对收益是正向影响
            "quality_improvement": 0.4,
            "automation_level": 0.3
        }

        coefficient = impact_coefficients.get(param_name, 0.5)
        impact = base_outcome * (1 + change_ratio * coefficient)

        return impact

    def _calculate_elasticity(self, parameter_values: List[float],
                            outcome_values: List[float]) -> float:
        """计算弹性系数"""
        if len(parameter_values) < 2 or len(outcome_values) < 2:
            return 0

        # 计算平均值
        avg_param = np.mean(parameter_values)
        avg_outcome = np.mean(outcome_values)

        if avg_param == 0 or avg_outcome == 0:
            return 0

        # 计算变化率
        param_change = (parameter_values[-1] - parameter_values[0]) / avg_param
        outcome_change = (outcome_values[-1] - outcome_values[0]) / avg_outcome

        # 弹性系数 = 结果变化率 / 参数变化率
        elasticity = outcome_change / param_change if param_change != 0 else 0

        return elasticity

    def _assess_market_risk(self, scenario: DecisionScenario) -> float:
        """评估市场风险"""
        # 基于方案参数评估市场风险
        risk_score = 0.3  # 基础风险

        # 市场相关参数
        if "market_share" in scenario.parameters:
            market_share = scenario.parameters["market_share"]
            if market_share > 0.3:  # 市场份额过高风险增加
                risk_score += 0.2

        if "competition_level" in scenario.parameters:
            competition = scenario.parameters["competition_level"]
            risk_score += competition * 0.3

        return min(1.0, risk_score)

    def _assess_operational_risk(self, scenario: DecisionScenario) -> float:
        """评估运营风险"""
        risk_score = 0.2  # 基础风险

        # 运营复杂度
        if "automation_level" in scenario.parameters:
            automation = scenario.parameters["automation_level"]
            risk_score += (1 - automation) * 0.3  # 自动化程度低风险高

        if "implementation_time" in scenario.__dict__:
            if scenario.implementation_time > 180:  # 实施时间超过6个月
                risk_score += 0.2

        return min(1.0, risk_score)

    def _assess_financial_risk(self, scenario: DecisionScenario) -> float:
        """评估财务风险"""
        risk_score = 0.25  # 基础风险

        # 投资规模
        total_cost = sum(scenario.costs.values())
        if total_cost > 1000000:  # 投资超过100万
            risk_score += 0.3

        # 收益确定性
        total_benefit = sum(scenario.benefits.values())
        if total_cost > 0:
            benefit_cost_ratio = total_benefit / total_cost
            if benefit_cost_ratio < 1.5:  # 收益成本比低于1.5
                risk_score += 0.2

        return min(1.0, risk_score)

    def _assess_technical_risk(self, scenario: DecisionScenario) -> float:
        """评估技术风险"""
        risk_score = 0.2  # 基础风险

        # 技术复杂度
        if "technology_maturity" in scenario.parameters:
            maturity = scenario.parameters["technology_maturity"]
            risk_score += (1 - maturity) * 0.4

        if "integration_complexity" in scenario.parameters:
            complexity = scenario.parameters["integration_complexity"]
            risk_score += complexity * 0.3

        return min(1.0, risk_score)

    def _assess_regulatory_risk(self, scenario: DecisionScenario) -> float:
        """评估监管风险"""
        risk_score = 0.15  # 基础风险

        # 合规要求
        if "regulatory_compliance" in scenario.parameters:
            compliance = scenario.parameters["regulatory_compliance"]
            risk_score += (1 - compliance) * 0.3

        # 环保要求
        if "environmental_impact" in scenario.parameters:
            impact = scenario.parameters["environmental_impact"]
            risk_score += impact * 0.2

        return min(1.0, risk_score)

    def _generate_mitigation_strategies(self, risk_factors: List[Dict[str, Any]]) -> List[str]:
        """生成风险缓解策略"""
        strategies = []

        for factor in risk_factors:
            risk_type = factor["type"]
            score = factor["score"]

            if score > 0.6:  # 高风险需要缓解策略
                if risk_type == "market_risk":
                    strategies.extend([
                        "进行详细的市场调研和竞争分析",
                        "制定灵活的市场策略和备选方案",
                        "建立客户关系管理系统"
                    ])
                elif risk_type == "operational_risk":
                    strategies.extend([
                        "制定详细的实施计划和里程碑",
                        "建立质量控制和监督机制",
                        "培训员工和建立标准操作程序"
                    ])
                elif risk_type == "financial_risk":
                    strategies.extend([
                        "建立财务监控和预警系统",
                        "制定资金流动性管理计划",
                        "考虑分阶段投资降低风险"
                    ])
                elif risk_type == "technical_risk":
                    strategies.extend([
                        "进行技术可行性验证和测试",
                        "建立技术支持和维护团队",
                        "制定技术备选方案"
                    ])
                elif risk_type == "regulatory_risk":
                    strategies.extend([
                        "密切关注政策法规变化",
                        "建立合规管理体系",
                        "与监管部门保持沟通"
                    ])

        # 去重并限制数量
        unique_strategies = list(set(strategies))
        return unique_strategies[:8]  # 最多返回8个策略

    def _calculate_payback_period(self, initial_investment: float,
                                annual_benefits: List[float],
                                annual_costs: List[float]) -> float:
        """计算回收期"""
        cumulative_cash_flow = -initial_investment

        for year, (benefit, cost) in enumerate(zip(annual_benefits, annual_costs)):
            net_cash_flow = benefit - cost
            cumulative_cash_flow += net_cash_flow

            if cumulative_cash_flow >= 0:
                # 线性插值计算精确回收期
                if year == 0:
                    return year + 1
                else:
                    previous_cumulative = cumulative_cash_flow - net_cash_flow
                    fraction = abs(previous_cumulative) / net_cash_flow
                    return year + fraction

        return float('inf')  # 无法回收

    def _calculate_break_even_point(self, initial_investment: float,
                                  annual_benefits: List[float],
                                  annual_costs: List[float]) -> float:
        """计算盈亏平衡点"""
        if not annual_benefits or not annual_costs:
            return float('inf')

        # 简化计算：假设年度收益和成本稳定
        avg_annual_benefit = np.mean(annual_benefits)
        avg_annual_cost = np.mean(annual_costs)

        net_annual_cash_flow = avg_annual_benefit - avg_annual_cost

        if net_annual_cash_flow <= 0:
            return float('inf')  # 无法盈亏平衡

        break_even_years = initial_investment / net_annual_cash_flow
        return break_even_years

    def _save_scenarios(self):
        """保存方案数据"""
        try:
            scenarios_file = self.storage_dir / "scenarios.json"
            scenarios_data = {k: asdict(v) for k, v in self.scenarios.items()}

            with open(scenarios_file, 'w', encoding='utf-8') as f:
                json.dump(scenarios_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"保存方案数据失败: {str(e)}")

    def get_scenarios(self) -> List[DecisionScenario]:
        """获取所有方案"""
        return list(self.scenarios.values())

    def get_scenario(self, scenario_id: str) -> Optional[DecisionScenario]:
        """获取特定方案"""
        return self.scenarios.get(scenario_id)

    def delete_scenario(self, scenario_id: str) -> bool:
        """删除方案"""
        if scenario_id in self.scenarios:
            del self.scenarios[scenario_id]
            self._save_scenarios()
            self.logger.info(f"删除方案: {scenario_id}")
            return True
        return False

    def generate_decision_report(self, scenario_ids: List[str]) -> Dict[str, Any]:
        """生成决策分析报告"""
        if not scenario_ids:
            raise ValueError("需要至少一个方案ID")

        report = {
            "title": "决策分析报告",
            "generated_at": datetime.now().isoformat(),
            "scenarios": [],
            "comparison": None,
            "recommendations": []
        }

        # 收集方案信息
        for scenario_id in scenario_ids:
            scenario = self.scenarios.get(scenario_id)
            if scenario:
                scenario_info = {
                    "scenario": asdict(scenario),
                    "roi_analysis": asdict(self.calculate_roi(scenario_id)),
                    "risk_assessment": asdict(self.assess_risk(scenario_id)),
                    "sensitivity_analysis": [asdict(sa) for sa in self.sensitivity_analysis(scenario_id)]
                }
                report["scenarios"].append(scenario_info)

        # 如果有多个方案，进行对比分析
        if len(scenario_ids) > 1:
            report["comparison"] = self.compare_scenarios(scenario_ids)
            report["recommendations"] = report["comparison"]["recommendations"]
        elif len(report["scenarios"]) == 1:
            # 单方案分析建议
            scenario_info = report["scenarios"][0]
            roi = scenario_info["roi_analysis"]
            risk = scenario_info["risk_assessment"]

            recommendations = []
            if roi["roi_percentage"] > 15:
                recommendations.append("该方案具有良好的投资回报率")
            if risk["overall_risk_score"] < 0.4:
                recommendations.append("该方案风险较低，建议实施")
            elif risk["overall_risk_score"] > 0.7:
                recommendations.append("该方案风险较高，需要制定详细的风险管控措施")

            report["recommendations"] = recommendations

        return report

    def update_configuration(self, config: Dict[str, Any]):
        """更新配置参数"""
        if "discount_rate" in config:
            self.discount_rate = config["discount_rate"]
        if "risk_free_rate" in config:
            self.risk_free_rate = config["risk_free_rate"]
        if "market_volatility" in config:
            self.market_volatility = config["market_volatility"]
        if "risk_weights" in config:
            self.risk_weights.update(config["risk_weights"])

        self.logger.info("决策支持配置已更新")

    def get_configuration(self) -> Dict[str, Any]:
        """获取当前配置"""
        return {
            "discount_rate": self.discount_rate,
            "risk_free_rate": self.risk_free_rate,
            "market_volatility": self.market_volatility,
            "risk_weights": self.risk_weights
        }

    def export_analysis(self, scenario_ids: List[str], format: str = "json") -> str:
        """导出分析结果"""
        report = self.generate_decision_report(scenario_ids)

        if format == "json":
            return json.dumps(report, ensure_ascii=False, indent=2)
        elif format == "csv":
            # 简化的CSV导出
            csv_content = "方案名称,ROI(%),NPV,回收期(年),风险等级,风险分数\n"

            for scenario_info in report["scenarios"]:
                scenario = scenario_info["scenario"]
                roi = scenario_info["roi_analysis"]
                risk = scenario_info["risk_assessment"]

                csv_content += f"{scenario['name']},{roi['roi_percentage']:.2f},{roi['net_present_value']:.2f},"
                csv_content += f"{roi['payback_period']:.2f},{risk['risk_level']},{risk['overall_risk_score']:.3f}\n"

            return csv_content
        else:
            return str(report)


# 创建全局实例
decision_support_service = DecisionSupportService()