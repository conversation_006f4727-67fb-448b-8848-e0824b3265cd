"""
能耗优化页面 - 绿色制造与可持续发展管理
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加服务路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

# 页面配置
st.set_page_config(
    page_title="能耗优化",
    page_icon="⚡",
    layout="wide"
)

st.title("⚡ 能耗优化与绿色制造")
st.markdown("### 可持续发展与环保制造管理平台")

# 导入服务
try:
    from green_manufacturing_service import green_manufacturing_service
    # 注意：后端能耗优化服务是可选的，如果导入失败不影响页面功能
    energy_optimization_service = None
    try:
        from backend.app.services.energy_optimization_service import energy_optimization_service
    except ImportError:
        pass  # 后端服务可选
except ImportError as e:
    st.error(f"⚠️ 无法导入绿色制造服务: {e}")
    st.stop()

# 模拟数据生成函数
@st.cache_data
def generate_energy_consumption_data():
    """生成能耗数据"""
    dates = pd.date_range(start=datetime.now()-timedelta(days=30), end=datetime.now(), freq='H')

    data = []
    for date in dates:
        hour = date.hour
        # 模拟工作时间和非工作时间的能耗差异
        base_consumption = 120 if 8 <= hour <= 18 else 80
        consumption = base_consumption + np.random.normal(0, 10)

        data.append({
            "时间": date,
            "总能耗": max(0, consumption),
            "生产能耗": max(0, consumption * 0.7),
            "辅助能耗": max(0, consumption * 0.2),
            "照明能耗": max(0, consumption * 0.1),
            "电费": consumption * 0.8,  # 假设电价0.8元/kWh
            "碳排放": consumption * 0.5968  # 中国电网碳排放因子
        })

    return pd.DataFrame(data)

@st.cache_data
def generate_equipment_energy_data():
    """生成设备能耗数据"""
    equipment_list = ["L01", "L02", "L03", "L04", "Tank01", "Tank02", "Tank03"]

    data = []
    for equipment in equipment_list:
        base_power = 50 if equipment.startswith("L") else 30
        current_power = base_power + np.random.normal(0, 5)
        efficiency = np.random.uniform(0.75, 0.95)

        data.append({
            "设备ID": equipment,
            "当前功率": max(0, current_power),
            "额定功率": base_power + 10,
            "能效比": efficiency,
            "运行状态": np.random.choice(["运行", "待机", "停机"], p=[0.7, 0.2, 0.1]),
            "日能耗": current_power * 24,
            "月能耗": current_power * 24 * 30,
            "能耗等级": "A" if efficiency > 0.9 else "B" if efficiency > 0.8 else "C"
        })

    return pd.DataFrame(data)

# 侧边栏控制面板
with st.sidebar:
    st.markdown("### ⚡ 能耗控制面板")

    # 实时状态
    st.markdown("#### 📊 实时状态")
    col1, col2 = st.columns(2)
    with col1:
        current_power = np.random.uniform(95, 125)
        st.metric("当前功率", f"{current_power:.1f}kW", delta=f"{np.random.uniform(-5, 5):.1f}")
    with col2:
        efficiency = np.random.uniform(0.78, 0.88)
        st.metric("能效比", f"{efficiency:.1%}", delta=f"{np.random.uniform(-0.02, 0.02):.1%}")

    # 快速操作
    st.markdown("#### 🚀 快速操作")

    if st.button("🔄 启动能耗监控", use_container_width=True):
        st.session_state.energy_monitoring = True
        st.success("✅ 能耗监控已启动")
        st.rerun()

    if st.button("⚡ 执行节能优化", use_container_width=True):
        st.session_state.energy_optimization = True
        st.success("✅ 节能优化已启动")
        st.rerun()

    if st.button("🌱 生成绿色报告", use_container_width=True):
        st.session_state.green_report = True
        st.rerun()

    if st.button("🎯 设置节能目标", use_container_width=True):
        st.session_state.set_targets = True
        st.rerun()

# 主要内容区域
tab1, tab2, tab3, tab4, tab5 = st.tabs(["📊 能耗监控", "⚡ 优化管理", "🌱 绿色指标", "🎯 可持续目标", "📈 分析报告"])

with tab1:
    st.markdown("#### 📊 实时能耗监控")

    # 能耗概览
    energy_data = generate_energy_consumption_data()
    equipment_data = generate_equipment_energy_data()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_power = equipment_data["当前功率"].sum()
        st.metric("总功率", f"{total_power:.1f}kW", delta="正常")

    with col2:
        daily_consumption = total_power * 24
        st.metric("日能耗", f"{daily_consumption:.0f}kWh", delta="+5.2%")

    with col3:
        daily_cost = daily_consumption * 0.8
        st.metric("日电费", f"¥{daily_cost:.0f}", delta="+3.8%")

    with col4:
        carbon_emission = daily_consumption * 0.5968
        st.metric("日碳排放", f"{carbon_emission:.1f}kg", delta="-2.1%")

    # 能耗趋势图
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 📈 能耗趋势")

        fig_trend = px.line(
            energy_data.tail(168),  # 最近7天
            x="时间",
            y=["总能耗", "生产能耗", "辅助能耗"],
            title="7天能耗趋势",
            labels={"value": "能耗 (kWh)", "variable": "类型"}
        )
        fig_trend.update_layout(height=350)
        st.plotly_chart(fig_trend, use_container_width=True)

    with col2:
        st.markdown("##### 🔋 设备功率分布")

        fig_pie = px.pie(
            equipment_data,
            values="当前功率",
            names="设备ID",
            title="设备功率分布"
        )
        fig_pie.update_layout(height=350)
        st.plotly_chart(fig_pie, use_container_width=True)

    # 设备能耗详情
    st.markdown("##### 🏭 设备能耗详情")

    # 筛选器
    col1, col2 = st.columns([2, 1])
    with col1:
        status_filter = st.multiselect(
            "运行状态筛选",
            options=equipment_data["运行状态"].unique(),
            default=equipment_data["运行状态"].unique()
        )
    with col2:
        efficiency_threshold = st.slider("最低能效比", 0.0, 1.0, 0.0, 0.05)

    # 应用筛选
    filtered_equipment = equipment_data[
        (equipment_data["运行状态"].isin(status_filter)) &
        (equipment_data["能效比"] >= efficiency_threshold)
    ]

    st.dataframe(filtered_equipment, use_container_width=True, hide_index=True)

    # 能耗热力图
    st.markdown("##### 🌡️ 24小时能耗热力图")

    # 生成热力图数据
    hours = list(range(24))
    days = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]

    heatmap_data = np.random.uniform(80, 150, (7, 24))

    fig_heatmap = go.Figure(data=go.Heatmap(
        z=heatmap_data,
        x=hours,
        y=days,
        colorscale='RdYlGn_r',
        colorbar=dict(title="能耗 (kWh)")
    ))

    fig_heatmap.update_layout(
        title="一周24小时能耗热力图",
        xaxis_title="小时",
        yaxis_title="星期",
        height=300
    )

    st.plotly_chart(fig_heatmap, use_container_width=True)

with tab2:
    st.markdown("#### ⚡ 能耗优化管理")

    # 优化策略选择
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 🎯 优化策略")

        optimization_strategy = st.selectbox(
            "选择优化策略",
            ["削峰填谷", "负载均衡", "可再生能源优先", "成本最小化", "碳减排"]
        )

        optimization_target = st.slider("节能目标 (%)", 5, 30, 15)

        time_window = st.selectbox(
            "优化时间窗口",
            ["实时优化", "1小时", "4小时", "8小时", "24小时"]
        )

        if st.button("🚀 启动优化", type="primary"):
            with st.spinner("正在执行能耗优化..."):
                import time
                time.sleep(2)
                st.success(f"✅ {optimization_strategy}优化已启动！预计节能{optimization_target}%")

    with col2:
        st.markdown("##### 📊 优化效果预测")

        # 优化效果预测图
        current_consumption = 120
        optimized_consumption = current_consumption * (1 - optimization_target/100)

        fig_comparison = go.Figure()

        fig_comparison.add_trace(go.Bar(
            name='优化前',
            x=['能耗', '成本', '碳排放'],
            y=[current_consumption, current_consumption*0.8, current_consumption*0.5968],
            marker_color='lightcoral'
        ))

        fig_comparison.add_trace(go.Bar(
            name='优化后',
            x=['能耗', '成本', '碳排放'],
            y=[optimized_consumption, optimized_consumption*0.8, optimized_consumption*0.5968],
            marker_color='lightgreen'
        ))

        fig_comparison.update_layout(
            title="优化效果对比",
            barmode='group',
            height=300
        )

        st.plotly_chart(fig_comparison, use_container_width=True)

    # 优化建议
    st.markdown("##### 💡 智能优化建议")

    try:
        recommendations = green_manufacturing_service.get_optimization_recommendations()

        if recommendations:
            for i, rec in enumerate(recommendations[:3]):  # 显示前3个建议
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                    with col1:
                        st.write(f"**{rec['title']}**")
                        st.write(rec['description'])

                    with col2:
                        st.metric("潜在节约", f"¥{rec['potential_savings']:,.0f}")

                    with col3:
                        st.metric("投资成本", f"¥{rec['implementation_cost']:,.0f}")

                    with col4:
                        priority_color = {"high": "🔴", "medium": "🟡", "low": "🟢"}
                        st.write("**优先级**")
                        st.write(f"{priority_color.get(rec['priority'], '⚪')} {rec['priority']}")

                    if st.button(f"采纳建议", key=f"adopt_{rec['recommendation_id']}"):
                        st.success(f"✅ 已采纳建议: {rec['title']}")

                    st.divider()
        else:
            st.info("📊 暂无优化建议，系统运行良好")

    except Exception as e:
        st.error(f"获取优化建议失败: {e}")

    # 实时优化控制
    st.markdown("##### 🎛️ 实时优化控制")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("⚡ 紧急节能模式", use_container_width=True):
            st.warning("⚠️ 紧急节能模式已启动，非关键设备将降低功率")

    with col2:
        if st.button("🌙 夜间模式", use_container_width=True):
            st.info("🌙 夜间模式已启动，照明和辅助设备功率降低")

    with col3:
        if st.button("🔄 恢复正常模式", use_container_width=True):
            st.success("✅ 已恢复正常运行模式")

# 能耗监控启动处理
if st.session_state.get('energy_monitoring', False):
    st.session_state.energy_monitoring = False

    with st.expander("📊 能耗监控详情", expanded=True):
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("监控设备", "7台", delta="全部在线")

        with col2:
            st.metric("数据采集频率", "1分钟", delta="实时")

        with col3:
            st.metric("监控状态", "正常", delta="稳定运行")

# 页面底部信息
st.markdown("---")
st.markdown("##### ℹ️ 能耗优化说明")

with st.expander("📖 能耗优化功能详解"):
    st.markdown("""
    ### ⚡ 能耗优化与绿色制造系统

    #### 核心功能
    - **实时监控**: 24/7能耗数据采集和分析
    - **智能优化**: AI驱动的节能策略推荐
    - **绿色指标**: 全面的可持续发展指标管理
    - **碳足迹**: 碳排放计算和减排目标管理

    #### 优化策略
    1. **削峰填谷**: 避开用电高峰，降低电费成本
    2. **负载均衡**: 优化设备负载分配，提高效率
    3. **可再生能源**: 优先使用清洁能源
    4. **智能调度**: 基于生产计划的能耗优化

    #### 绿色制造优势
    - **成本节约**: 通过优化降低能耗成本10-30%
    - **环保合规**: 满足环保法规要求
    - **品牌价值**: 提升企业绿色形象
    - **可持续发展**: 支持长期可持续经营
    """)

with tab3:
    st.markdown("#### 🌱 绿色制造指标")

    # 绿色指标概览
    try:
        metrics_summary = green_manufacturing_service.get_green_metrics_summary()

        if metrics_summary:
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                energy_eff = metrics_summary.get("energy_efficiency", {})
                current_val = energy_eff.get("current_value", 0)
                target_val = energy_eff.get("target_value", 100)
                st.metric("能源效率", f"{current_val:.1f}%",
                         delta=f"目标: {target_val:.1f}%")

            with col2:
                carbon_fp = metrics_summary.get("carbon_footprint", {})
                current_val = carbon_fp.get("current_value", 0)
                st.metric("碳足迹", f"{current_val:.1f}kg",
                         delta="-5.2kg")

            with col3:
                renewable = metrics_summary.get("renewable_ratio", {})
                current_val = renewable.get("current_value", 0)
                target_val = renewable.get("target_value", 100)
                st.metric("可再生能源", f"{current_val:.1f}%",
                         delta=f"目标: {target_val:.1f}%")

            with col4:
                recycling = metrics_summary.get("recycling_rate", {})
                current_val = recycling.get("current_value", 0)
                st.metric("回收率", f"{current_val:.1f}%",
                         delta="+2.3%")

    except Exception as e:
        st.error(f"获取绿色指标失败: {e}")

    # 绿色指标雷达图
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 📊 绿色指标雷达图")

        # 模拟绿色指标数据
        categories = ['能源效率', '碳减排', '废料减少', '水资源', '可再生能源', '回收利用']
        current_values = [78.5, 65.2, 72.8, 68.9, 18.5, 85.2]
        target_values = [85.0, 80.0, 75.0, 80.0, 30.0, 90.0]

        fig_radar = go.Figure()

        fig_radar.add_trace(go.Scatterpolar(
            r=current_values,
            theta=categories,
            fill='toself',
            name='当前值',
            line_color='blue'
        ))

        fig_radar.add_trace(go.Scatterpolar(
            r=target_values,
            theta=categories,
            fill='toself',
            name='目标值',
            line_color='green',
            opacity=0.6
        ))

        fig_radar.update_layout(
            polar=dict(
                radialaxis=dict(visible=True, range=[0, 100])
            ),
            showlegend=True,
            height=350
        )

        st.plotly_chart(fig_radar, use_container_width=True)

    with col2:
        st.markdown("##### 📈 绿色指标趋势")

        # 生成趋势数据
        dates = pd.date_range(start=datetime.now()-timedelta(days=30), end=datetime.now(), freq='D')
        trend_data = []

        for date in dates:
            trend_data.append({
                "日期": date,
                "绿色评分": 75 + np.random.normal(0, 3),
                "能效指数": 78 + np.random.normal(0, 2),
                "环保指数": 72 + np.random.normal(0, 2.5)
            })

        trend_df = pd.DataFrame(trend_data)

        fig_trend = px.line(
            trend_df,
            x="日期",
            y=["绿色评分", "能效指数", "环保指数"],
            title="30天绿色指标趋势"
        )
        fig_trend.update_layout(height=350)
        st.plotly_chart(fig_trend, use_container_width=True)

    # 环保合规状态
    st.markdown("##### 🏛️ 环保合规状态")

    compliance_data = [
        {"法规类型": "大气污染防治", "合规状态": "合规", "评估日期": "2024-01-15", "下次评估": "2024-07-15"},
        {"法规类型": "水污染防治", "合规状态": "合规", "评估日期": "2024-01-10", "下次评估": "2024-07-10"},
        {"法规类型": "固废管理", "合规状态": "待改进", "评估日期": "2024-01-20", "下次评估": "2024-04-20"},
        {"法规类型": "噪声控制", "合规状态": "合规", "评估日期": "2024-01-12", "下次评估": "2024-07-12"},
        {"法规类型": "能耗限额", "合规状态": "合规", "评估日期": "2024-01-18", "下次评估": "2024-07-18"}
    ]

    compliance_df = pd.DataFrame(compliance_data)

    # 添加状态颜色
    def color_compliance_status(val):
        if val == "合规":
            return "background-color: #d4edda; color: #155724"
        elif val == "待改进":
            return "background-color: #fff3cd; color: #856404"
        else:
            return "background-color: #f8d7da; color: #721c24"

    styled_df = compliance_df.style.applymap(color_compliance_status, subset=['合规状态'])
    st.dataframe(styled_df, use_container_width=True, hide_index=True)

with tab4:
    st.markdown("#### 🎯 可持续发展目标")

    # 目标概览
    try:
        goals = green_manufacturing_service.get_sustainability_goals()

        if goals:
            st.markdown("##### 📋 目标进度概览")

            for goal in goals:
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                    with col1:
                        st.write(f"**{goal['title']}**")
                        st.write(goal['description'])

                        # 进度条
                        progress = min(goal['completion_rate'], 100) / 100
                        st.progress(progress, text=f"完成度: {goal['completion_rate']:.1f}%")

                    with col2:
                        st.metric("当前值", f"{goal['current_value']:.1f}{goal['unit']}")

                    with col3:
                        st.metric("目标值", f"{goal['target_value']:.1f}{goal['unit']}")

                    with col4:
                        priority_color = {"high": "🔴", "medium": "🟡", "low": "🟢"}
                        st.write("**优先级**")
                        st.write(f"{priority_color.get(goal['priority'], '⚪')} {goal['priority']}")
                        st.write(f"截止: {goal['deadline']}")

                    st.divider()

    except Exception as e:
        st.error(f"获取可持续发展目标失败: {e}")

    # 目标设置
    if st.session_state.get('set_targets', False):
        st.session_state.set_targets = False

        st.markdown("##### ➕ 新增可持续发展目标")

        with st.form("new_sustainability_goal"):
            col1, col2 = st.columns(2)

            with col1:
                goal_title = st.text_input("目标名称", "新的可持续发展目标")
                goal_description = st.text_area("目标描述", "详细描述目标内容和意义")
                target_value = st.number_input("目标值", min_value=0.0, value=100.0)

            with col2:
                unit = st.text_input("单位", "%")
                deadline = st.date_input("截止日期", datetime.now() + timedelta(days=365))
                priority = st.selectbox("优先级", ["high", "medium", "low"])

            if st.form_submit_button("✅ 创建目标", type="primary"):
                st.success(f"✅ 可持续发展目标 '{goal_title}' 已创建！")
                st.rerun()

with tab5:
    st.markdown("#### 📈 绿色制造分析报告")

    # 生成报告
    if st.session_state.get('green_report', False):
        st.session_state.green_report = False

        try:
            report = green_manufacturing_service.generate_green_report()

            if report:
                st.markdown("##### 📊 绿色制造综合报告")

                # 总体评分
                col1, col2, col3 = st.columns(3)

                with col1:
                    overall_score = report.get("overall_green_score", 0)
                    st.metric("绿色制造评分", f"{overall_score:.1f}分",
                             delta="优秀" if overall_score >= 80 else "良好" if overall_score >= 60 else "需改进")

                with col2:
                    potential_savings = report.get("total_potential_savings", 0)
                    st.metric("潜在节约", f"¥{potential_savings:,.0f}", delta="年度预估")

                with col3:
                    st.metric("报告生成时间", datetime.now().strftime("%H:%M"), delta="实时更新")

                # 详细分析
                st.markdown("##### 📋 详细分析")

                # 指标达成情况
                metrics_summary = report.get("metrics_summary", {})
                if metrics_summary:
                    st.markdown("###### 🎯 指标达成情况")

                    metric_data = []
                    for metric_type, data in metrics_summary.items():
                        metric_data.append({
                            "指标类型": metric_type.replace("_", " ").title(),
                            "当前值": f"{data['current_value']:.1f}{data['unit']}",
                            "目标值": f"{data['target_value']:.1f}{data['unit']}",
                            "达成率": f"{data['achievement_rate']:.1f}%",
                            "状态": "✅ 达标" if data['achievement_rate'] >= 100 else "⚠️ 待改进"
                        })

                    st.dataframe(pd.DataFrame(metric_data), use_container_width=True, hide_index=True)

                # 改进建议
                recommendations = report.get("optimization_recommendations", [])
                if recommendations:
                    st.markdown("###### 💡 改进建议")

                    for rec in recommendations[:5]:  # 显示前5个建议
                        with st.expander(f"🔧 {rec['title']}"):
                            col1, col2 = st.columns(2)

                            with col1:
                                st.write("**建议描述:**")
                                st.write(rec['description'])
                                st.write(f"**类别:** {rec['category']}")
                                st.write(f"**优先级:** {rec['priority']}")

                            with col2:
                                st.write("**经济效益:**")
                                st.write(f"潜在节约: ¥{rec['potential_savings']:,.0f}")
                                st.write(f"实施成本: ¥{rec['implementation_cost']:,.0f}")
                                st.write(f"投资回报期: {rec['payback_period_months']}个月")
                                st.write(f"ROI: {rec['roi_percentage']:.1f}%")

        except Exception as e:
            st.error(f"生成绿色制造报告失败: {e}")

    # 报告导出
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📊 生成月度报告", use_container_width=True):
            st.success("✅ 月度绿色制造报告已生成")

    with col2:
        if st.button("📈 生成年度报告", use_container_width=True):
            st.success("✅ 年度可持续发展报告已生成")

    with col3:
        if st.button("📋 导出合规报告", use_container_width=True):
            st.success("✅ 环保合规报告已导出")

# 实时状态更新
if st.button("🔄 刷新数据"):
    st.rerun()
