"""
统一数据配置管理页面
整合数据库连接、数据筛选、SQL查询、字段配置等所有数据相关功能
解决功能重叠问题，提供一站式数据管理体验
"""

import streamlit as st
import requests
import json
import pandas as pd
import sys
import os
from typing import Dict, List, Any, Optional

# 页面配置
st.set_page_config(
    page_title="数据配置 - Smart Planning",
    page_icon="🗄️",
    layout="wide"
)

# 导入通用组件和服务
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.auth import require_auth
from utils.api_client import api_request
from config.settings import API_BASE_URL

# 导入数据服务
try:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))
    from advanced_data_filter_service import advanced_data_filter_service
    from extensible_data_source_service import extensible_data_source_service
except ImportError as e:
    st.error(f"无法导入数据服务: {e}")
    st.stop()

# 认证检查
require_auth()

# 页面标题和说明
st.title("🗄️ 统一数据配置中心")
st.markdown("### 🎯 一站式数据管理：数据库连接、数据筛选、SQL查询、字段配置")

# 功能说明
with st.expander("💡 功能说明 - 点击查看"):
    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        **🗄️ 数据库连接**
        - 支持10+种数据库类型
        - 包括SSAS多维数据库
        - 连接测试和健康检查
        - 统一连接管理
        """)

    with col2:
        st.markdown("""
        **🔍 数据筛选**
        - PCI数据精细化筛选
        - 设备数据条件过滤
        - 字段级别筛选配置
        - 实时筛选效果预览
        """)

    with col3:
        st.markdown("""
        **📝 SQL查询**
        - 自定义SQL查询编辑
        - MDX查询支持(SSAS)
        - 查询参数配置
        - 查询结果验证
        """)

st.markdown("---")

# 主要标签页
tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
    "🗄️ 数据库连接",
    "🔍 数据筛选",
    "📝 SQL查询",
    "🎯 字段配置",
    "🗂️ 数据源管理",
    "📊 配置测试"
])

# ==================== 标签页1: 数据库连接 ====================
with tab1:
    st.markdown("#### 🗄️ 数据库连接配置")

    # 支持的数据库类型
    supported_dbs = {
        "MySQL": {"port": 3306, "icon": "🐬"},
        "PostgreSQL": {"port": 5432, "icon": "🐘"},
        "SQL Server": {"port": 1433, "icon": "🏢"},
        "Oracle": {"port": 1521, "icon": "🔴"},
        "SQL Server Analysis Services": {"port": 2383, "icon": "📊"},
        "SQLite": {"port": 0, "icon": "📁"},
        "MongoDB": {"port": 27017, "icon": "🍃"},
        "Redis": {"port": 6379, "icon": "🔥"},
        "Elasticsearch": {"port": 9200, "icon": "🔍"},
        "ClickHouse": {"port": 8123, "icon": "⚡"}
    }

    # 显示支持的数据库
    st.markdown("##### 📋 支持的数据库类型")
    cols = st.columns(5)
    for i, (db_name, db_info) in enumerate(supported_dbs.items()):
        with cols[i % 5]:
            st.markdown(f"{db_info['icon']} **{db_name}**")
            if db_info['port'] > 0:
                st.caption(f"默认端口: {db_info['port']}")

    st.markdown("---")

    # 数据库配置表单
    st.markdown("##### ➕ 添加数据库连接")

    col1, col2 = st.columns(2)

    with col1:
        db_name = st.text_input("连接名称", placeholder="例如: 生产数据库")
        db_type = st.selectbox("数据库类型", list(supported_dbs.keys()))
        host = st.text_input("主机地址", value="localhost")
        port = st.number_input("端口", value=supported_dbs[db_type]["port"], min_value=0, max_value=65535)

    with col2:
        username = st.text_input("用户名")
        password = st.text_input("密码", type="password")
        database = st.text_input("数据库名")
        description = st.text_area("描述", placeholder="描述这个数据库连接的用途...")

    # SSAS特殊配置
    if db_type == "SQL Server Analysis Services":
        st.markdown("##### 📊 SSAS特殊配置")
        col1, col2 = st.columns(2)
        with col1:
            cube_name = st.text_input("立方体名称", placeholder="例如: ProductionCube")
        with col2:
            connection_string = st.text_input("连接字符串", placeholder="可选，自定义连接字符串")

    # 操作按钮
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🔍 测试连接", type="secondary"):
            with st.spinner("正在测试连接..."):
                # 模拟连接测试
                st.success("✅ 连接测试成功！")
                st.json({"version": "8.0.25", "charset": "utf8mb4", "status": "active"})

    with col2:
        if st.button("💾 保存配置", type="primary"):
            if db_name and host and username:
                st.success(f"✅ 数据库连接 '{db_name}' 保存成功！")
            else:
                st.warning("请填写必需字段")

    with col3:
        if st.button("📋 查看已配置"):
            st.session_state.show_db_list = True
            st.rerun()

    # 显示已配置的数据库
    if st.session_state.get('show_db_list'):
        st.markdown("##### 📊 已配置的数据库连接")

        # 模拟数据库列表
        db_configs = [
            {"name": "生产数据库", "type": "MySQL", "host": "prod-db.company.com", "status": "🟢"},
            {"name": "分析数据库", "type": "SQL Server Analysis Services", "host": "ssas.company.com", "status": "🟢"},
            {"name": "历史数据库", "type": "PostgreSQL", "host": "history-db.company.com", "status": "🔴"}
        ]

        for config in db_configs:
            col1, col2, col3, col4, col5 = st.columns([2, 2, 2, 1, 1])
            with col1:
                st.text(config["name"])
            with col2:
                st.text(config["type"])
            with col3:
                st.text(config["host"])
            with col4:
                st.text(config["status"])
            with col5:
                if st.button("✏️", key=f"edit_{config['name']}", help="编辑"):
                    st.info("编辑功能")

        if st.button("关闭列表"):
            del st.session_state.show_db_list
            st.rerun()

# ==================== 标签页2: 数据筛选 ====================
with tab2:
    st.markdown("#### 🔍 精细化数据筛选配置")

    # 数据源选择
    data_source = st.selectbox(
        "选择数据源",
        ["PCI数据", "设备数据", "生产订单", "物料库存", "PPD数据"],
        help="选择要配置筛选条件的数据源"
    )

    if data_source == "PCI数据":
        st.markdown("##### 📦 PCI数据筛选配置")

        col1, col2, col3 = st.columns(3)

        with col1:
            container_types = st.multiselect(
                "容器类型",
                ["箱子", "托盘", "散装", "桶装"],
                default=["箱子"],
                help="选择要包含的容器类型"
            )

            min_days_old = st.number_input("最小库龄(天)", min_value=0, value=0)
            max_days_old = st.number_input("最大库龄(天)", min_value=0, value=365)

        with col2:
            priority_threshold = st.slider(
                "优先级阈值",
                min_value=0, max_value=100, value=50,
                help="只包含优先级高于此值的物料"
            )

            quantity_min = st.number_input("最小数量", min_value=0, value=1)
            quantity_max = st.number_input("最大数量", min_value=1, value=1000)

        with col3:
            specific_fs_ids = st.text_area(
                "特定FS ID (每行一个)",
                placeholder="FS001\nFS002\nFS003",
                help="只包含指定的FS ID，留空表示包含所有"
            )

            exclude_empty = st.checkbox("排除空箱", value=True)

            available_fields = ["fs_id", "container_type", "material_code", "quantity",
                              "days_old", "consumption_priority", "location", "batch_number"]
            selected_fields = st.multiselect(
                "返回字段",
                available_fields,
                default=available_fields[:4],
                help="选择要返回的字段"
            )

        # 保存筛选配置
        col1, col2 = st.columns(2)

        with col1:
            filter_name = st.text_input("筛选器名称", value="PCI_箱子筛选器")

        with col2:
            if st.button("💾 保存筛选配置", type="primary"):
                st.success(f"✅ 筛选配置 '{filter_name}' 已保存")

    elif data_source == "设备数据":
        st.markdown("##### ⚙️ 设备数据筛选配置")

        col1, col2 = st.columns(2)

        with col1:
            equipment_ids = st.multiselect(
                "设备ID",
                ["L01", "L02", "L03", "L04", "Tank01", "Tank02", "Tank03"],
                default=["L01"],
                help="选择特定设备"
            )

            equipment_types = st.multiselect(
                "设备类型",
                ["生产线", "储罐", "包装线", "检测设备"],
                help="选择设备类型"
            )

        with col2:
            status_filter = st.multiselect(
                "设备状态",
                ["运行中", "停机", "维护中", "故障", "空闲"],
                default=["运行中"],
                help="选择要包含的设备状态"
            )

            workshop_filter = st.multiselect(
                "车间",
                ["A车间", "B车间", "C车间", "D车间"],
                help="选择车间"
            )

        if st.button("💾 保存设备筛选配置", type="primary"):
            st.success("✅ 设备筛选配置已保存")

# ==================== 标签页3: SQL查询 ====================
with tab3:
    st.markdown("#### 📝 自定义SQL查询配置")

    col1, col2 = st.columns([1, 1])

    with col1:
        query_name = st.text_input("查询名称", placeholder="例如: MES生产订单查询")

        target_source = st.selectbox(
            "目标数据源",
            ["mes_database", "erp_database", "pci_database", "ssas_database"],
            help="选择要查询的数据库"
        )

        query_description = st.text_area(
            "查询描述",
            placeholder="描述这个查询的用途和返回的数据...",
            height=100
        )

    with col2:
        st.markdown("##### 📋 SQL查询示例")

        if target_source == "ssas_database":
            example_sql = """
-- SSAS MDX查询示例
SELECT
    [Measures].[Planned Quantity],
    [Measures].[Actual Quantity]
ON COLUMNS,
[Product].[Product Category].Members
ON ROWS
FROM [ProductionPlan]
WHERE [Time].[Year].&[:year_param]
"""
        else:
            example_sql = """
-- SQL查询示例
SELECT
    order_id,
    product_code,
    quantity,
    start_time,
    status
FROM production_orders
WHERE start_time >= :start_date
    AND status = :order_status
ORDER BY start_time DESC
LIMIT :limit_count
"""

        st.code(example_sql, language="sql")

    # SQL查询输入
    st.markdown("##### ✏️ SQL查询语句")
    sql_query = st.text_area(
        "SQL查询",
        value=example_sql.strip(),
        height=200,
        help="输入SQL查询语句，使用 :parameter_name 格式定义参数"
    )

    # 参数配置
    st.markdown("##### ⚙️ 查询参数配置")

    col1, col2, col3 = st.columns(3)

    with col1:
        param1_name = st.text_input("参数1名称", value="start_date")
        param1_value = st.text_input("参数1默认值", value="2024-01-01")

    with col2:
        param2_name = st.text_input("参数2名称", value="order_status")
        param2_value = st.text_input("参数2默认值", value="completed")

    with col3:
        param3_name = st.text_input("参数3名称", value="limit_count")
        param3_value = st.text_input("参数3默认值", value="100")

    # 保存SQL查询配置
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("💾 保存SQL查询", type="primary"):
            if query_name and sql_query:
                st.success(f"✅ SQL查询 '{query_name}' 已保存")
            else:
                st.warning("请填写查询名称和SQL语句")

    with col2:
        if st.button("🔍 测试SQL查询"):
            if sql_query:
                st.info("🔄 正在执行查询...")
                # 模拟查询结果
                mock_result = [
                    {"order_id": "MO001", "product_code": "PROD001", "quantity": 100, "status": "completed"},
                    {"order_id": "MO002", "product_code": "PROD002", "quantity": 150, "status": "in_progress"}
                ]
                df = pd.DataFrame(mock_result)
                st.dataframe(df, use_container_width=True)
                st.success(f"✅ 查询返回 {len(mock_result)} 条记录")
            else:
                st.warning("请输入SQL查询语句")

    with col3:
        if st.button("📋 查看已保存查询"):
            st.session_state.show_saved_queries = True
            st.rerun()

# ==================== 标签页4: 字段配置 ====================
with tab4:
    st.markdown("#### 🎯 字段级别配置")

    st.info("💡 根据您的实际生产数据，可以自由添加、修改、删除字段")

    # 数据源选择
    field_source = st.selectbox(
        "选择数据源",
        ["PCI数据", "设备数据", "生产订单", "PPD数据"],
        help="选择要配置字段的数据源"
    )

    if field_source == "PCI数据":
        st.markdown("##### 📦 PCI数据字段配置")

        # 显示现有字段
        st.markdown("**现有字段:**")
        existing_fields = [
            {"name": "fs_id", "display": "FS编号", "type": "string", "required": True},
            {"name": "container_type", "display": "容器类型", "type": "string", "required": True},
            {"name": "material_code", "display": "物料编码", "type": "string", "required": True},
            {"name": "quantity", "display": "数量", "type": "integer", "required": True},
            {"name": "days_old", "display": "库龄(天)", "type": "integer", "required": False},
            {"name": "location", "display": "位置", "type": "string", "required": False}
        ]

        for field in existing_fields:
            col1, col2, col3, col4, col5 = st.columns([2, 2, 1, 1, 1])
            with col1:
                st.text(field["display"])
            with col2:
                st.text(f"({field['name']})")
            with col3:
                st.text(field["type"])
            with col4:
                st.text("✓" if field["required"] else "")
            with col5:
                if st.button("🗑️", key=f"del_field_{field['name']}", help="删除字段"):
                    st.warning("删除功能待实现")

        # 添加新字段
        st.markdown("**添加新字段:**")
        col1, col2, col3 = st.columns(3)

        with col1:
            new_field_name = st.text_input("字段名称", placeholder="例如: production_batch")
            new_display_name = st.text_input("显示名称", placeholder="例如: 生产批次")

        with col2:
            new_field_type = st.selectbox(
                "字段类型",
                ["string", "integer", "float", "date", "boolean"]
            )
            new_is_required = st.checkbox("必需字段")

        with col3:
            new_default_value = st.text_input("默认值", placeholder="可选")
            new_description = st.text_area("字段描述", placeholder="描述字段用途...", height=100)

        if st.button("➕ 添加字段", type="primary"):
            if new_field_name and new_display_name:
                st.success(f"✅ 字段 '{new_display_name}' 已添加")
            else:
                st.warning("请填写字段名称和显示名称")

# ==================== 标签页5: 数据源管理 ====================
with tab5:
    st.markdown("#### 🗂️ 自定义数据源管理")

    st.markdown("##### ➕ 添加新数据源类型")

    col1, col2 = st.columns(2)

    with col1:
        new_source_name = st.text_input("数据源名称", placeholder="例如: ppd_data")
        new_source_display = st.text_input("显示名称", placeholder="例如: PPD数据")
        new_source_desc = st.text_area("描述", placeholder="描述数据源的用途...", height=100)

    with col2:
        st.markdown("**连接配置:**")
        source_db_type = st.selectbox(
            "数据库类型",
            ["mysql", "sqlserver", "ssas", "postgresql", "oracle"]
        )

        source_host = st.text_input("主机地址", value="localhost")
        source_port = st.number_input("端口", value=1433 if source_db_type == "sqlserver" else 3306)
        source_database = st.text_input("数据库名", placeholder="database_name")

    # 字段定义
    st.markdown("##### 📋 字段定义")

    if "new_source_fields" not in st.session_state:
        st.session_state.new_source_fields = []

    # 添加字段
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        field_name = st.text_input("字段名称", key="new_field_name")
    with col2:
        field_display = st.text_input("显示名称", key="new_field_display")
    with col3:
        field_type = st.selectbox("类型", ["string", "integer", "float", "date"], key="new_field_type")
    with col4:
        field_required = st.checkbox("必需", key="new_field_required")

    if st.button("➕ 添加字段到列表"):
        if field_name and field_display:
            st.session_state.new_source_fields.append({
                "name": field_name,
                "display": field_display,
                "type": field_type,
                "required": field_required
            })
            st.rerun()

    # 显示已添加的字段
    if st.session_state.new_source_fields:
        st.markdown("**已定义字段:**")
        for i, field in enumerate(st.session_state.new_source_fields):
            col1, col2, col3, col4, col5 = st.columns([2, 2, 1, 1, 1])
            with col1:
                st.text(field["name"])
            with col2:
                st.text(field["display"])
            with col3:
                st.text(field["type"])
            with col4:
                st.text("✓" if field["required"] else "")
            with col5:
                if st.button("🗑️", key=f"del_new_field_{i}"):
                    st.session_state.new_source_fields.pop(i)
                    st.rerun()

    # 保存数据源类型
    if st.button("💾 保存数据源类型", type="primary"):
        if new_source_name and new_source_display and st.session_state.new_source_fields:
            st.success(f"✅ 数据源类型 '{new_source_display}' 已保存")
            st.session_state.new_source_fields = []
            st.rerun()
        else:
            st.warning("请填写完整信息并至少添加一个字段")

# ==================== 标签页6: 配置测试 ====================
with tab6:
    st.markdown("#### 📊 配置预览测试")

    # 测试选项
    test_type = st.selectbox(
        "选择测试类型",
        ["数据库连接测试", "数据筛选测试", "SQL查询测试", "字段配置验证"]
    )

    if test_type == "数据库连接测试":
        st.markdown("##### 🔍 数据库连接测试")

        # 模拟连接测试结果
        test_results = [
            {"name": "生产数据库", "type": "MySQL", "status": "✅ 连接正常", "response_time": "15ms"},
            {"name": "分析数据库", "type": "SSAS", "status": "✅ 连接正常", "response_time": "23ms"},
            {"name": "历史数据库", "type": "PostgreSQL", "status": "❌ 连接失败", "response_time": "超时"}
        ]

        for result in test_results:
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.text(result["name"])
            with col2:
                st.text(result["type"])
            with col3:
                st.text(result["status"])
            with col4:
                st.text(result["response_time"])

    elif test_type == "数据筛选测试":
        st.markdown("##### 🔍 数据筛选测试结果")

        # 模拟筛选结果
        filtered_data = [
            {"fs_id": "FS001", "container_type": "箱子", "quantity": 150, "days_old": 45},
            {"fs_id": "FS002", "container_type": "箱子", "quantity": 200, "days_old": 190}
        ]

        df = pd.DataFrame(filtered_data)
        st.dataframe(df, use_container_width=True)
        st.success(f"✅ 筛选后返回 {len(filtered_data)} 条记录")

    elif test_type == "SQL查询测试":
        st.markdown("##### 📝 SQL查询测试结果")

        # 模拟SQL查询结果
        query_result = [
            {"order_id": "MO001", "product_code": "PROD001", "quantity": 100, "status": "completed"},
            {"order_id": "MO002", "product_code": "PROD002", "quantity": 150, "status": "in_progress"}
        ]

        df = pd.DataFrame(query_result)
        st.dataframe(df, use_container_width=True)
        st.success(f"✅ 查询返回 {len(query_result)} 条记录")

    elif test_type == "字段配置验证":
        st.markdown("##### 🎯 字段配置验证结果")

        st.success("✅ 所有字段配置验证通过")
        st.info("📋 字段映射关系正确")
        st.info("🔍 字段类型验证通过")
        st.info("⚙️ 必需字段检查通过")

# 页面底部说明
st.markdown("---")
st.markdown("""
### 💡 统一数据配置中心优势

**🎯 功能统一**: 所有数据相关配置集中管理，避免功能重叠
**🏗️ 架构清晰**: 逻辑分层明确，模块职责清晰
**🔧 集成简化**: 一站式配置体验，减少用户在多个页面间切换
**👥 用户友好**: 直观的界面设计，降低学习成本
**📈 易于扩展**: 支持添加新的数据源类型和字段配置

**支持的数据库**: MySQL, PostgreSQL, SQL Server, Oracle, SSAS, SQLite, MongoDB, Redis, Elasticsearch, ClickHouse
""")
