# 🎯 Smart Planning 数据智能调度与配置机制

## 📋 概述

Smart Planning系统采用智能数据调度机制，LLM和算法能够根据用户问题和任务需求自动选择相关数据，同时提供灵活的配置选项让用户自定义数据源和调用策略。

## 🧠 智能数据选择机制

### 1. 自动数据上下文获取

系统通过**数据集成服务**自动收集所有相关数据：

```python
# 数据集成服务自动获取全面数据上下文
data_context = data_integration_service.get_comprehensive_data_context()

# 包含的数据源：
- equipment: 设备状态和产能数据
- pci: PCI库存和FIFO数据  
- uploaded_files: 用户上传的Excel/CSV数据
- user_inputs: 用户手动输入数据
- learning: 算法学习和历史数据
- custom_sources: 自定义数据源
```

### 2. 智能问题分析

LLM根据用户问题智能选择相关数据：

<augment_code_snippet path="frontend/services/llm_service.py" mode="EXCERPT">
```python
def _generate_intelligent_response(self, prompt: str, language: str = "zh") -> str:
    """根据用户问题智能选择数据"""
    user_message = prompt.split("用户当前问题: ")[-1].split("\n")[0]
    
    # 生产计划相关 → 调用设备数据、物料数据、历史计划
    if any(keyword in user_message for keyword in ["生产计划", "排程", "调整计划"]):
        return self._generate_production_planning_response(user_message, prompt)
    
    # 设备相关 → 调用设备状态、维护记录、产能数据
    elif any(keyword in user_message for keyword in ["设备", "故障", "维护"]):
        return self._generate_equipment_response(user_message, prompt)
    
    # 库存相关 → 调用PCI数据、FIFO逻辑、消耗策略
    elif any(keyword in user_message for keyword in ["库存", "物料", "PCI", "FIFO"]):
        return self._generate_inventory_response(user_message, prompt)
```
</augment_code_snippet>

### 3. 算法数据自动获取

算法执行时自动获取所需的所有数据：

<augment_code_snippet path="frontend/services/data_integration_service.py" mode="EXCERPT">
```python
def get_algorithm_input_data(self) -> Dict[str, Any]:
    """为算法提供完整输入数据"""
    data_context = self.get_comprehensive_data_context()
    
    return {
        "equipment": {
            "available_capacity": data_context['data_sources']['equipment']['capacity_summary'],
            "constraints": data_context['data_sources']['equipment']['constraints']
        },
        "materials": {
            "fs_inventory": data_context['data_sources']['pci']['fs_inventory'],
            "consumption_priorities": data_context['data_sources']['pci']['consumption_priorities']
        },
        "historical_data": {
            "uploaded_data": data_context['data_sources']['uploaded_files']['recent_files']
        }
    }
```
</augment_code_snippet>

## ⚙️ 数据源配置机制

### 1. 核心数据源管理

系统内置核心数据源，可以启用/禁用：

<augment_code_snippet path="frontend/services/data_integration_service.py" mode="EXCERPT">
```python
def _register_core_data_sources(self):
    """注册核心数据源"""
    self.data_source_plugins = {
        "equipment": {
            "name": "设备数据",
            "description": "生产线和储罐设备状态数据",
            "handler": self._get_equipment_context,
            "enabled": True  # 可配置启用/禁用
        },
        "pci": {
            "name": "PCI数据", 
            "description": "FS库存和FIFO消耗数据",
            "handler": self._get_pci_context,
            "enabled": True
        },
        "uploaded_files": {
            "name": "上传文件",
            "description": "Excel、CSV、邮件等上传文件数据",
            "handler": self._get_uploaded_files_context,
            "enabled": True
        }
    }
```
</augment_code_snippet>

### 2. 自定义数据源配置

用户可以通过界面添加自定义数据源：

#### 支持的数据源类型：
- **数据库**: 连接外部MySQL、Oracle、SQL Server等
- **API接口**: 调用REST API获取数据
- **文件系统**: 读取指定路径的文件
- **传感器**: 连接IoT设备获取实时数据

#### 配置示例：

```python
# 添加外部数据库数据源
data_integration_service.add_custom_data_source(
    source_name="erp_database",
    source_type="database", 
    config={
        "connection": {
            "type": "mysql",
            "host": "erp.company.com",
            "port": 3306,
            "database": "erp_production",
            "username": "readonly_user",
            "password": "encrypted_password"
        },
        "tables": ["orders", "materials", "schedules"],
        "sync_interval": "15min"
    }
)

# 添加API数据源
data_integration_service.add_custom_data_source(
    source_name="mes_api",
    source_type="api",
    config={
        "endpoint": "https://mes.company.com/api/v1/production",
        "auth_type": "bearer_token",
        "token": "encrypted_token",
        "refresh_interval": "5min"
    }
)
```

### 3. 可视化数据源配置

在"数据中心"页面提供可视化配置界面：

<augment_code_snippet path="frontend/pages/12_数据中心.py" mode="EXCERPT">
```python
# 数据源开关控制
processing_status = data_status["processing_status"]
for source_name, status in processing_status.items():
    source_display_name = {
        "file_upload": "📁 文件上传",
        "email_import": "📧 邮件导入", 
        "database": "🗄️ 数据库",
        "api_endpoint": "🔗 API接口",
        "equipment_sensor": "⚙️ 设备传感器",
        "pci_system": "🔬 PCI系统"
    }.get(source_name, source_name)
    
    # 用户可以通过界面开关控制数据源
    enabled = st.checkbox(source_display_name, value=status, key=f"source_{source_name}")
```
</augment_code_snippet>

## 🎛️ 数据调用策略配置

### 1. LLM数据调用配置

可以配置LLM调用哪些数据源：

```python
# LLM配置示例
llm_config = {
    "data_sources": {
        "equipment": True,      # 总是包含设备数据
        "pci": True,           # 总是包含PCI数据
        "uploaded_files": True, # 包含上传文件数据
        "custom_erp": False,   # 不包含ERP数据（可选）
        "custom_mes": True     # 包含MES数据
    },
    "context_limit": 10000,    # 上下文长度限制
    "data_freshness": "1hour", # 数据新鲜度要求
    "priority_sources": ["equipment", "pci"]  # 优先级数据源
}
```

### 2. 算法数据调用配置

可以为不同算法配置不同的数据需求：

```python
# 算法数据配置示例
algorithm_configs = {
    "genetic_algorithm": {
        "required_data": ["equipment", "uploaded_files"],
        "optional_data": ["pci", "learning"],
        "data_validation": True,
        "historical_depth": "30days"
    },
    "simulated_annealing": {
        "required_data": ["equipment", "pci"],
        "optional_data": ["uploaded_files"],
        "real_time_data": True,
        "constraint_checking": True
    }
}
```

### 3. 条件化数据调用

根据条件智能选择数据：

```python
def get_conditional_data_context(self, user_question: str, task_type: str) -> Dict[str, Any]:
    """根据条件获取数据上下文"""
    context = {"timestamp": datetime.now().isoformat()}
    
    # 根据问题类型选择数据源
    if "生产计划" in user_question:
        context.update({
            "equipment": self._get_equipment_context(),
            "materials": self._get_pci_context(),
            "orders": self._get_uploaded_files_context()
        })
    
    elif "设备维护" in user_question:
        context.update({
            "equipment": self._get_equipment_context(),
            "maintenance": self._get_maintenance_history(),
            "sensors": self._get_sensor_data()
        })
    
    # 根据任务类型添加特定数据
    if task_type == "optimization":
        context["constraints"] = self._identify_constraints(context)
        context["objectives"] = self._get_optimization_objectives()
    
    return context
```

## 🔧 高级配置选项

### 1. 数据过滤和筛选

```python
# 数据过滤配置
data_filters = {
    "equipment": {
        "status": ["running", "idle"],  # 只包含运行和空闲设备
        "workshop": ["workshop_a", "workshop_b"],  # 指定车间
        "exclude_maintenance": True     # 排除维护中设备
    },
    "pci": {
        "min_days_old": 30,            # 最小库龄
        "priority_threshold": 50,       # 优先级阈值
        "material_types": ["raw", "semi"]  # 物料类型
    }
}
```

### 2. 数据缓存策略

```python
# 缓存配置
cache_config = {
    "equipment_data": {
        "ttl": 300,        # 5分钟缓存
        "refresh_on_change": True
    },
    "pci_data": {
        "ttl": 900,        # 15分钟缓存
        "background_refresh": True
    },
    "uploaded_files": {
        "ttl": 3600,       # 1小时缓存
        "persist_on_disk": True
    }
}
```

### 3. 数据质量控制

```python
# 数据质量配置
quality_config = {
    "validation_rules": {
        "equipment": ["status_valid", "capacity_positive"],
        "pci": ["quantity_positive", "date_valid"],
        "uploaded_files": ["format_valid", "schema_match"]
    },
    "error_handling": {
        "missing_data": "use_default",
        "invalid_data": "exclude",
        "stale_data": "refresh"
    }
}
```

## 🎯 实际使用示例

### 示例1：用户询问生产计划优化

```
用户问题: "L02生产线停线了，如何调整生产计划？"

系统自动调用数据：
✅ 设备数据 - 获取所有生产线状态
✅ PCI数据 - 获取物料库存和FIFO信息  
✅ 上传文件 - 获取订单数据
✅ 学习数据 - 获取历史优化经验

LLM基于这些数据给出智能建议
```

### 示例2：算法执行优化

```
算法类型: 遗传算法生产排程

系统自动获取：
✅ 设备产能和约束条件
✅ 物料消耗优先级
✅ 订单需求和交期
✅ 历史排程效果

算法基于完整数据进行优化计算
```

### 示例3：自定义数据源

```
配置ERP系统数据源：
- 连接类型：MySQL数据库
- 同步频率：每15分钟
- 包含表：orders, materials, schedules
- 启用状态：开启

LLM和算法自动包含ERP数据进行分析
```

这种智能数据调度机制确保了系统能够根据实际需求自动选择最相关的数据，同时提供了灵活的配置选项满足不同企业的个性化需求。
