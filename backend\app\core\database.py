"""
数据库配置和连接管理
支持多数据库连接
"""

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import StaticPool
import logging
from typing import AsyncGenerator, Dict, Optional, List
from enum import Enum

from app.core.config import settings

logger = logging.getLogger(__name__)


class DatabaseType(str, Enum):
    """数据库类型枚举 - 可扩展"""
    MAIN = "main"                    # 主数据库
    INVENTORY = "inventory"          # 库存数据库
    PRODUCTION_ANALYSIS = "production_analysis"  # 生产数据分析
    ERP = "erp"                     # ERP系统
    MES = "mes"                     # MES系统
    PCI = "pci"                     # PCI数据库
    QUALITY = "quality"             # 质量数据库
    LOGISTICS = "logistics"         # 物流数据库
    FINANCE = "finance"             # 财务数据库
    HR = "hr"                       # 人力资源数据库
    WAREHOUSE = "warehouse"         # 仓储数据库
    MAINTENANCE = "maintenance"     # 维护数据库
    CUSTOM1 = "custom1"             # 自定义数据库1
    CUSTOM2 = "custom2"             # 自定义数据库2
    CUSTOM3 = "custom3"             # 自定义数据库3


# 数据库引擎配置
def create_database_engine(database_url: str, echo: bool = False):
    """创建数据库引擎"""
    return create_async_engine(
        database_url,
        echo=echo,
        pool_size=10,
        max_overflow=20,
        pool_timeout=30,
        pool_recycle=3600,
        pool_pre_ping=True,
    )


# 主数据库引擎
main_engine = create_database_engine(settings.DATABASE_URL, settings.DEBUG)

# 多数据库引擎字典 - 初始化所有类型为None
engines: Dict[DatabaseType, Optional[object]] = {}

# 初始化所有数据库类型
for db_type in DatabaseType:
    engines[db_type] = None

# 设置主数据库
engines[DatabaseType.MAIN] = main_engine

# 初始化其他数据库引擎（如果配置了）
if hasattr(settings, 'INVENTORY_DB_URL') and settings.INVENTORY_DB_URL:
    engines[DatabaseType.INVENTORY] = create_database_engine(settings.INVENTORY_DB_URL)

if hasattr(settings, 'PRODUCTION_ANALYSIS_DB_URL') and settings.PRODUCTION_ANALYSIS_DB_URL:
    engines[DatabaseType.PRODUCTION_ANALYSIS] = create_database_engine(settings.PRODUCTION_ANALYSIS_DB_URL)

if hasattr(settings, 'ERP_DB_URL') and settings.ERP_DB_URL:
    engines[DatabaseType.ERP] = create_database_engine(settings.ERP_DB_URL)

if hasattr(settings, 'MES_DB_URL') and settings.MES_DB_URL:
    engines[DatabaseType.MES] = create_database_engine(settings.MES_DB_URL)

if hasattr(settings, 'PCI_DB_URL') and settings.PCI_DB_URL:
    engines[DatabaseType.PCI] = create_database_engine(settings.PCI_DB_URL)

if hasattr(settings, 'QUALITY_DB_URL') and settings.QUALITY_DB_URL:
    engines[DatabaseType.QUALITY] = create_database_engine(settings.QUALITY_DB_URL)

if hasattr(settings, 'LOGISTICS_DB_URL') and settings.LOGISTICS_DB_URL:
    engines[DatabaseType.LOGISTICS] = create_database_engine(settings.LOGISTICS_DB_URL)

# 会话工厂字典
session_factories: Dict[DatabaseType, Optional[object]] = {}

for db_type, engine in engines.items():
    if engine:
        session_factories[db_type] = async_sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,
            autocommit=False
        )

# 主数据库会话工厂（向后兼容）
AsyncSessionLocal = session_factories[DatabaseType.MAIN]
engine = main_engine

# 创建基础模型类
Base = declarative_base()


async def get_db(db_type: DatabaseType = DatabaseType.MAIN) -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话
    支持多数据库连接
    """
    session_factory = session_factories.get(db_type)
    if not session_factory:
        raise ValueError(f"数据库类型 {db_type} 未配置")

    async with session_factory() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"数据库会话错误 ({db_type}): {str(e)}")
            await session.rollback()
            raise
        finally:
            await session.close()


# 向后兼容的主数据库会话获取函数
async def get_main_db() -> AsyncGenerator[AsyncSession, None]:
    """获取主数据库会话（向后兼容）"""
    async for session in get_db(DatabaseType.MAIN):
        yield session


async def init_db():
    """初始化主数据库"""
    try:
        async with main_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("✅ 主数据库表创建成功")
    except Exception as e:
        logger.error(f"❌ 主数据库初始化失败: {str(e)}")
        raise


async def close_db():
    """关闭所有数据库连接"""
    try:
        for db_type, engine in engines.items():
            if engine:
                await engine.dispose()
                logger.info(f"✅ {db_type} 数据库连接已关闭")
    except Exception as e:
        logger.error(f"❌ 关闭数据库连接失败: {str(e)}")


class MultiDatabaseManager:
    """多数据库管理器"""

    def __init__(self):
        self.engines = engines
        self.session_factories = session_factories

    async def get_session(self, db_type: DatabaseType = DatabaseType.MAIN) -> AsyncSession:
        """获取指定数据库会话"""
        session_factory = self.session_factories.get(db_type)
        if not session_factory:
            raise ValueError(f"数据库类型 {db_type} 未配置")
        return session_factory()

    async def health_check(self, db_type: DatabaseType = DatabaseType.MAIN) -> bool:
        """数据库健康检查"""
        try:
            session_factory = self.session_factories.get(db_type)
            if not session_factory:
                return False

            async with session_factory() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"数据库健康检查失败 ({db_type}): {str(e)}")
            return False

    async def health_check_all(self) -> Dict[str, bool]:
        """检查所有数据库健康状态"""
        results = {}
        for db_type in DatabaseType:
            if self.session_factories.get(db_type):
                results[db_type.value] = await self.health_check(db_type)
            else:
                results[db_type.value] = None  # 未配置
        return results

    async def get_connection_info(self, db_type: DatabaseType = DatabaseType.MAIN) -> dict:
        """获取连接信息"""
        engine = self.engines.get(db_type)
        if not engine:
            return {"error": f"数据库类型 {db_type} 未配置"}

        pool = engine.pool
        return {
            "database_type": db_type.value,
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }

    async def get_all_connection_info(self) -> Dict[str, dict]:
        """获取所有数据库连接信息"""
        results = {}
        for db_type in DatabaseType:
            if self.engines.get(db_type):
                results[db_type.value] = await self.get_connection_info(db_type)
        return results

    def is_database_configured(self, db_type: DatabaseType) -> bool:
        """检查数据库是否已配置"""
        return self.engines.get(db_type) is not None

    def get_configured_databases(self) -> List[DatabaseType]:
        """获取已配置的数据库列表"""
        return [db_type for db_type in DatabaseType if self.is_database_configured(db_type)]


# 向后兼容的数据库管理器
class DatabaseManager:
    """数据库管理器（向后兼容）"""

    def __init__(self):
        self.engine = main_engine
        self.session_factory = AsyncSessionLocal

    async def get_session(self) -> AsyncSession:
        """获取主数据库会话"""
        return self.session_factory()

    async def health_check(self) -> bool:
        """主数据库健康检查"""
        return await multi_db_manager.health_check(DatabaseType.MAIN)

    async def get_connection_info(self) -> dict:
        """获取主数据库连接信息"""
        return await multi_db_manager.get_connection_info(DatabaseType.MAIN)


# 全局数据库管理器实例
multi_db_manager = MultiDatabaseManager()
db_manager = DatabaseManager()  # 向后兼容
