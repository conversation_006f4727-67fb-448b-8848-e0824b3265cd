"""
数字化双胞胎服务
实现生产线的数字化仿真和优化分析
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass, field
from enum import Enum
import json
import asyncio
from collections import defaultdict

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from ..core.config import settings
from ..models.production import ProductionOrder, Equipment, ProductionLine
from ..models.simulation import SimulationModel, SimulationResult

logger = logging.getLogger(__name__)


class SimulationType(Enum):
    """仿真类型"""
    PRODUCTION_LINE = "production_line"
    EQUIPMENT_PERFORMANCE = "equipment_performance"
    BOTTLENECK_ANALYSIS = "bottleneck_analysis"
    WHAT_IF_SCENARIO = "what_if_scenario"
    CAPACITY_PLANNING = "capacity_planning"
    MAINTENANCE_OPTIMIZATION = "maintenance_optimization"


class SimulationStatus(Enum):
    """仿真状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProductionLineModel:
    """生产线模型"""
    line_id: str
    name: str
    stations: List[Dict[str, Any]]
    capacity: float
    efficiency: float
    setup_time: float
    cycle_time: float
    availability: float
    quality_rate: float


@dataclass
class EquipmentModel:
    """设备模型"""
    equipment_id: str
    name: str
    type: str
    capacity: float
    efficiency: float
    mtbf: float  # Mean Time Between Failures
    mttr: float  # Mean Time To Repair
    energy_consumption: float
    maintenance_cost: float


@dataclass
class SimulationScenario:
    """仿真场景"""
    scenario_id: str
    name: str
    description: str
    simulation_type: SimulationType
    parameters: Dict[str, Any]
    duration_days: int
    created_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class SimulationMetrics:
    """仿真指标"""
    throughput: float
    utilization_rate: float
    oee: float  # Overall Equipment Effectiveness
    cycle_time: float
    lead_time: float
    wip_level: float  # Work In Process
    bottleneck_stations: List[str]
    energy_consumption: float
    production_cost: float
    quality_rate: float


class DigitalTwinService:
    """数字化双胞胎服务"""
    
    def __init__(self, db_session: AsyncSession = None):
        self.db = db_session
        self.production_models = {}
        self.equipment_models = {}
        self.simulation_cache = {}
        self.running_simulations = {}
        
        # 仿真参数
        self.simulation_config = {
            'time_step': 1.0,  # 仿真时间步长（分钟）
            'warm_up_period': 24,  # 预热期（小时）
            'random_seed': 42,
            'confidence_level': 0.95
        }
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        self.simulation_config['time_step'] = getattr(settings, 'SIMULATION_TIME_STEP', 1.0)
        self.simulation_config['warm_up_period'] = getattr(settings, 'SIMULATION_WARM_UP_PERIOD', 24)
    
    async def initialize_digital_twin(self) -> Dict[str, Any]:
        """初始化数字化双胞胎"""
        try:
            # 加载生产线模型
            await self._load_production_line_models()
            
            # 加载设备模型
            await self._load_equipment_models()
            
            # 验证模型完整性
            validation_result = await self._validate_models()
            
            return {
                "status": "success",
                "production_lines": len(self.production_models),
                "equipment_count": len(self.equipment_models),
                "validation": validation_result,
                "initialized_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"初始化数字化双胞胎失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def _load_production_line_models(self):
        """加载生产线模型"""
        try:
            # 从数据库获取生产线信息
            stmt = select(ProductionLine)
            result = await self.db.execute(stmt)
            production_lines = result.scalars().all()
            
            for line in production_lines:
                # 获取生产线的设备信息
                equipment_stmt = select(Equipment).where(Equipment.production_line_id == line.id)
                equipment_result = await self.db.execute(equipment_stmt)
                equipment_list = equipment_result.scalars().all()
                
                # 构建生产线模型
                stations = []
                for eq in equipment_list:
                    stations.append({
                        'equipment_id': eq.equipment_id,
                        'name': eq.name,
                        'capacity': eq.capacity or 100.0,
                        'efficiency': eq.efficiency or 0.85,
                        'cycle_time': eq.cycle_time or 10.0,
                        'setup_time': eq.setup_time or 30.0
                    })
                
                line_model = ProductionLineModel(
                    line_id=line.line_id,
                    name=line.name,
                    stations=stations,
                    capacity=line.capacity or 1000.0,
                    efficiency=line.efficiency or 0.85,
                    setup_time=line.setup_time or 60.0,
                    cycle_time=line.cycle_time or 30.0,
                    availability=line.availability or 0.90,
                    quality_rate=line.quality_rate or 0.95
                )
                
                self.production_models[line.line_id] = line_model
            
            logger.info(f"已加载 {len(self.production_models)} 个生产线模型")
            
        except Exception as e:
            logger.error(f"加载生产线模型失败: {e}")
    
    async def _load_equipment_models(self):
        """加载设备模型"""
        try:
            # 从数据库获取设备信息
            stmt = select(Equipment)
            result = await self.db.execute(stmt)
            equipment_list = result.scalars().all()
            
            for eq in equipment_list:
                equipment_model = EquipmentModel(
                    equipment_id=eq.equipment_id,
                    name=eq.name,
                    type=eq.type or "generic",
                    capacity=eq.capacity or 100.0,
                    efficiency=eq.efficiency or 0.85,
                    mtbf=eq.mtbf or 168.0,  # 默认168小时
                    mttr=eq.mttr or 4.0,    # 默认4小时
                    energy_consumption=eq.energy_consumption or 50.0,
                    maintenance_cost=eq.maintenance_cost or 1000.0
                )
                
                self.equipment_models[eq.equipment_id] = equipment_model
            
            logger.info(f"已加载 {len(self.equipment_models)} 个设备模型")
            
        except Exception as e:
            logger.error(f"加载设备模型失败: {e}")
    
    async def _validate_models(self) -> Dict[str, Any]:
        """验证模型完整性"""
        validation_result = {
            "valid": True,
            "warnings": [],
            "errors": []
        }
        
        try:
            # 验证生产线模型
            for line_id, line_model in self.production_models.items():
                if not line_model.stations:
                    validation_result["warnings"].append(f"生产线 {line_id} 没有配置工站")
                
                if line_model.efficiency <= 0 or line_model.efficiency > 1:
                    validation_result["errors"].append(f"生产线 {line_id} 效率值无效")
                    validation_result["valid"] = False
            
            # 验证设备模型
            for eq_id, eq_model in self.equipment_models.items():
                if eq_model.capacity <= 0:
                    validation_result["errors"].append(f"设备 {eq_id} 产能值无效")
                    validation_result["valid"] = False
                
                if eq_model.mtbf <= 0 or eq_model.mttr <= 0:
                    validation_result["warnings"].append(f"设备 {eq_id} 故障参数可能不准确")
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"模型验证失败: {str(e)}")
        
        return validation_result
    
    async def create_simulation_scenario(self, scenario: SimulationScenario) -> Dict[str, Any]:
        """创建仿真场景"""
        try:
            # 验证场景参数
            validation_result = self._validate_scenario_parameters(scenario)
            if not validation_result["valid"]:
                return {
                    "status": "failed",
                    "error": "场景参数验证失败",
                    "details": validation_result["errors"]
                }
            
            # 保存场景到数据库
            await self._save_simulation_scenario(scenario)
            
            return {
                "status": "success",
                "scenario_id": scenario.scenario_id,
                "message": "仿真场景创建成功"
            }
            
        except Exception as e:
            logger.error(f"创建仿真场景失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def run_simulation(self, scenario_id: str) -> Dict[str, Any]:
        """运行仿真"""
        try:
            # 获取场景信息
            scenario = await self._get_simulation_scenario(scenario_id)
            if not scenario:
                return {"status": "failed", "error": "场景不存在"}
            
            # 检查是否已在运行
            if scenario_id in self.running_simulations:
                return {"status": "failed", "error": "仿真已在运行中"}
            
            # 标记为运行中
            self.running_simulations[scenario_id] = {
                "status": SimulationStatus.RUNNING,
                "start_time": datetime.utcnow(),
                "progress": 0.0
            }
            
            # 异步运行仿真
            asyncio.create_task(self._execute_simulation(scenario))
            
            return {
                "status": "started",
                "scenario_id": scenario_id,
                "message": "仿真已开始运行"
            }
            
        except Exception as e:
            logger.error(f"运行仿真失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def _execute_simulation(self, scenario: SimulationScenario):
        """执行仿真"""
        try:
            logger.info(f"开始执行仿真: {scenario.scenario_id}")
            
            # 根据仿真类型选择执行方法
            if scenario.simulation_type == SimulationType.PRODUCTION_LINE:
                result = await self._simulate_production_line(scenario)
            elif scenario.simulation_type == SimulationType.BOTTLENECK_ANALYSIS:
                result = await self._simulate_bottleneck_analysis(scenario)
            elif scenario.simulation_type == SimulationType.WHAT_IF_SCENARIO:
                result = await self._simulate_what_if_scenario(scenario)
            elif scenario.simulation_type == SimulationType.CAPACITY_PLANNING:
                result = await self._simulate_capacity_planning(scenario)
            else:
                result = await self._simulate_generic_scenario(scenario)
            
            # 保存仿真结果
            await self._save_simulation_result(scenario.scenario_id, result)
            
            # 更新运行状态
            self.running_simulations[scenario.scenario_id]["status"] = SimulationStatus.COMPLETED
            self.running_simulations[scenario.scenario_id]["end_time"] = datetime.utcnow()
            
            logger.info(f"仿真完成: {scenario.scenario_id}")
            
        except Exception as e:
            logger.error(f"执行仿真失败: {e}")
            self.running_simulations[scenario.scenario_id]["status"] = SimulationStatus.FAILED
            self.running_simulations[scenario.scenario_id]["error"] = str(e)
    
    async def _simulate_production_line(self, scenario: SimulationScenario) -> SimulationMetrics:
        """仿真生产线"""
        try:
            line_id = scenario.parameters.get('line_id')
            if not line_id or line_id not in self.production_models:
                raise ValueError(f"生产线 {line_id} 不存在")
            
            line_model = self.production_models[line_id]
            
            # 仿真参数
            simulation_hours = scenario.duration_days * 24
            time_step = self.simulation_config['time_step'] / 60  # 转换为小时
            total_steps = int(simulation_hours / time_step)
            
            # 初始化仿真变量
            throughput_data = []
            utilization_data = []
            wip_levels = []
            cycle_times = []
            
            # 蒙特卡洛仿真
            np.random.seed(self.simulation_config['random_seed'])
            
            for step in range(total_steps):
                # 更新进度
                progress = (step / total_steps) * 100
                self.running_simulations[scenario.scenario_id]["progress"] = progress
                
                # 仿真一个时间步
                step_result = self._simulate_time_step(line_model, step * time_step)
                
                throughput_data.append(step_result['throughput'])
                utilization_data.append(step_result['utilization'])
                wip_levels.append(step_result['wip'])
                cycle_times.append(step_result['cycle_time'])
                
                # 每100步休眠一次，避免阻塞
                if step % 100 == 0:
                    await asyncio.sleep(0.01)
            
            # 计算仿真指标
            metrics = SimulationMetrics(
                throughput=np.mean(throughput_data),
                utilization_rate=np.mean(utilization_data),
                oee=np.mean(utilization_data) * line_model.availability * line_model.quality_rate,
                cycle_time=np.mean(cycle_times),
                lead_time=np.mean(cycle_times) * 1.2,  # 简化计算
                wip_level=np.mean(wip_levels),
                bottleneck_stations=self._identify_bottlenecks(line_model),
                energy_consumption=self._calculate_energy_consumption(line_model, np.mean(utilization_data)),
                production_cost=self._calculate_production_cost(line_model, np.mean(throughput_data)),
                quality_rate=line_model.quality_rate
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"生产线仿真失败: {e}")
            raise
    
    def _simulate_time_step(self, line_model: ProductionLineModel, current_time: float) -> Dict[str, float]:
        """仿真单个时间步"""
        # 简化的时间步仿真
        base_throughput = line_model.capacity / 24  # 每小时产量
        
        # 添加随机变化
        efficiency_variation = np.random.normal(line_model.efficiency, 0.05)
        efficiency_variation = max(0.1, min(1.0, efficiency_variation))
        
        # 计算当前步骤的指标
        throughput = base_throughput * efficiency_variation
        utilization = efficiency_variation
        wip = throughput * line_model.cycle_time / 60  # 转换为小时
        cycle_time = line_model.cycle_time * (1 + np.random.normal(0, 0.1))
        
        return {
            'throughput': throughput,
            'utilization': utilization,
            'wip': wip,
            'cycle_time': cycle_time
        }
    
    async def _simulate_bottleneck_analysis(self, scenario: SimulationScenario) -> SimulationMetrics:
        """瓶颈分析仿真"""
        try:
            line_id = scenario.parameters.get('line_id')
            line_model = self.production_models[line_id]
            
            # 分析每个工站的产能
            station_capacities = []
            for station in line_model.stations:
                capacity = station['capacity'] * station['efficiency']
                station_capacities.append({
                    'station_id': station['equipment_id'],
                    'capacity': capacity,
                    'cycle_time': station['cycle_time']
                })
            
            # 找出瓶颈工站
            bottleneck_station = min(station_capacities, key=lambda x: x['capacity'])
            bottleneck_stations = [bottleneck_station['station_id']]
            
            # 计算整体指标
            line_capacity = bottleneck_station['capacity']
            avg_utilization = line_capacity / line_model.capacity
            
            metrics = SimulationMetrics(
                throughput=line_capacity,
                utilization_rate=avg_utilization,
                oee=avg_utilization * line_model.availability * line_model.quality_rate,
                cycle_time=bottleneck_station['cycle_time'],
                lead_time=bottleneck_station['cycle_time'] * len(line_model.stations),
                wip_level=line_capacity * bottleneck_station['cycle_time'] / 60,
                bottleneck_stations=bottleneck_stations,
                energy_consumption=self._calculate_energy_consumption(line_model, avg_utilization),
                production_cost=self._calculate_production_cost(line_model, line_capacity),
                quality_rate=line_model.quality_rate
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"瓶颈分析仿真失败: {e}")
            raise
    
    async def _simulate_what_if_scenario(self, scenario: SimulationScenario) -> SimulationMetrics:
        """假设分析仿真"""
        try:
            # 获取基础模型
            line_id = scenario.parameters.get('line_id')
            base_model = self.production_models[line_id]
            
            # 应用假设变更
            modified_model = self._apply_what_if_changes(base_model, scenario.parameters)
            
            # 运行修改后的仿真
            modified_scenario = SimulationScenario(
                scenario_id=f"{scenario.scenario_id}_modified",
                name=f"{scenario.name}_modified",
                description="What-if scenario simulation",
                simulation_type=SimulationType.PRODUCTION_LINE,
                parameters={'line_id': line_id},
                duration_days=scenario.duration_days
            )
            
            # 临时替换模型
            original_model = self.production_models[line_id]
            self.production_models[line_id] = modified_model
            
            try:
                metrics = await self._simulate_production_line(modified_scenario)
            finally:
                # 恢复原始模型
                self.production_models[line_id] = original_model
            
            return metrics
            
        except Exception as e:
            logger.error(f"假设分析仿真失败: {e}")
            raise
    
    async def _simulate_capacity_planning(self, scenario: SimulationScenario) -> SimulationMetrics:
        """产能规划仿真"""
        try:
            # 获取需求预测数据
            demand_forecast = scenario.parameters.get('demand_forecast', [])
            line_id = scenario.parameters.get('line_id')
            line_model = self.production_models[line_id]
            
            # 计算所需产能
            total_demand = sum(demand_forecast)
            required_capacity = total_demand / scenario.duration_days
            
            # 分析产能缺口
            current_capacity = line_model.capacity * line_model.efficiency
            capacity_gap = required_capacity - current_capacity
            
            # 计算指标
            utilization_rate = min(required_capacity / current_capacity, 1.0)
            
            metrics = SimulationMetrics(
                throughput=min(required_capacity, current_capacity),
                utilization_rate=utilization_rate,
                oee=utilization_rate * line_model.availability * line_model.quality_rate,
                cycle_time=line_model.cycle_time,
                lead_time=line_model.cycle_time * 1.2,
                wip_level=required_capacity * line_model.cycle_time / 60,
                bottleneck_stations=self._identify_bottlenecks(line_model),
                energy_consumption=self._calculate_energy_consumption(line_model, utilization_rate),
                production_cost=self._calculate_production_cost(line_model, min(required_capacity, current_capacity)),
                quality_rate=line_model.quality_rate
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"产能规划仿真失败: {e}")
            raise
    
    async def _simulate_generic_scenario(self, scenario: SimulationScenario) -> SimulationMetrics:
        """通用场景仿真"""
        # 默认使用生产线仿真
        return await self._simulate_production_line(scenario)
    
    def _apply_what_if_changes(self, base_model: ProductionLineModel, parameters: Dict[str, Any]) -> ProductionLineModel:
        """应用假设变更"""
        # 创建模型副本
        import copy
        modified_model = copy.deepcopy(base_model)
        
        # 应用变更
        changes = parameters.get('changes', {})
        
        if 'efficiency' in changes:
            modified_model.efficiency = changes['efficiency']
        
        if 'capacity' in changes:
            modified_model.capacity = changes['capacity']
        
        if 'station_changes' in changes:
            for station_change in changes['station_changes']:
                station_id = station_change['station_id']
                for station in modified_model.stations:
                    if station['equipment_id'] == station_id:
                        station.update(station_change.get('parameters', {}))
        
        return modified_model
    
    def _identify_bottlenecks(self, line_model: ProductionLineModel) -> List[str]:
        """识别瓶颈工站"""
        if not line_model.stations:
            return []
        
        # 计算每个工站的有效产能
        station_capacities = []
        for station in line_model.stations:
            effective_capacity = station['capacity'] * station['efficiency']
            station_capacities.append((station['equipment_id'], effective_capacity))
        
        # 找出产能最低的工站
        min_capacity = min(station_capacities, key=lambda x: x[1])[1]
        bottlenecks = [station_id for station_id, capacity in station_capacities if capacity <= min_capacity * 1.05]
        
        return bottlenecks
    
    def _calculate_energy_consumption(self, line_model: ProductionLineModel, utilization: float) -> float:
        """计算能耗"""
        base_consumption = 0
        for station in line_model.stations:
            equipment_id = station['equipment_id']
            if equipment_id in self.equipment_models:
                equipment = self.equipment_models[equipment_id]
                base_consumption += equipment.energy_consumption
        
        return base_consumption * utilization
    
    def _calculate_production_cost(self, line_model: ProductionLineModel, throughput: float) -> float:
        """计算生产成本"""
        # 简化的成本计算
        fixed_cost = 1000.0  # 固定成本
        variable_cost_per_unit = 10.0  # 单位变动成本
        
        return fixed_cost + (throughput * variable_cost_per_unit)
    
    def _validate_scenario_parameters(self, scenario: SimulationScenario) -> Dict[str, Any]:
        """验证场景参数"""
        validation_result = {
            "valid": True,
            "errors": []
        }
        
        # 验证基本参数
        if scenario.duration_days <= 0:
            validation_result["valid"] = False
            validation_result["errors"].append("仿真持续时间必须大于0")
        
        # 验证特定类型参数
        if scenario.simulation_type == SimulationType.PRODUCTION_LINE:
            line_id = scenario.parameters.get('line_id')
            if not line_id or line_id not in self.production_models:
                validation_result["valid"] = False
                validation_result["errors"].append(f"生产线 {line_id} 不存在")
        
        return validation_result
    
    async def get_simulation_status(self, scenario_id: str) -> Dict[str, Any]:
        """获取仿真状态"""
        try:
            if scenario_id in self.running_simulations:
                simulation_info = self.running_simulations[scenario_id]
                return {
                    "status": simulation_info["status"].value,
                    "progress": simulation_info.get("progress", 0.0),
                    "start_time": simulation_info["start_time"].isoformat(),
                    "end_time": simulation_info.get("end_time", "").isoformat() if simulation_info.get("end_time") else None,
                    "error": simulation_info.get("error")
                }
            else:
                # 检查是否有已完成的结果
                result = await self._get_simulation_result(scenario_id)
                if result:
                    return {
                        "status": "completed",
                        "progress": 100.0,
                        "has_result": True
                    }
                else:
                    return {
                        "status": "not_found",
                        "progress": 0.0
                    }
                    
        except Exception as e:
            logger.error(f"获取仿真状态失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def get_simulation_result(self, scenario_id: str) -> Optional[Dict[str, Any]]:
        """获取仿真结果"""
        try:
            result = await self._get_simulation_result(scenario_id)
            if result:
                return {
                    "scenario_id": scenario_id,
                    "metrics": result.__dict__,
                    "generated_at": datetime.utcnow().isoformat()
                }
            return None
            
        except Exception as e:
            logger.error(f"获取仿真结果失败: {e}")
            return None
    
    async def compare_scenarios(self, scenario_ids: List[str]) -> Dict[str, Any]:
        """比较仿真场景"""
        try:
            comparison_data = {}
            
            for scenario_id in scenario_ids:
                result = await self.get_simulation_result(scenario_id)
                if result:
                    comparison_data[scenario_id] = result['metrics']
            
            if len(comparison_data) < 2:
                return {"error": "需要至少两个场景进行比较"}
            
            # 计算比较指标
            comparison_metrics = {}
            metric_names = ['throughput', 'utilization_rate', 'oee', 'cycle_time', 'production_cost']
            
            for metric in metric_names:
                values = [data[metric] for data in comparison_data.values()]
                comparison_metrics[metric] = {
                    'min': min(values),
                    'max': max(values),
                    'avg': sum(values) / len(values),
                    'best_scenario': max(comparison_data.keys(), key=lambda k: comparison_data[k][metric])
                }
            
            return {
                "scenarios": comparison_data,
                "comparison": comparison_metrics,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"比较仿真场景失败: {e}")
            return {"error": str(e)}
    
    # 数据库操作方法
    async def _save_simulation_scenario(self, scenario: SimulationScenario):
        """保存仿真场景"""
        # 实现保存到数据库的逻辑
        pass
    
    async def _get_simulation_scenario(self, scenario_id: str) -> Optional[SimulationScenario]:
        """获取仿真场景"""
        # 实现从数据库获取场景的逻辑
        return None
    
    async def _save_simulation_result(self, scenario_id: str, result: SimulationMetrics):
        """保存仿真结果"""
        # 实现保存结果到数据库的逻辑
        pass
    
    async def _get_simulation_result(self, scenario_id: str) -> Optional[SimulationMetrics]:
        """获取仿真结果"""
        # 实现从数据库获取结果的逻辑
        return None


# 全局数字化双胞胎服务实例
digital_twin_service = DigitalTwinService()
