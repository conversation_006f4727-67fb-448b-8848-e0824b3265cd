"""
Smart APS 自动化报告生成服务
实现定时报告、异常报告、邮件推送和报告模板功能
"""

import json
import logging
import smtplib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import pandas as pd
import io
import base64

@dataclass
class ReportTemplate:
    """报告模板"""
    id: str
    name: str
    description: str
    template_type: str  # daily, weekly, monthly, anomaly, custom
    sections: List[Dict[str, Any]]
    format: str  # html, pdf, excel
    recipients: List[str]
    schedule: Dict[str, Any]  # cron-like schedule
    enabled: bool = True
    created_at: str = None
    updated_at: str = None

@dataclass
class ReportData:
    """报告数据"""
    id: str
    template_id: str
    title: str
    content: str
    format: str
    generated_at: str
    data_sources: List[str]
    file_path: Optional[str] = None
    sent_to: List[str] = None

@dataclass
class ScheduledReport:
    """定时报告"""
    id: str
    template_id: str
    name: str
    schedule_type: str  # daily, weekly, monthly
    schedule_time: str  # HH:MM format
    schedule_day: Optional[int] = None  # for weekly (0-6) or monthly (1-31)
    enabled: bool = True
    last_run: Optional[str] = None
    next_run: Optional[str] = None

class AutomatedReportService:
    """自动化报告生成服务"""

    def __init__(self, storage_dir: str = "data/reports"):
        self.logger = logging.getLogger(__name__)
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)

        # 报告存储
        self.report_templates = {}
        self.scheduled_reports = {}
        self.generated_reports = []

        # 邮件配置
        self.email_config = {
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "username": "",
            "password": "",
            "from_email": "",
            "enabled": False
        }

        # 初始化默认模板
        self._initialize_default_templates()

        # 加载配置
        self._load_configurations()

        # 数据源集成
        self.data_sources = {
            "production_data": self._get_production_data,
            "equipment_status": self._get_equipment_status,
            "quality_metrics": self._get_quality_metrics,
            "performance_kpis": self._get_performance_kpis,
            "anomaly_alerts": self._get_anomaly_alerts
        }

    def _initialize_default_templates(self):
        """初始化默认报告模板"""
        # 日报模板
        daily_template = ReportTemplate(
            id="daily_operations",
            name="日常运营报告",
            description="每日生产运营情况汇总",
            template_type="daily",
            sections=[
                {
                    "type": "summary",
                    "title": "运营概览",
                    "data_source": "performance_kpis",
                    "config": {"metrics": ["production_output", "efficiency", "quality_rate"]}
                },
                {
                    "type": "chart",
                    "title": "生产趋势",
                    "data_source": "production_data",
                    "config": {"chart_type": "line", "time_range": "24h"}
                },
                {
                    "type": "table",
                    "title": "设备状态",
                    "data_source": "equipment_status",
                    "config": {"columns": ["name", "status", "utilization", "alerts"]}
                },
                {
                    "type": "alerts",
                    "title": "异常警报",
                    "data_source": "anomaly_alerts",
                    "config": {"severity": ["high", "medium"]}
                }
            ],
            format="html",
            recipients=["<EMAIL>"],
            schedule={"type": "daily", "time": "08:00"},
            created_at=datetime.now().isoformat()
        )

        # 周报模板
        weekly_template = ReportTemplate(
            id="weekly_summary",
            name="周度总结报告",
            description="每周生产和质量总结",
            template_type="weekly",
            sections=[
                {
                    "type": "executive_summary",
                    "title": "执行摘要",
                    "data_source": "performance_kpis",
                    "config": {"time_range": "7d", "comparison": "previous_week"}
                },
                {
                    "type": "production_analysis",
                    "title": "生产分析",
                    "data_source": "production_data",
                    "config": {"breakdown": "by_product", "time_range": "7d"}
                },
                {
                    "type": "quality_report",
                    "title": "质量报告",
                    "data_source": "quality_metrics",
                    "config": {"metrics": ["defect_rate", "yield", "customer_complaints"]}
                },
                {
                    "type": "recommendations",
                    "title": "改进建议",
                    "data_source": "anomaly_alerts",
                    "config": {"analysis_type": "trend_analysis"}
                }
            ],
            format="html",
            recipients=["<EMAIL>", "<EMAIL>"],
            schedule={"type": "weekly", "day": 1, "time": "09:00"},  # Monday 9:00
            created_at=datetime.now().isoformat()
        )

        # 异常报告模板
        anomaly_template = ReportTemplate(
            id="anomaly_alert",
            name="异常警报报告",
            description="检测到异常时自动生成的报告",
            template_type="anomaly",
            sections=[
                {
                    "type": "alert_summary",
                    "title": "异常概览",
                    "data_source": "anomaly_alerts",
                    "config": {"current_alerts": True}
                },
                {
                    "type": "impact_analysis",
                    "title": "影响分析",
                    "data_source": "production_data",
                    "config": {"analysis_window": "2h"}
                },
                {
                    "type": "recommended_actions",
                    "title": "建议措施",
                    "data_source": "anomaly_alerts",
                    "config": {"action_type": "immediate"}
                }
            ],
            format="html",
            recipients=["<EMAIL>", "<EMAIL>"],
            schedule={"type": "trigger", "condition": "anomaly_detected"},
            created_at=datetime.now().isoformat()
        )

        self.report_templates = {
            "daily_operations": daily_template,
            "weekly_summary": weekly_template,
            "anomaly_alert": anomaly_template
        }

    def _load_configurations(self):
        """加载配置"""
        try:
            # 加载邮件配置
            email_config_file = self.storage_dir / "email_config.json"
            if email_config_file.exists():
                with open(email_config_file, 'r', encoding='utf-8') as f:
                    self.email_config.update(json.load(f))

            # 加载定时报告配置
            scheduled_reports_file = self.storage_dir / "scheduled_reports.json"
            if scheduled_reports_file.exists():
                with open(scheduled_reports_file, 'r', encoding='utf-8') as f:
                    scheduled_data = json.load(f)
                    self.scheduled_reports = {
                        k: ScheduledReport(**v) for k, v in scheduled_data.items()
                    }

        except Exception as e:
            self.logger.error(f"加载配置失败: {str(e)}")

    def create_report_template(self, template_data: Dict[str, Any]) -> ReportTemplate:
        """创建报告模板"""
        template_id = template_data.get("id") or f"template_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        template = ReportTemplate(
            id=template_id,
            name=template_data["name"],
            description=template_data.get("description", ""),
            template_type=template_data.get("template_type", "custom"),
            sections=template_data.get("sections", []),
            format=template_data.get("format", "html"),
            recipients=template_data.get("recipients", []),
            schedule=template_data.get("schedule", {}),
            enabled=template_data.get("enabled", True),
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

        self.report_templates[template_id] = template
        self._save_template(template)

        self.logger.info(f"创建报告模板: {template_id}")
        return template

    def generate_report(self, template_id: str, custom_data: Dict[str, Any] = None) -> ReportData:
        """生成报告"""
        template = self.report_templates.get(template_id)
        if not template:
            raise ValueError(f"报告模板不存在: {template_id}")

        report_id = f"report_{template_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 收集数据
        report_content = self._build_report_content(template, custom_data)

        # 生成报告
        if template.format == "html":
            content = self._generate_html_report(template, report_content)
        elif template.format == "excel":
            content = self._generate_excel_report(template, report_content)
        else:
            content = self._generate_text_report(template, report_content)

        # 保存报告
        file_path = self._save_report_file(report_id, content, template.format)

        report_data = ReportData(
            id=report_id,
            template_id=template_id,
            title=f"{template.name} - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            content=content,
            format=template.format,
            generated_at=datetime.now().isoformat(),
            data_sources=list(set(section["data_source"] for section in template.sections)),
            file_path=str(file_path) if file_path else None
        )

        self.generated_reports.append(report_data)

        self.logger.info(f"生成报告: {report_id}")
        return report_data

    def send_report(self, report_data: ReportData, recipients: List[str] = None) -> bool:
        """发送报告"""
        if not self.email_config.get("enabled", False):
            self.logger.warning("邮件功能未启用")
            return False

        try:
            template = self.report_templates.get(report_data.template_id)
            recipients = recipients or (template.recipients if template else [])

            if not recipients:
                self.logger.warning("没有指定收件人")
                return False

            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.email_config["from_email"]
            msg['To'] = ", ".join(recipients)
            msg['Subject'] = report_data.title

            # 邮件正文
            if report_data.format == "html":
                msg.attach(MIMEText(report_data.content, 'html', 'utf-8'))
            else:
                msg.attach(MIMEText(report_data.content, 'plain', 'utf-8'))

            # 附件
            if report_data.file_path and Path(report_data.file_path).exists():
                with open(report_data.file_path, "rb") as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {Path(report_data.file_path).name}'
                    )
                    msg.attach(part)

            # 发送邮件
            server = smtplib.SMTP(self.email_config["smtp_server"], self.email_config["smtp_port"])
            server.starttls()
            server.login(self.email_config["username"], self.email_config["password"])
            server.send_message(msg)
            server.quit()

            # 更新发送记录
            report_data.sent_to = recipients

            self.logger.info(f"报告已发送: {report_data.id} to {recipients}")
            return True

        except Exception as e:
            self.logger.error(f"发送报告失败: {str(e)}")
            return False

    def schedule_report(self, template_id: str, schedule_config: Dict[str, Any]) -> ScheduledReport:
        """安排定时报告"""
        schedule_id = f"schedule_{template_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        scheduled_report = ScheduledReport(
            id=schedule_id,
            template_id=template_id,
            name=schedule_config.get("name", f"定时报告 - {template_id}"),
            schedule_type=schedule_config["schedule_type"],
            schedule_time=schedule_config["schedule_time"],
            schedule_day=schedule_config.get("schedule_day"),
            enabled=schedule_config.get("enabled", True)
        )

        # 计算下次运行时间
        scheduled_report.next_run = self._calculate_next_run(scheduled_report)

        self.scheduled_reports[schedule_id] = scheduled_report
        self._save_scheduled_reports()

        self.logger.info(f"安排定时报告: {schedule_id}")
        return scheduled_report

    def trigger_anomaly_report(self, anomaly_data: Dict[str, Any]) -> Optional[ReportData]:
        """触发异常报告"""
        anomaly_template = None
        for template in self.report_templates.values():
            if template.template_type == "anomaly" and template.enabled:
                anomaly_template = template
                break

        if not anomaly_template:
            self.logger.warning("没有找到启用的异常报告模板")
            return None

        try:
            # 生成异常报告
            report_data = self.generate_report(anomaly_template.id, anomaly_data)

            # 自动发送
            if anomaly_template.recipients:
                self.send_report(report_data)

            self.logger.info(f"触发异常报告: {report_data.id}")
            return report_data

        except Exception as e:
            self.logger.error(f"触发异常报告失败: {str(e)}")
            return None

    def _build_report_content(self, template: ReportTemplate, custom_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """构建报告内容"""
        content = {
            "title": template.name,
            "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "sections": []
        }

        for section in template.sections:
            try:
                section_data = self._build_section_content(section, custom_data)
                content["sections"].append(section_data)
            except Exception as e:
                self.logger.error(f"构建报告段落失败 {section['title']}: {str(e)}")
                # 添加错误段落
                content["sections"].append({
                    "title": section["title"],
                    "type": section["type"],
                    "error": f"数据获取失败: {str(e)}"
                })

        return content

    def _build_section_content(self, section: Dict[str, Any], custom_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """构建段落内容"""
        data_source = section["data_source"]
        config = section.get("config", {})

        # 获取数据
        if custom_data and data_source in custom_data:
            raw_data = custom_data[data_source]
        else:
            data_fetcher = self.data_sources.get(data_source)
            if data_fetcher:
                raw_data = data_fetcher(config)
            else:
                raw_data = {"error": f"未知数据源: {data_source}"}

        # 处理数据
        processed_data = self._process_section_data(section["type"], raw_data, config)

        return {
            "title": section["title"],
            "type": section["type"],
            "data": processed_data,
            "config": config
        }

    def _process_section_data(self, section_type: str, raw_data: Any, config: Dict[str, Any]) -> Any:
        """处理段落数据"""
        if section_type == "summary":
            return self._process_summary_data(raw_data, config)
        elif section_type == "chart":
            return self._process_chart_data(raw_data, config)
        elif section_type == "table":
            return self._process_table_data(raw_data, config)
        elif section_type == "alerts":
            return self._process_alerts_data(raw_data, config)
        else:
            return raw_data

    def _process_summary_data(self, raw_data: Any, config: Dict[str, Any]) -> Dict[str, Any]:
        """处理摘要数据"""
        metrics = config.get("metrics", [])

        summary = {
            "metrics": [],
            "total_items": len(raw_data) if isinstance(raw_data, list) else 1
        }

        if isinstance(raw_data, dict):
            for metric in metrics:
                if metric in raw_data:
                    summary["metrics"].append({
                        "name": metric,
                        "value": raw_data[metric],
                        "unit": raw_data.get(f"{metric}_unit", "")
                    })

        return summary

    def _process_chart_data(self, raw_data: Any, config: Dict[str, Any]) -> Dict[str, Any]:
        """处理图表数据"""
        chart_type = config.get("chart_type", "line")

        if isinstance(raw_data, list) and len(raw_data) > 0:
            return {
                "chart_type": chart_type,
                "data": raw_data[:50],  # 限制数据量
                "labels": [item.get("label", f"项目{i+1}") for i, item in enumerate(raw_data[:50])]
            }

        return {"chart_type": chart_type, "data": [], "labels": []}

    def _process_table_data(self, raw_data: Any, config: Dict[str, Any]) -> Dict[str, Any]:
        """处理表格数据"""
        columns = config.get("columns", [])

        if isinstance(raw_data, list) and len(raw_data) > 0:
            # 如果指定了列，只返回这些列
            if columns:
                filtered_data = []
                for item in raw_data:
                    if isinstance(item, dict):
                        filtered_item = {col: item.get(col, "") for col in columns}
                        filtered_data.append(filtered_item)
                return {"columns": columns, "data": filtered_data[:100]}  # 限制行数
            else:
                # 返回所有数据
                first_item = raw_data[0]
                if isinstance(first_item, dict):
                    columns = list(first_item.keys())
                    return {"columns": columns, "data": raw_data[:100]}

        return {"columns": [], "data": []}

    def _process_alerts_data(self, raw_data: Any, config: Dict[str, Any]) -> Dict[str, Any]:
        """处理警报数据"""
        severity_filter = config.get("severity", [])

        if isinstance(raw_data, list):
            filtered_alerts = raw_data

            # 按严重程度过滤
            if severity_filter:
                filtered_alerts = [
                    alert for alert in raw_data
                    if isinstance(alert, dict) and alert.get("severity") in severity_filter
                ]

            return {
                "alerts": filtered_alerts[:20],  # 限制警报数量
                "total_count": len(filtered_alerts),
                "severity_counts": self._count_by_severity(filtered_alerts)
            }

        return {"alerts": [], "total_count": 0, "severity_counts": {}}

    def _count_by_severity(self, alerts: List[Dict[str, Any]]) -> Dict[str, int]:
        """按严重程度统计警报"""
        counts = {"high": 0, "medium": 0, "low": 0}

        for alert in alerts:
            severity = alert.get("severity", "low")
            if severity in counts:
                counts[severity] += 1

        return counts

    def _generate_html_report(self, template: ReportTemplate, content: Dict[str, Any]) -> str:
        """生成HTML报告"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{content['title']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #e8f4fd; border-radius: 3px; }}
                table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .alert {{ padding: 10px; margin: 5px 0; border-radius: 3px; }}
                .alert.high {{ background-color: #ffebee; border-left: 4px solid #f44336; }}
                .alert.medium {{ background-color: #fff3e0; border-left: 4px solid #ff9800; }}
                .alert.low {{ background-color: #e8f5e8; border-left: 4px solid #4caf50; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{content['title']}</h1>
                <p>生成时间: {content['generated_at']}</p>
            </div>
        """

        for section in content["sections"]:
            html_content += self._generate_html_section(section)

        html_content += """
        </body>
        </html>
        """

        return html_content

    def _generate_html_section(self, section: Dict[str, Any]) -> str:
        """生成HTML段落"""
        if "error" in section:
            return f"""
            <div class="section">
                <h2>{section['title']}</h2>
                <p style="color: red;">错误: {section['error']}</p>
            </div>
            """

        section_type = section["type"]
        data = section["data"]

        html = f'<div class="section"><h2>{section["title"]}</h2>'

        if section_type == "summary":
            html += self._generate_summary_html(data)
        elif section_type == "table":
            html += self._generate_table_html(data)
        elif section_type == "alerts":
            html += self._generate_alerts_html(data)
        elif section_type == "chart":
            html += self._generate_chart_html(data)
        else:
            html += f"<p>{str(data)}</p>"

        html += "</div>"
        return html

    def _generate_summary_html(self, data: Dict[str, Any]) -> str:
        """生成摘要HTML"""
        html = "<div>"

        for metric in data.get("metrics", []):
            html += f"""
            <div class="metric">
                <strong>{metric['name']}</strong><br>
                {metric['value']} {metric.get('unit', '')}
            </div>
            """

        html += "</div>"
        return html

    def _generate_table_html(self, data: Dict[str, Any]) -> str:
        """生成表格HTML"""
        columns = data.get("columns", [])
        rows = data.get("data", [])

        if not columns or not rows:
            return "<p>暂无数据</p>"

        html = "<table><thead><tr>"
        for col in columns:
            html += f"<th>{col}</th>"
        html += "</tr></thead><tbody>"

        for row in rows:
            html += "<tr>"
            for col in columns:
                value = row.get(col, "") if isinstance(row, dict) else ""
                html += f"<td>{value}</td>"
            html += "</tr>"

        html += "</tbody></table>"
        return html

    def _generate_alerts_html(self, data: Dict[str, Any]) -> str:
        """生成警报HTML"""
        alerts = data.get("alerts", [])

        if not alerts:
            return "<p>无警报</p>"

        html = f"<p>总计: {data.get('total_count', 0)} 个警报</p>"

        for alert in alerts:
            severity = alert.get("severity", "low")
            message = alert.get("message", "未知警报")
            timestamp = alert.get("timestamp", "")

            html += f"""
            <div class="alert {severity}">
                <strong>[{severity.upper()}]</strong> {message}
                {f'<br><small>{timestamp}</small>' if timestamp else ''}
            </div>
            """

        return html

    def _generate_chart_html(self, data: Dict[str, Any]) -> str:
        """生成图表HTML（简化版）"""
        chart_data = data.get("data", [])

        if not chart_data:
            return "<p>暂无图表数据</p>"

        # 简化的图表表示
        html = "<div style='background-color: #f9f9f9; padding: 20px; text-align: center;'>"
        html += f"<p>📊 {data.get('chart_type', '图表')} ({len(chart_data)} 个数据点)</p>"
        html += "<p><em>图表数据已包含在报告中，请使用专业工具查看详细图表</em></p>"
        html += "</div>"

        return html

    def _generate_excel_report(self, template: ReportTemplate, content: Dict[str, Any]) -> str:
        """生成Excel报告"""
        # 简化的Excel生成（返回CSV格式）
        csv_content = f"{content['title']}\n生成时间: {content['generated_at']}\n\n"

        for section in content["sections"]:
            csv_content += f"\n{section['title']}\n"
            csv_content += "=" * len(section['title']) + "\n"

            if section["type"] == "table" and "data" in section:
                data = section["data"]
                if data.get("columns") and data.get("data"):
                    # 添加表头
                    csv_content += ",".join(data["columns"]) + "\n"
                    # 添加数据行
                    for row in data["data"]:
                        if isinstance(row, dict):
                            values = [str(row.get(col, "")) for col in data["columns"]]
                            csv_content += ",".join(values) + "\n"
            else:
                csv_content += str(section.get("data", "")) + "\n"

            csv_content += "\n"

        return csv_content

    def _generate_text_report(self, template: ReportTemplate, content: Dict[str, Any]) -> str:
        """生成文本报告"""
        text_content = f"{content['title']}\n"
        text_content += "=" * len(content['title']) + "\n"
        text_content += f"生成时间: {content['generated_at']}\n\n"

        for section in content["sections"]:
            text_content += f"\n{section['title']}\n"
            text_content += "-" * len(section['title']) + "\n"

            if "error" in section:
                text_content += f"错误: {section['error']}\n"
            else:
                text_content += self._format_section_as_text(section)

            text_content += "\n"

        return text_content

    def _format_section_as_text(self, section: Dict[str, Any]) -> str:
        """将段落格式化为文本"""
        section_type = section["type"]
        data = section["data"]

        if section_type == "summary":
            text = ""
            for metric in data.get("metrics", []):
                text += f"  {metric['name']}: {metric['value']} {metric.get('unit', '')}\n"
            return text

        elif section_type == "table":
            if not data.get("columns") or not data.get("data"):
                return "  暂无数据\n"

            text = ""
            columns = data["columns"]

            # 表头
            text += "  " + " | ".join(columns) + "\n"
            text += "  " + "-" * (len(" | ".join(columns))) + "\n"

            # 数据行
            for row in data["data"][:10]:  # 限制显示行数
                if isinstance(row, dict):
                    values = [str(row.get(col, "")) for col in columns]
                    text += "  " + " | ".join(values) + "\n"

            return text

        elif section_type == "alerts":
            alerts = data.get("alerts", [])
            if not alerts:
                return "  无警报\n"

            text = f"  总计: {data.get('total_count', 0)} 个警报\n\n"
            for alert in alerts[:10]:  # 限制显示数量
                severity = alert.get("severity", "low")
                message = alert.get("message", "未知警报")
                text += f"  [{severity.upper()}] {message}\n"

            return text

        else:
            return f"  {str(data)}\n"

    def _save_report_file(self, report_id: str, content: str, format: str) -> Optional[Path]:
        """保存报告文件"""
        try:
            reports_dir = self.storage_dir / "generated_reports"
            reports_dir.mkdir(exist_ok=True)

            if format == "html":
                file_path = reports_dir / f"{report_id}.html"
            elif format == "excel":
                file_path = reports_dir / f"{report_id}.csv"
            else:
                file_path = reports_dir / f"{report_id}.txt"

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return file_path

        except Exception as e:
            self.logger.error(f"保存报告文件失败: {str(e)}")
            return None

    def _save_template(self, template: ReportTemplate):
        """保存报告模板"""
        try:
            templates_dir = self.storage_dir / "templates"
            templates_dir.mkdir(exist_ok=True)

            file_path = templates_dir / f"{template.id}.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(template), f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"保存模板失败: {str(e)}")

    def _save_scheduled_reports(self):
        """保存定时报告配置"""
        try:
            file_path = self.storage_dir / "scheduled_reports.json"
            scheduled_data = {k: asdict(v) for k, v in self.scheduled_reports.items()}

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(scheduled_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"保存定时报告配置失败: {str(e)}")

    def _calculate_next_run(self, scheduled_report: ScheduledReport) -> str:
        """计算下次运行时间"""
        try:
            now = datetime.now()
            schedule_time = datetime.strptime(scheduled_report.schedule_time, "%H:%M").time()

            if scheduled_report.schedule_type == "daily":
                next_run = datetime.combine(now.date(), schedule_time)
                if next_run <= now:
                    next_run += timedelta(days=1)

            elif scheduled_report.schedule_type == "weekly":
                target_weekday = scheduled_report.schedule_day or 0  # Monday
                days_ahead = target_weekday - now.weekday()
                if days_ahead <= 0:  # Target day already happened this week
                    days_ahead += 7
                next_run = datetime.combine(now.date(), schedule_time) + timedelta(days=days_ahead)

            elif scheduled_report.schedule_type == "monthly":
                target_day = scheduled_report.schedule_day or 1
                next_run = datetime.combine(now.date().replace(day=target_day), schedule_time)
                if next_run <= now:
                    # Next month
                    if now.month == 12:
                        next_run = next_run.replace(year=now.year + 1, month=1)
                    else:
                        next_run = next_run.replace(month=now.month + 1)

            else:
                next_run = now + timedelta(hours=1)  # Default fallback

            return next_run.isoformat()

        except Exception as e:
            self.logger.error(f"计算下次运行时间失败: {str(e)}")
            return (datetime.now() + timedelta(hours=1)).isoformat()

    # 数据源方法
    def _get_production_data(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取生产数据"""
        import random
        from datetime import datetime, timedelta

        time_range = config.get("time_range", "24h")
        hours = 24 if time_range == "24h" else 168 if time_range == "7d" else 24

        data = []
        for i in range(hours):
            timestamp = datetime.now() - timedelta(hours=i)
            data.append({
                "timestamp": timestamp.strftime("%Y-%m-%d %H:%M"),
                "production_output": random.randint(80, 120),
                "efficiency": random.uniform(0.8, 0.95),
                "downtime": random.randint(0, 30),
                "label": f"{i}小时前"
            })

        return data

    def _get_equipment_status(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取设备状态数据"""
        import random

        equipment_data = []
        for i in range(6):
            equipment_data.append({
                "name": f"设备{i+1}",
                "status": random.choice(["运行中", "维护中", "故障", "停机"]),
                "utilization": random.randint(60, 95),
                "temperature": random.randint(60, 80),
                "alerts": random.randint(0, 3)
            })

        return equipment_data

    def _get_quality_metrics(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取质量指标数据"""
        import random

        return {
            "defect_rate": random.uniform(0.02, 0.08),
            "defect_rate_unit": "%",
            "yield": random.uniform(0.92, 0.98),
            "yield_unit": "%",
            "customer_complaints": random.randint(0, 5),
            "customer_complaints_unit": "件",
            "quality_score": random.uniform(85, 95),
            "quality_score_unit": "分"
        }

    def _get_performance_kpis(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取性能KPI数据"""
        import random

        return {
            "production_output": random.randint(950, 1050),
            "production_output_unit": "件",
            "efficiency": random.uniform(0.85, 0.95),
            "efficiency_unit": "%",
            "quality_rate": random.uniform(0.92, 0.98),
            "quality_rate_unit": "%",
            "oee": random.uniform(0.75, 0.85),
            "oee_unit": "%",
            "cost_per_unit": random.uniform(15, 25),
            "cost_per_unit_unit": "元"
        }

    def _get_anomaly_alerts(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取异常警报数据"""
        import random
        from datetime import datetime, timedelta

        severity_filter = config.get("severity", ["high", "medium", "low"])

        alerts = []
        for i in range(random.randint(0, 10)):
            severity = random.choice(severity_filter)
            timestamp = datetime.now() - timedelta(hours=random.randint(0, 24))

            alerts.append({
                "id": f"ALERT{i+1:03d}",
                "severity": severity,
                "message": random.choice([
                    "设备温度异常",
                    "生产效率下降",
                    "质量指标异常",
                    "设备振动异常",
                    "原料库存不足"
                ]),
                "timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "equipment": f"设备{random.randint(1, 6)}",
                "status": random.choice(["未处理", "处理中", "已解决"])
            })

        return alerts

    def get_report_templates(self) -> List[ReportTemplate]:
        """获取所有报告模板"""
        return list(self.report_templates.values())

    def get_scheduled_reports(self) -> List[ScheduledReport]:
        """获取所有定时报告"""
        return list(self.scheduled_reports.values())

    def get_generated_reports(self, limit: int = 50) -> List[ReportData]:
        """获取生成的报告列表"""
        return self.generated_reports[-limit:]

    def configure_email(self, email_config: Dict[str, Any]):
        """配置邮件设置"""
        self.email_config.update(email_config)

        # 保存配置
        try:
            config_file = self.storage_dir / "email_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.email_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存邮件配置失败: {str(e)}")


# 创建全局实例
automated_report_service = AutomatedReportService()