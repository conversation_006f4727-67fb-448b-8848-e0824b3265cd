"""
生产计划相关的Pydantic模式
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, validator


class ProductionPlanBase(BaseModel):
    """生产计划基础模式"""
    plan_name: str
    plan_code: str
    description: Optional[str] = None
    plan_start_date: datetime
    plan_end_date: datetime
    
    @validator('plan_end_date')
    def validate_end_date(cls, v, values):
        if 'plan_start_date' in values and v <= values['plan_start_date']:
            raise ValueError('结束时间必须晚于开始时间')
        return v


class ProductionPlanCreate(ProductionPlanBase):
    """生产计划创建模式"""
    optimization_config: Optional[Dict[str, Any]] = None
    constraints: Optional[Dict[str, Any]] = None


class ProductionPlanUpdate(BaseModel):
    """生产计划更新模式"""
    plan_name: Optional[str] = None
    description: Optional[str] = None
    plan_start_date: Optional[datetime] = None
    plan_end_date: Optional[datetime] = None
    optimization_config: Optional[Dict[str, Any]] = None
    constraints: Optional[Dict[str, Any]] = None
    status: Optional[str] = None
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ["draft", "optimizing", "optimized", "executing", "completed", "cancelled"]
            if v not in allowed_statuses:
                raise ValueError(f'状态必须是: {", ".join(allowed_statuses)}')
        return v


class ProductionPlanDetailResponse(BaseModel):
    """生产计划明细响应模式"""
    id: str
    order_id: str
    order_code: str
    product_id: str
    product_code: str
    product_name: str
    planned_quantity: float
    unit: str
    scheduled_start_time: datetime
    scheduled_end_time: datetime
    equipment_id: str
    equipment_code: str
    priority: int
    status: str
    actual_start_time: Optional[datetime] = None
    actual_end_time: Optional[datetime] = None
    actual_quantity: Optional[float] = None
    
    class Config:
        from_attributes = True


class ProductionPlanResponse(BaseModel):
    """生产计划响应模式"""
    id: str
    plan_name: str
    plan_code: str
    description: Optional[str] = None
    plan_start_date: datetime
    plan_end_date: datetime
    status: str
    optimization_config: Optional[Dict[str, Any]] = None
    constraints: Optional[Dict[str, Any]] = None
    optimization_result: Optional[Dict[str, Any]] = None
    total_orders: int
    total_quantity: float
    equipment_count: int
    created_at: datetime
    updated_at: datetime
    plan_details: List[ProductionPlanDetailResponse] = []
    
    class Config:
        from_attributes = True


class ProductionPlanListResponse(BaseModel):
    """生产计划列表响应模式"""
    plans: List[ProductionPlanResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class OptimizationRequest(BaseModel):
    """优化请求模式"""
    algorithm: str = "milp"
    objective: str = "minimize_makespan"
    constraints: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    
    @validator('algorithm')
    def validate_algorithm(cls, v):
        allowed_algorithms = ["milp", "genetic", "simulated_annealing", "hybrid"]
        if v not in allowed_algorithms:
            raise ValueError(f'算法必须是: {", ".join(allowed_algorithms)}')
        return v
    
    @validator('objective')
    def validate_objective(cls, v):
        allowed_objectives = [
            "minimize_makespan", "minimize_cost", "maximize_utilization", 
            "minimize_tardiness", "maximize_throughput"
        ]
        if v not in allowed_objectives:
            raise ValueError(f'目标函数必须是: {", ".join(allowed_objectives)}')
        return v


class PlanExecutionRequest(BaseModel):
    """计划执行请求模式"""
    notes: Optional[str] = None


class EquipmentBase(BaseModel):
    """设备基础模式"""
    equipment_code: str
    equipment_name: str
    equipment_type: str
    specifications: Optional[Dict[str, Any]] = None
    capacity_per_hour: Optional[float] = None
    capacity_unit: Optional[str] = None
    location: Optional[str] = None
    workshop: Optional[str] = None


class EquipmentCreate(EquipmentBase):
    """设备创建模式"""
    pass


class EquipmentUpdate(BaseModel):
    """设备更新模式"""
    equipment_name: Optional[str] = None
    equipment_type: Optional[str] = None
    specifications: Optional[Dict[str, Any]] = None
    capacity_per_hour: Optional[float] = None
    capacity_unit: Optional[str] = None
    status: Optional[str] = None
    location: Optional[str] = None
    workshop: Optional[str] = None
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ["available", "busy", "maintenance", "offline"]
            if v not in allowed_statuses:
                raise ValueError(f'状态必须是: {", ".join(allowed_statuses)}')
        return v


class EquipmentCapabilityResponse(BaseModel):
    """设备能力响应模式"""
    id: str
    product_type: str
    process_type: str
    efficiency: float
    setup_time: float
    quality_rate: float
    
    class Config:
        from_attributes = True


class EquipmentResponse(BaseModel):
    """设备响应模式"""
    id: str
    equipment_code: str
    equipment_name: str
    equipment_type: str
    specifications: Optional[Dict[str, Any]] = None
    capacity_per_hour: Optional[float] = None
    capacity_unit: Optional[str] = None
    status: str
    location: Optional[str] = None
    workshop: Optional[str] = None
    last_maintenance_date: Optional[datetime] = None
    next_maintenance_date: Optional[datetime] = None
    oee: Optional[float] = None
    utilization_rate: Optional[float] = None
    created_at: datetime
    updated_at: datetime
    capabilities: List[EquipmentCapabilityResponse] = []
    
    class Config:
        from_attributes = True


class EquipmentListResponse(BaseModel):
    """设备列表响应模式"""
    equipment: List[EquipmentResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class GanttChartTask(BaseModel):
    """甘特图任务模式"""
    id: str
    name: str
    start: datetime
    end: datetime
    duration: float
    progress: float
    resource: str
    dependencies: List[str] = []
    color: Optional[str] = None
    status: str


class GanttChartData(BaseModel):
    """甘特图数据模式"""
    tasks: List[GanttChartTask]
    resources: List[str]
    timeline: Dict[str, Any]
    statistics: Dict[str, Any]


class OptimizationResult(BaseModel):
    """优化结果模式"""
    algorithm: str
    objective_value: float
    execution_time: float
    iterations: int
    convergence: bool
    solution_quality: str
    statistics: Dict[str, Any]
    gantt_data: GanttChartData


class PlanExecutionResponse(BaseModel):
    """计划执行响应模式"""
    id: str
    plan_id: str
    execution_date: datetime
    executor_id: str
    execution_status: str
    completion_rate: float
    completed_orders: int
    completed_quantity: float
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
