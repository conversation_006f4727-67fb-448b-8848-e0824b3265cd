"""
系统启动初始化服务
"""

import logging
import asyncio
from app.services.database_config_service import database_config_storage_service

logger = logging.getLogger(__name__)


async def initialize_database_configs():
    """初始化数据库配置"""
    try:
        logger.info("🔄 开始初始化数据库配置...")
        await database_config_storage_service.initialize_from_stored_configs()
        logger.info("✅ 数据库配置初始化完成")
    except Exception as e:
        logger.error(f"❌ 数据库配置初始化失败: {str(e)}")


async def startup_tasks():
    """启动任务"""
    tasks = [
        initialize_database_configs(),
    ]
    
    await asyncio.gather(*tasks, return_exceptions=True)
