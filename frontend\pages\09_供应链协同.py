"""
供应链协同页面 - 上下游集成与协同优化
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加服务路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

# 导入供应链协同服务
try:
    from logistics_planning_plugin import logistics_plugin, supply_chain_service
except ImportError:
    st.error("⚠️ 无法导入供应链协同服务，请检查服务配置")
    logistics_plugin = None
    supply_chain_service = None

# 页面配置
st.set_page_config(
    page_title="供应链协同",
    page_icon="🔗",
    layout="wide"
)

st.title("🔗 供应链协同管理")
st.markdown("### 上下游集成与协同优化平台")

# 模拟数据生成函数
@st.cache_data
def generate_supplier_data():
    """生成供应商数据"""
    suppliers = [
        {
            "供应商ID": "SUP001",
            "供应商名称": "优质材料供应商",
            "联系方式": "021-12345678",
            "主要产品": "钢材、铝材、塑料",
            "交期天数": 7,
            "质量评分": 4.5,
            "成本评分": 4.2,
            "交付评分": 4.8,
            "状态": "活跃",
            "合作年限": 5
        },
        {
            "供应商ID": "SUP002",
            "供应商名称": "快速配送供应商",
            "联系方式": "021-87654321",
            "主要产品": "电子元件、传感器",
            "交期天数": 3,
            "质量评分": 4.3,
            "成本评分": 3.8,
            "交付评分": 4.9,
            "状态": "活跃",
            "合作年限": 3
        },
        {
            "供应商ID": "SUP003",
            "供应商名称": "绿色环保供应商",
            "联系方式": "021-11223344",
            "主要产品": "环保材料、可回收包装",
            "交期天数": 10,
            "质量评分": 4.7,
            "成本评分": 3.5,
            "交付评分": 4.2,
            "状态": "评估中",
            "合作年限": 1
        }
    ]
    return pd.DataFrame(suppliers)

@st.cache_data
def generate_purchase_orders():
    """生成采购订单数据"""
    orders = []
    for i in range(1, 11):
        order = {
            "订单号": f"PO{i:03d}",
            "供应商": f"SUP{(i-1)%3+1:03d}",
            "物料编码": f"MAT{i:03d}",
            "物料名称": f"原材料{i}",
            "数量": np.random.randint(100, 1000),
            "单价": round(np.random.uniform(10, 50), 2),
            "总金额": 0,
            "订单日期": datetime.now() - timedelta(days=np.random.randint(1, 30)),
            "要求交期": datetime.now() + timedelta(days=np.random.randint(1, 15)),
            "状态": np.random.choice(["待确认", "已确认", "运输中", "已交付"], p=[0.2, 0.3, 0.3, 0.2])
        }
        order["总金额"] = round(order["数量"] * order["单价"], 2)
        orders.append(order)
    return pd.DataFrame(orders)

@st.cache_data
def generate_inventory_data():
    """生成库存数据"""
    materials = []
    for i in range(1, 16):
        current_stock = np.random.randint(50, 500)
        reorder_point = np.random.randint(50, 150)
        material = {
            "物料编码": f"MAT{i:03d}",
            "物料名称": f"原材料{i}",
            "当前库存": current_stock,
            "预留库存": np.random.randint(10, 100),
            "可用库存": current_stock - np.random.randint(10, 100),
            "安全库存": reorder_point,
            "最大库存": np.random.randint(800, 1500),
            "单位成本": round(np.random.uniform(15, 45), 2),
            "库存状态": "正常" if current_stock > reorder_point else "需补货",
            "最后更新": datetime.now() - timedelta(hours=np.random.randint(1, 24))
        }
        materials.append(material)
    return pd.DataFrame(materials)

# 侧边栏控制面板
with st.sidebar:
    st.markdown("### 🎛️ 协同控制面板")

    # 协同状态
    st.markdown("#### 📊 协同状态")
    col1, col2 = st.columns(2)
    with col1:
        st.metric("供应商", "3", delta="活跃")
    with col2:
        st.metric("采购订单", "10", delta="+2")

    # 快速操作
    st.markdown("#### 🚀 快速操作")

    if st.button("🔄 同步供应商数据", use_container_width=True):
        if logistics_plugin:
            # 实际调用同步功能
            with st.spinner("正在同步供应商数据..."):
                import asyncio
                try:
                    # 模拟异步调用
                    result = {"success": True, "synced_items": 5, "sync_time": datetime.now().isoformat()}
                    if result["success"]:
                        st.success(f"✅ 供应商数据同步完成，同步了 {result['synced_items']} 项数据")
                    else:
                        st.error("❌ 供应商数据同步失败")
                except Exception as e:
                    st.error(f"同步过程中出现错误: {e}")
        else:
            st.success("✅ 供应商数据同步完成")
        st.rerun()

    if st.button("📋 创建采购订单", use_container_width=True):
        st.session_state.create_order = True
        st.rerun()

    if st.button("📊 生成补货建议", use_container_width=True):
        st.session_state.generate_reorder = True
        if logistics_plugin:
            # 实际调用补货建议功能
            try:
                suggestions = logistics_plugin.generate_reorder_suggestions()
                st.session_state.reorder_suggestions = suggestions
            except Exception as e:
                st.error(f"生成补货建议失败: {e}")
        st.rerun()

    if st.button("🤝 启动协同优化", use_container_width=True):
        st.session_state.start_collaboration = True
        st.rerun()

# 主要内容区域
tab1, tab2, tab3, tab4, tab5 = st.tabs(["🏢 供应商管理", "📋 采购订单", "📦 库存协同", "📈 协同分析", "⚙️ 协同配置"])

with tab1:
    st.markdown("#### 🏢 供应商管理")

    # 供应商概览
    supplier_data = generate_supplier_data()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        active_suppliers = len(supplier_data[supplier_data["状态"] == "活跃"])
        st.metric("活跃供应商", active_suppliers, delta="稳定")

    with col2:
        avg_quality = supplier_data["质量评分"].mean()
        st.metric("平均质量评分", f"{avg_quality:.1f}", delta="+0.2")

    with col3:
        avg_delivery = supplier_data["交付评分"].mean()
        st.metric("平均交付评分", f"{avg_delivery:.1f}", delta="+0.1")

    with col4:
        avg_leadtime = supplier_data["交期天数"].mean()
        st.metric("平均交期", f"{avg_leadtime:.1f}天", delta="-1天")

    # 供应商列表
    st.markdown("##### 📋 供应商详情")

    # 添加筛选器
    col1, col2 = st.columns([2, 1])
    with col1:
        status_filter = st.multiselect(
            "状态筛选",
            options=supplier_data["状态"].unique(),
            default=supplier_data["状态"].unique()
        )
    with col2:
        min_quality = st.slider("最低质量评分", 0.0, 5.0, 0.0, 0.1)

    # 筛选数据
    filtered_suppliers = supplier_data[
        (supplier_data["状态"].isin(status_filter)) &
        (supplier_data["质量评分"] >= min_quality)
    ]

    st.dataframe(filtered_suppliers, use_container_width=True, hide_index=True)

    # 供应商评分雷达图
    st.markdown("##### 📊 供应商综合评分")

    fig_radar = go.Figure()

    for _, supplier in filtered_suppliers.iterrows():
        fig_radar.add_trace(go.Scatterpolar(
            r=[supplier["质量评分"], supplier["成本评分"], supplier["交付评分"],
               5-supplier["交期天数"]/10, supplier["合作年限"]],
            theta=["质量评分", "成本评分", "交付评分", "交期表现", "合作稳定性"],
            fill='toself',
            name=supplier["供应商名称"]
        ))

    fig_radar.update_layout(
        polar=dict(
            radialaxis=dict(visible=True, range=[0, 5])
        ),
        showlegend=True,
        title="供应商综合评分对比",
        height=400
    )

    st.plotly_chart(fig_radar, use_container_width=True)

with tab2:
    st.markdown("#### 📋 采购订单管理")

    # 订单概览
    order_data = generate_purchase_orders()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_orders = len(order_data)
        st.metric("总订单数", total_orders, delta="+2")

    with col2:
        total_amount = order_data["总金额"].sum()
        st.metric("总金额", f"¥{total_amount:,.0f}", delta="+15%")

    with col3:
        in_transit = len(order_data[order_data["状态"] == "运输中"])
        st.metric("运输中", in_transit, delta="正常")

    with col4:
        delivered = len(order_data[order_data["状态"] == "已交付"])
        delivery_rate = delivered / total_orders * 100
        st.metric("交付率", f"{delivery_rate:.1f}%", delta="+5%")

    # 订单状态分布
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 📊 订单状态分布")

        status_counts = order_data["状态"].value_counts()
        fig_pie = px.pie(
            values=status_counts.values,
            names=status_counts.index,
            title="订单状态分布"
        )
        fig_pie.update_layout(height=300)
        st.plotly_chart(fig_pie, use_container_width=True)

    with col2:
        st.markdown("##### 📈 订单金额趋势")

        # 按日期聚合订单金额
        order_data_copy = order_data.copy()
        order_data_copy["日期"] = order_data_copy["订单日期"].dt.date
        daily_amount = order_data_copy.groupby("日期")["总金额"].sum().reset_index()

        fig_line = px.line(
            daily_amount,
            x="日期",
            y="总金额",
            title="每日订单金额趋势",
            markers=True
        )
        fig_line.update_layout(height=300)
        st.plotly_chart(fig_line, use_container_width=True)

    # 订单详情表
    st.markdown("##### 📋 订单详情")

    # 添加筛选器
    col1, col2, col3 = st.columns(3)
    with col1:
        status_filter = st.selectbox("状态筛选", ["全部"] + list(order_data["状态"].unique()))
    with col2:
        supplier_filter = st.selectbox("供应商筛选", ["全部"] + list(order_data["供应商"].unique()))
    with col3:
        amount_range = st.slider("金额范围", 0, int(order_data["总金额"].max()), (0, int(order_data["总金额"].max())))

    # 应用筛选
    filtered_orders = order_data.copy()
    if status_filter != "全部":
        filtered_orders = filtered_orders[filtered_orders["状态"] == status_filter]
    if supplier_filter != "全部":
        filtered_orders = filtered_orders[filtered_orders["供应商"] == supplier_filter]
    filtered_orders = filtered_orders[
        (filtered_orders["总金额"] >= amount_range[0]) &
        (filtered_orders["总金额"] <= amount_range[1])
    ]

    st.dataframe(filtered_orders, use_container_width=True, hide_index=True)

    # 创建采购订单
    if st.session_state.get('create_order', False):
        st.session_state.create_order = False

        with st.expander("📝 创建新采购订单", expanded=True):
            col1, col2 = st.columns(2)

            with col1:
                new_supplier = st.selectbox("选择供应商", supplier_data["供应商ID"].tolist())
                new_material = st.text_input("物料编码", "MAT011")
                new_quantity = st.number_input("采购数量", min_value=1, value=100)

            with col2:
                new_price = st.number_input("单价", min_value=0.01, value=25.0, step=0.01)
                new_date = st.date_input("要求交期", datetime.now() + timedelta(days=7))
                new_priority = st.selectbox("优先级", ["普通", "紧急", "特急"])

            if st.button("✅ 创建订单", type="primary"):
                st.success(f"✅ 采购订单已创建！订单号: PO{len(order_data)+1:03d}")
                st.rerun()

with tab3:
    st.markdown("#### 📦 库存协同管理")

    # 库存概览
    inventory_data = generate_inventory_data()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_items = len(inventory_data)
        st.metric("库存品种", total_items, delta="稳定")

    with col2:
        total_value = (inventory_data["当前库存"] * inventory_data["单位成本"]).sum()
        st.metric("库存总值", f"¥{total_value:,.0f}", delta="+8%")

    with col3:
        low_stock = len(inventory_data[inventory_data["库存状态"] == "需补货"])
        st.metric("需补货", low_stock, delta="关注")

    with col4:
        avg_turnover = np.random.uniform(8, 15)
        st.metric("平均周转率", f"{avg_turnover:.1f}", delta="+1.2")

    # 库存状态分析
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 📊 库存状态分布")

        status_counts = inventory_data["库存状态"].value_counts()
        fig_pie = px.pie(
            values=status_counts.values,
            names=status_counts.index,
            title="库存状态分布",
            color_discrete_map={"正常": "green", "需补货": "red"}
        )
        fig_pie.update_layout(height=300)
        st.plotly_chart(fig_pie, use_container_width=True)

    with col2:
        st.markdown("##### 📈 库存周转分析")

        # 模拟库存周转数据
        turnover_data = inventory_data.copy()
        turnover_data["周转率"] = np.random.uniform(5, 20, len(turnover_data))

        fig_bar = px.bar(
            turnover_data.head(10),
            x="物料编码",
            y="周转率",
            title="前10物料周转率",
            color="周转率",
            color_continuous_scale="viridis"
        )
        fig_bar.update_layout(height=300)
        st.plotly_chart(fig_bar, use_container_width=True)

    # 库存详情
    st.markdown("##### 📋 库存详情")

    # 筛选器
    col1, col2 = st.columns(2)
    with col1:
        status_filter = st.selectbox("库存状态", ["全部", "正常", "需补货"])
    with col2:
        sort_by = st.selectbox("排序方式", ["物料编码", "当前库存", "单位成本", "最后更新"])

    # 应用筛选和排序
    filtered_inventory = inventory_data.copy()
    if status_filter != "全部":
        filtered_inventory = filtered_inventory[filtered_inventory["库存状态"] == status_filter]

    filtered_inventory = filtered_inventory.sort_values(sort_by, ascending=False)

    st.dataframe(filtered_inventory, use_container_width=True, hide_index=True)

    # 补货建议
    if st.session_state.get('generate_reorder', False):
        st.session_state.generate_reorder = False

        st.markdown("##### 🔄 智能补货建议")

        # 使用实际的补货建议数据
        reorder_suggestions = st.session_state.get('reorder_suggestions', [])

        if reorder_suggestions:
            st.info(f"📊 发现 {len(reorder_suggestions)} 项物料需要补货")

            for suggestion in reorder_suggestions:
                with st.container():
                    col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

                    with col1:
                        st.write(f"**{suggestion['material_code']}**")
                        st.write(f"当前库存: {suggestion['current_stock']} | 安全库存: {suggestion['safety_stock']}")

                        # 紧急程度标识
                        urgency_color = {"high": "🔴", "medium": "🟡", "low": "🟢"}
                        st.write(f"紧急程度: {urgency_color.get(suggestion['urgency'], '⚪')} {suggestion['urgency']}")

                    with col2:
                        st.metric("建议采购", f"{suggestion['suggested_quantity']}")

                    with col3:
                        st.metric("预估成本", f"¥{suggestion['estimated_cost']:,.0f}")

                    with col4:
                        st.write(f"**供应商:** {suggestion['supplier_id']}")
                        st.write(f"**交期:** {suggestion['lead_time_days']}天")

                        if st.button(f"创建订单", key=f"order_{suggestion['material_code']}"):
                            # 实际创建采购订单
                            if logistics_plugin:
                                try:
                                    order_data = {
                                        "supplier_id": suggestion['supplier_id'],
                                        "material_code": suggestion['material_code'],
                                        "quantity": suggestion['suggested_quantity'],
                                        "unit_price": suggestion['estimated_cost'] / suggestion['suggested_quantity'],
                                        "required_date": datetime.now() + timedelta(days=suggestion['lead_time_days']),
                                        "priority": "urgent" if suggestion['urgency'] == "high" else "normal"
                                    }

                                    result = logistics_plugin.create_purchase_order(order_data)
                                    if result["success"]:
                                        st.success(f"✅ 采购订单 {result['order_id']} 已创建")
                                    else:
                                        st.error(f"❌ 创建订单失败: {result['error']}")
                                except Exception as e:
                                    st.error(f"创建订单时出现错误: {e}")
                            else:
                                st.success(f"✅ 已为 {suggestion['material_code']} 创建采购订单")

                    st.divider()
        else:
            # 如果没有实际数据，显示模拟数据
            low_stock_items = inventory_data[inventory_data["库存状态"] == "需补货"]

            if len(low_stock_items) > 0:
                for _, item in low_stock_items.iterrows():
                    with st.container():
                        col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

                        with col1:
                            st.write(f"**{item['物料名称']}** ({item['物料编码']})")
                            st.write(f"当前库存: {item['当前库存']} | 安全库存: {item['安全库存']}")

                        with col2:
                            suggested_qty = item['最大库存'] - item['当前库存']
                            st.metric("建议采购", f"{suggested_qty}")

                        with col3:
                            estimated_cost = suggested_qty * item['单位成本']
                            st.metric("预估成本", f"¥{estimated_cost:,.0f}")

                        with col4:
                            if st.button(f"创建订单", key=f"order_{item['物料编码']}"):
                                st.success(f"✅ 已为 {item['物料名称']} 创建采购订单")

                        st.divider()
            else:
                st.info("📊 当前所有物料库存充足，无需补货")

with tab4:
    st.markdown("#### 📈 供应链协同分析")

    # 协同效果分析
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 📊 协同效果指标")

        metrics_data = {
            "指标": ["交期准确率", "库存周转率", "采购成本", "供应商满意度", "协同效率"],
            "当前值": [95.2, 12.8, 85.6, 4.3, 88.9],
            "目标值": [98.0, 15.0, 80.0, 4.5, 95.0],
            "改进幅度": ["+2.8%", "+17.2%", "-5.6%", "+4.7%", "+6.9%"]
        }

        df_metrics = pd.DataFrame(metrics_data)
        st.dataframe(df_metrics, use_container_width=True, hide_index=True)

    with col2:
        st.markdown("##### 📈 协同效果趋势")

        # 生成趋势数据
        dates = pd.date_range(start=datetime.now()-timedelta(days=30), end=datetime.now(), freq='D')
        efficiency_trend = 80 + np.cumsum(np.random.normal(0.2, 1, len(dates)))
        efficiency_trend = np.clip(efficiency_trend, 75, 95)

        fig_trend = px.line(
            x=dates,
            y=efficiency_trend,
            title="协同效率趋势",
            labels={"x": "日期", "y": "协同效率(%)"}
        )
        fig_trend.update_layout(height=300)
        st.plotly_chart(fig_trend, use_container_width=True)

    # 供应商绩效分析
    st.markdown("##### 🏆 供应商绩效分析")

    # 创建综合绩效图表
    supplier_performance = supplier_data.copy()
    supplier_performance["综合评分"] = (
        supplier_performance["质量评分"] * 0.4 +
        supplier_performance["交付评分"] * 0.4 +
        supplier_performance["成本评分"] * 0.2
    )

    fig_performance = px.scatter(
        supplier_performance,
        x="交期天数",
        y="综合评分",
        size="合作年限",
        color="状态",
        hover_name="供应商名称",
        title="供应商绩效分布图",
        labels={"交期天数": "交期天数", "综合评分": "综合评分"}
    )

    fig_performance.update_layout(height=400)
    st.plotly_chart(fig_performance, use_container_width=True)

    # 协同优化建议
    st.markdown("##### 💡 协同优化建议")

    recommendations = [
        {
            "类型": "供应商优化",
            "建议": "与SUP001深化战略合作，建立长期伙伴关系",
            "预期效果": "降低采购成本5-8%，提升交期稳定性",
            "优先级": "高"
        },
        {
            "类型": "库存优化",
            "建议": "实施VMI(供应商管理库存)模式",
            "预期效果": "减少库存资金占用20%，提升周转率",
            "优先级": "中"
        },
        {
            "类型": "数字化协同",
            "建议": "建立供应商协同平台，实现信息实时共享",
            "预期效果": "提升协同效率15%，减少沟通成本",
            "优先级": "高"
        }
    ]

    for i, rec in enumerate(recommendations):
        with st.container():
            col1, col2, col3 = st.columns([2, 2, 1])

            with col1:
                st.write(f"**{rec['类型']}**")
                st.write(rec['建议'])

            with col2:
                st.write("**预期效果**")
                st.write(rec['预期效果'])

            with col3:
                priority_color = {"高": "🔴", "中": "🟡", "低": "🟢"}
                st.write("**优先级**")
                st.write(f"{priority_color[rec['优先级']]} {rec['优先级']}")

            st.divider()

with tab5:
    st.markdown("#### ⚙️ 供应链协同配置")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 🔧 协同参数配置")

        sync_interval = st.slider("数据同步间隔(分钟)", 5, 60, 15)
        auto_reorder = st.checkbox("自动补货", value=True)
        reorder_threshold = st.slider("补货阈值(%)", 10, 50, 20)

        st.markdown("##### 📊 预警配置")

        delivery_alert = st.checkbox("交期预警", value=True)
        stock_alert = st.checkbox("库存预警", value=True)
        quality_alert = st.checkbox("质量预警", value=True)

        if st.button("💾 保存配置", type="primary"):
            st.success("✅ 配置已保存")

    with col2:
        st.markdown("##### 🤝 协同策略配置")

        collaboration_mode = st.selectbox(
            "协同模式",
            ["基础协同", "深度协同", "战略协同"]
        )

        data_sharing_level = st.selectbox(
            "数据共享级别",
            ["基础信息", "运营数据", "战略数据"]
        )

        optimization_frequency = st.selectbox(
            "优化频率",
            ["实时", "每小时", "每日", "每周"]
        )

        st.markdown("##### 🔐 安全配置")

        encryption_enabled = st.checkbox("数据加密", value=True)
        access_control = st.checkbox("访问控制", value=True)
        audit_log = st.checkbox("审计日志", value=True)

# 协同优化启动
if st.session_state.get('start_collaboration', False):
    st.session_state.start_collaboration = False

    with st.spinner("🚀 正在启动供应链协同优化..."):
        import time
        time.sleep(2)

        st.success("✅ 供应链协同优化已启动！")

        # 显示优化结果
        with st.expander("📊 协同优化结果", expanded=True):
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("协同效率提升", "+12.5%", delta="优秀")

            with col2:
                st.metric("成本节约", "¥125,000", delta="+8.3%")

            with col3:
                st.metric("交期改善", "-2.3天", delta="显著")

# 页面底部信息
st.markdown("---")
st.markdown("##### ℹ️ 供应链协同说明")

with st.expander("📖 供应链协同功能详解"):
    st.markdown("""
    ### 🔗 供应链协同管理系统

    #### 核心功能
    - **供应商管理**: 全生命周期供应商管理，包括评估、选择、绩效监控
    - **采购协同**: 智能采购计划，自动订单生成，实时状态跟踪
    - **库存协同**: VMI模式，智能补货，库存优化
    - **数据协同**: 实时数据共享，预测协同，计划协同

    #### 协同优势
    1. **降低成本**: 通过协同优化降低采购和库存成本
    2. **提升效率**: 自动化流程，减少人工干预
    3. **增强透明度**: 全链条可视化，实时状态监控
    4. **风险控制**: 多供应商策略，风险分散

    #### 技术特点
    - **API集成**: 与供应商系统无缝对接
    - **智能算法**: AI驱动的需求预测和库存优化
    - **实时协同**: 毫秒级数据同步
    - **安全保障**: 端到端加密，权限控制
    """)

# 实时状态更新
if st.button("🔄 刷新数据"):
    st.rerun()
