"""
安全相关功能
包括密码哈希、JWT令牌、加密等
"""

from datetime import datetime, timedelta
from typing import Any, Union, Optional
from passlib.context import CryptContext
from jose import JWTError, jwt
from cryptography.fernet import Fernet
import secrets
import hashlib
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# 密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 加密器
cipher_suite = Fernet(settings.ENCRYPTION_KEY.encode())


class PasswordManager:
    """密码管理器"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        try:
            return pwd_context.verify(plain_password, hashed_password)
        except Exception as e:
            logger.error(f"密码验证失败: {str(e)}")
            return False
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """生成密码哈希"""
        return pwd_context.hash(password)
    
    @staticmethod
    def generate_random_password(length: int = 12) -> str:
        """生成随机密码"""
        alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))


class TokenManager:
    """JWT令牌管理器"""
    
    @staticmethod
    def create_access_token(
        data: dict, 
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        
        to_encode.update({"exp": expire, "type": "access"})
        
        encoded_jwt = jwt.encode(
            to_encode, 
            settings.SECRET_KEY, 
            algorithm=settings.ALGORITHM
        )
        
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(data: dict) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        
        to_encode.update({"exp": expire, "type": "refresh"})
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> Optional[dict]:
        """验证令牌"""
        try:
            payload = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM]
            )
            
            # 检查令牌类型
            if payload.get("type") != token_type:
                return None
            
            return payload
            
        except JWTError as e:
            logger.warning(f"JWT验证失败: {str(e)}")
            return None
    
    @staticmethod
    def decode_token(token: str) -> Optional[dict]:
        """解码令牌（不验证）"""
        try:
            return jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM],
                options={"verify_exp": False}
            )
        except JWTError:
            return None


class DataEncryption:
    """数据加密器"""
    
    @staticmethod
    def encrypt(data: str) -> str:
        """加密数据"""
        try:
            encrypted_data = cipher_suite.encrypt(data.encode())
            return encrypted_data.decode()
        except Exception as e:
            logger.error(f"数据加密失败: {str(e)}")
            raise
    
    @staticmethod
    def decrypt(encrypted_data: str) -> str:
        """解密数据"""
        try:
            decrypted_data = cipher_suite.decrypt(encrypted_data.encode())
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"数据解密失败: {str(e)}")
            raise
    
    @staticmethod
    def encrypt_sensitive_fields(data: dict, fields: list) -> dict:
        """加密敏感字段"""
        encrypted_data = data.copy()
        
        for field in fields:
            if field in encrypted_data and encrypted_data[field]:
                encrypted_data[field] = DataEncryption.encrypt(str(encrypted_data[field]))
        
        return encrypted_data
    
    @staticmethod
    def decrypt_sensitive_fields(data: dict, fields: list) -> dict:
        """解密敏感字段"""
        decrypted_data = data.copy()
        
        for field in fields:
            if field in decrypted_data and decrypted_data[field]:
                try:
                    decrypted_data[field] = DataEncryption.decrypt(decrypted_data[field])
                except Exception:
                    # 如果解密失败，保持原值（可能未加密）
                    pass
        
        return decrypted_data


class SecurityUtils:
    """安全工具类"""
    
    @staticmethod
    def generate_api_key() -> str:
        """生成API密钥"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def generate_session_id() -> str:
        """生成会话ID"""
        return secrets.token_urlsafe(16)
    
    @staticmethod
    def hash_string(data: str, salt: str = "") -> str:
        """哈希字符串"""
        return hashlib.sha256((data + salt).encode()).hexdigest()
    
    @staticmethod
    def verify_hash(data: str, hash_value: str, salt: str = "") -> bool:
        """验证哈希"""
        return SecurityUtils.hash_string(data, salt) == hash_value
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名"""
        import re
        # 移除危险字符
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(sanitized) > 255:
            name, ext = sanitized.rsplit('.', 1) if '.' in sanitized else (sanitized, '')
            sanitized = name[:255-len(ext)-1] + '.' + ext if ext else name[:255]
        
        return sanitized
    
    @staticmethod
    def validate_file_type(filename: str, content_type: str) -> bool:
        """验证文件类型"""
        # 检查MIME类型
        if content_type not in settings.ALLOWED_FILE_TYPES:
            return False
        
        # 检查文件扩展名
        allowed_extensions = {
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
            'application/vnd.ms-excel': ['.xls'],
            'text/csv': ['.csv'],
            'message/rfc822': ['.eml'],
            'application/vnd.ms-outlook': ['.msg']
        }
        
        file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
        valid_extensions = []
        
        for mime_type, extensions in allowed_extensions.items():
            if content_type == mime_type:
                valid_extensions.extend(extensions)
        
        return f'.{file_ext}' in valid_extensions


# 全局实例
password_manager = PasswordManager()
token_manager = TokenManager()
data_encryption = DataEncryption()
security_utils = SecurityUtils()
