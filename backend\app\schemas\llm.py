"""
LLM相关的Pydantic模式
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, validator


class ChatRequest(BaseModel):
    """聊天请求模式"""
    message: str
    context: Optional[Dict[str, Any]] = None
    model: Optional[str] = None
    session_id: Optional[str] = None
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000
    
    @validator('message')
    def validate_message(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('消息内容不能为空')
        if len(v) > 10000:
            raise ValueError('消息内容过长，最多10000字符')
        return v.strip()
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if v is not None and (v < 0 or v > 2):
            raise ValueError('温度参数必须在0-2之间')
        return v
    
    @validator('max_tokens')
    def validate_max_tokens(cls, v):
        if v is not None and (v < 1 or v > 4000):
            raise ValueError('最大令牌数必须在1-4000之间')
        return v


class ChatMessage(BaseModel):
    """聊天消息模式"""
    id: str
    role: str  # user, assistant, system
    content: str
    timestamp: datetime
    model: Optional[str] = None
    tokens_used: Optional[int] = None
    
    class Config:
        from_attributes = True


class ChatResponse(BaseModel):
    """聊天响应模式"""
    success: bool
    message: str
    data: Dict[str, Any]


class ChatSession(BaseModel):
    """聊天会话模式"""
    id: str
    title: str
    created_at: datetime
    updated_at: datetime
    message_count: int
    total_tokens: int
    
    class Config:
        from_attributes = True


class ChatHistoryResponse(BaseModel):
    """聊天历史响应模式"""
    history: List[ChatMessage]
    total: int
    page: int
    page_size: int
    total_pages: int


class LLMConfig(BaseModel):
    """LLM配置模式"""
    default_service: str  # ollama, azure
    ollama_config: Dict[str, Any]
    azure_config: Dict[str, Any]
    default_model: str
    default_temperature: float
    default_max_tokens: int
    rate_limit: Dict[str, Any]


class LLMConfigUpdate(BaseModel):
    """LLM配置更新模式"""
    default_service: Optional[str] = None
    ollama_config: Optional[Dict[str, Any]] = None
    azure_config: Optional[Dict[str, Any]] = None
    default_model: Optional[str] = None
    default_temperature: Optional[float] = None
    default_max_tokens: Optional[int] = None
    rate_limit: Optional[Dict[str, Any]] = None
    
    @validator('default_service')
    def validate_default_service(cls, v):
        if v is not None and v not in ['ollama', 'azure']:
            raise ValueError('默认服务必须是 ollama 或 azure')
        return v
    
    @validator('default_temperature')
    def validate_temperature(cls, v):
        if v is not None and (v < 0 or v > 2):
            raise ValueError('默认温度参数必须在0-2之间')
        return v
    
    @validator('default_max_tokens')
    def validate_max_tokens(cls, v):
        if v is not None and (v < 1 or v > 4000):
            raise ValueError('默认最大令牌数必须在1-4000之间')
        return v


class LLMConfigResponse(BaseModel):
    """LLM配置响应模式"""
    success: bool
    data: LLMConfig


class ModelInfo(BaseModel):
    """模型信息模式"""
    name: str
    service: str  # ollama, azure
    description: Optional[str] = None
    max_tokens: int
    capabilities: List[str]
    status: str  # available, unavailable, loading


class AnalysisRequest(BaseModel):
    """数据分析请求模式"""
    data: Dict[str, Any]
    analysis_type: str
    context: Optional[Dict[str, Any]] = None
    
    @validator('analysis_type')
    def validate_analysis_type(cls, v):
        allowed_types = [
            'trend_analysis', 'performance_analysis', 'bottleneck_analysis',
            'efficiency_analysis', 'quality_analysis', 'cost_analysis'
        ]
        if v not in allowed_types:
            raise ValueError(f'分析类型必须是: {", ".join(allowed_types)}')
        return v


class AnalysisResult(BaseModel):
    """数据分析结果模式"""
    analysis_type: str
    summary: str
    insights: List[str]
    recommendations: List[str]
    charts: Optional[List[Dict[str, Any]]] = None
    confidence_score: Optional[float] = None
    data_quality: Optional[str] = None


class OptimizationSuggestion(BaseModel):
    """优化建议模式"""
    category: str
    priority: str  # high, medium, low
    title: str
    description: str
    expected_benefit: Optional[str] = None
    implementation_difficulty: Optional[str] = None
    estimated_impact: Optional[Dict[str, Any]] = None


class SuggestionRequest(BaseModel):
    """建议请求模式"""
    plan_data: Dict[str, Any]
    constraints: Optional[Dict[str, Any]] = None
    objectives: Optional[List[str]] = None
    context: Optional[Dict[str, Any]] = None


class SuggestionResponse(BaseModel):
    """建议响应模式"""
    suggestions: List[OptimizationSuggestion]
    overall_assessment: str
    key_metrics: Dict[str, Any]
    next_steps: List[str]


class LLMUsageStatistics(BaseModel):
    """LLM使用统计模式"""
    total_requests: int
    total_tokens: int
    average_response_time: float
    success_rate: float
    popular_models: List[Dict[str, Any]]
    usage_by_service: Dict[str, int]
    usage_by_user: List[Dict[str, Any]]
    daily_usage: List[Dict[str, Any]]
    error_statistics: Dict[str, int]


class PromptTemplate(BaseModel):
    """提示词模板模式"""
    id: str
    name: str
    category: str
    template: str
    variables: List[str]
    description: Optional[str] = None
    is_active: bool
    
    class Config:
        from_attributes = True


class PromptTemplateCreate(BaseModel):
    """提示词模板创建模式"""
    name: str
    category: str
    template: str
    variables: List[str]
    description: Optional[str] = None
    
    @validator('name')
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('模板名称不能为空')
        return v.strip()
    
    @validator('template')
    def validate_template(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('模板内容不能为空')
        return v.strip()


class PromptTemplateUpdate(BaseModel):
    """提示词模板更新模式"""
    name: Optional[str] = None
    category: Optional[str] = None
    template: Optional[str] = None
    variables: Optional[List[str]] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class ConversationContext(BaseModel):
    """对话上下文模式"""
    session_id: str
    user_id: str
    context_data: Dict[str, Any]
    last_updated: datetime
    
    class Config:
        from_attributes = True


class LLMServiceStatus(BaseModel):
    """LLM服务状态模式"""
    service_name: str
    status: str  # online, offline, error
    response_time: Optional[float] = None
    last_check: datetime
    error_message: Optional[str] = None
    available_models: List[str]
