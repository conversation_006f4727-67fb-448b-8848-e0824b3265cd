# 📊 Smart Planning 数据配置功能示例

## 🎯 概述

本文档提供Smart Planning系统数据配置功能的完整示例，包括数据源添加、数据设置、查询设置、自定义配置等核心功能的详细操作指南。

## 🗄️ 数据源添加示例

### 1. 添加MySQL数据库

```python
# 数据库连接配置
mysql_config = {
    "连接名称": "生产数据库",
    "数据库类型": "MySQL",
    "主机地址": "prod-db.company.com",
    "端口": 3306,
    "用户名": "production_user",
    "密码": "secure_password",
    "数据库名": "production_db",
    "描述": "生产环境主数据库，包含订单、物料、设备等核心数据"
}
```

**操作步骤：**
1. 进入"数据配置"页面 → "数据库连接"标签页
2. 填写连接信息
3. 点击"测试连接"验证
4. 点击"保存配置"完成添加

### 2. 添加SQL Server Analysis Services (SSAS)

```python
# SSAS连接配置
ssas_config = {
    "连接名称": "分析数据库",
    "数据库类型": "SQL Server Analysis Services",
    "主机地址": "ssas.company.com",
    "端口": 2383,
    "用户名": "domain\\analyst",
    "密码": "ssas_password",
    "数据库名": "ProductionCube",
    "立方体名称": "ProductionPlan",
    "描述": "多维数据分析服务，用于生产计划分析和报告"
}
```

### 3. 添加PPD数据源

```python
# PPD数据源配置
ppd_config = {
    "数据源名称": "ppd_data",
    "显示名称": "PPD数据",
    "描述": "生产计划数据(Production Planning Data)",
    "数据库类型": "sqlserver",
    "主机地址": "ppd-server.company.com",
    "端口": 1433,
    "数据库名": "ProductionPlanning",
    "字段定义": [
        {"name": "plan_id", "display": "计划编号", "type": "string", "required": True},
        {"name": "product_code", "display": "产品编码", "type": "string", "required": True},
        {"name": "planned_quantity", "display": "计划数量", "type": "integer", "required": True},
        {"name": "start_date", "display": "开始日期", "type": "date", "required": True},
        {"name": "end_date", "display": "结束日期", "type": "date", "required": True},
        {"name": "priority", "display": "优先级", "type": "integer", "required": False},
        {"name": "department", "display": "部门", "type": "string", "required": False}
    ]
}
```

**操作步骤：**
1. 进入"数据配置"页面 → "数据源管理"标签页
2. 填写数据源基本信息
3. 配置连接参数
4. 定义字段结构
5. 保存数据源类型

## 🔍 数据筛选设置示例

### 1. PCI数据精细化筛选

```python
# PCI箱子数据筛选配置
pci_filter_config = {
    "筛选器名称": "PCI_高优先级箱子筛选器",
    "容器类型": ["箱子"],                    # 只要箱子
    "最小库龄": 180,                        # 库龄大于180天
    "优先级阈值": 80,                       # 优先级大于80
    "特定FS ID": ["FS001", "FS002", "FS003"], # 指定FS ID
    "排除空箱": True,                       # 排除数量为0的箱子
    "返回字段": [                           # 只返回需要的字段
        "fs_id",
        "container_type", 
        "quantity",
        "days_old",
        "location"
    ]
}
```

**实际效果：**
- 只返回箱子类型的容器
- 库龄超过180天的物料
- 优先级高于80的物料
- 排除空箱
- 只返回指定的5个字段

### 2. 设备数据筛选

```python
# 设备L01数据筛选配置
equipment_filter_config = {
    "筛选器名称": "设备L01运行状态筛选器",
    "设备ID": ["L01"],                      # 只要L01设备
    "设备状态": ["运行中", "空闲"],          # 运行中和空闲状态
    "车间": ["A车间"],                      # A车间设备
    "利用率范围": [50, 100],                # 利用率50%-100%
    "返回字段": [
        "equipment_id",
        "status",
        "current_utilization",
        "capacity"
    ]
}
```

## 📝 SQL查询设置示例

### 1. MES生产订单查询

```sql
-- MES生产订单查询
SELECT 
    order_id,
    product_code,
    equipment_id,
    quantity,
    start_time,
    end_time,
    status,
    efficiency_rate
FROM production_orders 
WHERE equipment_id = :equipment_id
    AND start_time >= :start_date 
    AND status IN (:status_list)
ORDER BY start_time DESC
LIMIT :limit_count
```

**参数配置：**
```python
query_parameters = {
    "equipment_id": {"value": "L01", "type": "string"},
    "start_date": {"value": "2024-01-01", "type": "date"},
    "status_list": {"value": "completed,in_progress", "type": "string"},
    "limit_count": {"value": 100, "type": "integer"}
}
```

### 2. SSAS多维数据查询

```mdx
-- SSAS MDX查询示例
SELECT 
    [Measures].[Planned Quantity],
    [Measures].[Actual Quantity],
    [Measures].[Variance Percentage]
ON COLUMNS,
[Product].[Product Category].Members
ON ROWS
FROM [ProductionPlan]
WHERE [Time].[Year].&[:year_param]
    AND [Department].[Department Name].&[:department_param]
```

**参数配置：**
```python
mdx_parameters = {
    "year_param": {"value": "2024", "type": "string"},
    "department_param": {"value": "Production", "type": "string"}
}
```

### 3. PCI库存分析查询

```sql
-- PCI库存分析查询
SELECT 
    fs_id,
    material_code,
    container_type,
    quantity,
    location,
    receive_date,
    DATEDIFF(NOW(), receive_date) as days_old,
    CASE 
        WHEN DATEDIFF(NOW(), receive_date) > 180 THEN 'HIGH'
        WHEN DATEDIFF(NOW(), receive_date) > 90 THEN 'MEDIUM'
        ELSE 'LOW'
    END as priority_level
FROM fs_inventory 
WHERE location LIKE :location_pattern
    AND container_type = :container_type
    AND quantity > :min_quantity
    AND fs_id IN (:fs_id_list)
ORDER BY receive_date ASC
```

## 🎯 字段配置示例

### 1. 为PCI数据添加自定义字段

```python
# 添加生产批次字段
custom_field_1 = {
    "字段名称": "production_batch",
    "显示名称": "生产批次",
    "字段类型": "string",
    "默认值": "",
    "是否必需": False,
    "字段描述": "物料的生产批次号，用于追溯和质量管理",
    "验证规则": {
        "pattern": "^BATCH\\d{8}$",  # 格式：BATCH + 8位数字
        "max_length": 13
    }
}

# 添加质量等级字段
custom_field_2 = {
    "字段名称": "quality_grade",
    "显示名称": "质量等级",
    "字段类型": "string",
    "默认值": "A",
    "是否必需": True,
    "字段描述": "物料质量等级：A(优秀)、B(良好)、C(合格)、D(不合格)",
    "验证规则": {
        "enum": ["A", "B", "C", "D"]
    }
}

# 添加存储温度字段
custom_field_3 = {
    "字段名称": "storage_temperature",
    "显示名称": "存储温度",
    "字段类型": "float",
    "默认值": "25.0",
    "是否必需": False,
    "字段描述": "物料存储环境温度（摄氏度）",
    "验证规则": {
        "min_value": -20.0,
        "max_value": 60.0
    }
}
```

### 2. 为设备数据添加自定义字段

```python
# 添加维护计划字段
equipment_field_1 = {
    "字段名称": "maintenance_schedule",
    "显示名称": "维护计划",
    "字段类型": "date",
    "默认值": "",
    "是否必需": False,
    "字段描述": "设备下次维护计划日期"
}

# 添加能耗数据字段
equipment_field_2 = {
    "字段名称": "energy_consumption",
    "显示名称": "能耗数据",
    "字段类型": "json",
    "默认值": "{}",
    "是否必需": False,
    "字段描述": "设备能耗统计数据，JSON格式存储"
}
```

## ⚙️ 自定义配置设置示例

### 1. 数据源连接池配置

```python
# 连接池配置
connection_pool_config = {
    "最大连接数": 50,
    "最小连接数": 5,
    "连接超时": 30,  # 秒
    "空闲超时": 300, # 秒
    "重试次数": 3,
    "健康检查间隔": 60  # 秒
}
```

### 2. 数据缓存配置

```python
# 数据缓存配置
cache_config = {
    "启用缓存": True,
    "缓存类型": "Redis",
    "缓存过期时间": 3600,  # 秒
    "最大缓存大小": "1GB",
    "缓存键前缀": "smart_planning:",
    "压缩数据": True
}
```

### 3. 查询性能优化配置

```python
# 查询优化配置
query_optimization_config = {
    "启用查询缓存": True,
    "最大查询时间": 30,  # 秒
    "分页大小": 1000,
    "并发查询限制": 10,
    "慢查询阈值": 5,  # 秒
    "自动索引建议": True
}
```

## 🔧 实际使用场景示例

### 场景1：只获取PCI箱子信息用于FIFO分析

```python
# 配置筛选条件
pci_fifo_config = {
    "container_types": ["箱子"],
    "min_days_old": 180,  # 优先处理180天以上的物料
    "exclude_empty": True,
    "fields": ["fs_id", "material_code", "quantity", "days_old", "location"]
}

# 应用筛选
fifo_data = advanced_data_filter_service.get_filtered_pci_data(pci_fifo_config)

# 结果：只返回符合FIFO条件的箱子数据
```

### 场景2：获取特定设备的生产数据

```python
# 配置设备筛选
equipment_config = {
    "equipment_ids": ["L01"],
    "status_filter": ["运行中"],
    "fields": ["equipment_id", "current_utilization", "capacity"]
}

# 获取设备数据
equipment_data = advanced_data_filter_service.get_filtered_equipment_data(equipment_config)

# 结果：只返回L01设备的运行数据
```

### 场景3：执行自定义MES查询

```python
# 执行预定义的MES查询
mes_data = advanced_data_filter_service.execute_custom_sql_query(
    "MES生产订单查询",
    parameters={
        "equipment_id": "L01",
        "start_date": "2024-01-15",
        "status_list": "completed"
    }
)

# 结果：返回L01设备的已完成订单
```

## 📊 配置验证和测试

### 1. 连接测试

```python
# 测试数据库连接
test_result = extensible_data_source_service.test_connection(
    "mysql", 
    {
        "host": "prod-db.company.com",
        "port": 3306,
        "username": "user",
        "password": "pass",
        "database": "production_db"
    }
)

# 结果：{"success": True, "message": "连接测试成功"}
```

### 2. 配置验证

```python
# 验证配置完整性
validation_result = extensible_data_source_service.validate_connection_config(
    "mysql",
    mysql_config
)

# 结果：{"valid": True, "errors": []}
```

### 3. 查询测试

```python
# 测试SQL查询
query_result = advanced_data_filter_service.execute_custom_sql_query(
    "测试查询",
    {"limit": 10}
)

# 结果：返回查询数据或错误信息
```

## 💡 最佳实践建议

### 1. 数据源命名规范
- 使用有意义的名称：`生产数据库`、`分析数据库`
- 包含环境信息：`生产_MySQL`、`测试_SSAS`
- 避免特殊字符和空格

### 2. 字段配置建议
- 根据实际业务需求添加字段
- 设置合理的验证规则
- 提供清晰的字段描述
- 考虑字段的扩展性

### 3. 查询优化建议
- 使用参数化查询避免SQL注入
- 设置合理的查询超时时间
- 对大数据量查询使用分页
- 定期清理无用的查询配置

### 4. 安全配置建议
- 使用强密码和加密连接
- 定期更新数据库密码
- 限制数据库用户权限
- 启用连接日志记录

这些示例涵盖了Smart Planning系统数据配置的所有核心功能，可以根据实际需求进行调整和扩展。
