version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: smart_planning_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: smart_planning
      MYSQL_USER: smart_planning
      MYSQL_PASSWORD: smart_planning_password
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - smart_planning_network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: smart_planning_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - smart_planning_network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: smart_planning_backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=smart_planning
      - DB_PASSWORD=smart_planning_password
      - DB_NAME=smart_planning
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - DEBUG=false
    volumes:
      - ./backend:/app
      - uploads_data:/app/uploads
      - logs_data:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - smart_planning_network

  # Streamlit前端
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: smart_planning_frontend
    restart: unless-stopped
    ports:
      - "8501:8501"
    environment:
      - API_BASE_URL=http://backend:8000
    volumes:
      - ./frontend:/app
    depends_on:
      - backend
    networks:
      - smart_planning_network

  # Ollama LLM服务（可选）
  ollama:
    image: ollama/ollama:latest
    container_name: smart_planning_ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    networks:
      - smart_planning_network
    # 如果有GPU，取消注释以下配置
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  logs_data:
    driver: local
  ollama_data:
    driver: local

networks:
  smart_planning_network:
    driver: bridge
