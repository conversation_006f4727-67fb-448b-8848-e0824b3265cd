"""
数据源插件示例
展示如何创建自定义数据源插件
"""

import json
import requests
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional


class DataSourcePlugin:
    """数据源插件基类"""

    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.enabled = True

    def get_data_context(self) -> Dict[str, Any]:
        """获取数据上下文 - 子类必须实现"""
        raise NotImplementedError("子类必须实现get_data_context方法")

    def get_constraints(self) -> List[Dict[str, Any]]:
        """获取约束条件 - 可选实现"""
        return []

    def get_recommendations(self) -> List[str]:
        """获取建议 - 可选实现"""
        return []

    def is_available(self) -> bool:
        """检查数据源是否可用"""
        return self.enabled


class ERPDataSourcePlugin(DataSourcePlugin):
    """ERP系统数据源插件"""

    def __init__(self, api_endpoint: str, api_key: str):
        super().__init__("erp_system", "ERP系统数据源")
        self.api_endpoint = api_endpoint
        self.api_key = api_key

    def get_data_context(self) -> Dict[str, Any]:
        """获取ERP数据上下文"""
        try:
            # 模拟ERP API调用
            orders_data = self._fetch_orders()
            inventory_data = self._fetch_inventory()

            return {
                "orders": orders_data,
                "inventory": inventory_data,
                "last_sync": datetime.now().isoformat(),
                "status": "connected"
            }
        except Exception as e:
            return {
                "error": str(e),
                "status": "error",
                "last_sync": datetime.now().isoformat()
            }

    def _fetch_orders(self) -> List[Dict[str, Any]]:
        """获取订单数据"""
        # 模拟ERP订单数据
        return [
            {
                "order_id": "ERP001",
                "customer": "客户A",
                "product": "产品X",
                "quantity": 500,
                "due_date": "2024-01-20",
                "priority": "high",
                "status": "confirmed"
            },
            {
                "order_id": "ERP002",
                "customer": "客户B",
                "product": "产品Y",
                "quantity": 300,
                "due_date": "2024-01-25",
                "priority": "medium",
                "status": "pending"
            }
        ]

    def _fetch_inventory(self) -> List[Dict[str, Any]]:
        """获取库存数据"""
        # 模拟ERP库存数据
        return [
            {
                "material_code": "MAT001",
                "material_name": "原料A",
                "current_stock": 1500,
                "safety_stock": 200,
                "unit": "kg",
                "location": "仓库A"
            },
            {
                "material_code": "MAT002",
                "material_name": "原料B",
                "current_stock": 800,
                "safety_stock": 150,
                "unit": "kg",
                "location": "仓库B"
            }
        ]

    def get_constraints(self) -> List[Dict[str, Any]]:
        """获取ERP相关约束"""
        constraints = []

        # 检查库存约束
        inventory_data = self._fetch_inventory()
        for item in inventory_data:
            if item["current_stock"] <= item["safety_stock"]:
                constraints.append({
                    "type": "inventory_constraint",
                    "description": f"原料 {item['material_name']} 库存不足",
                    "impact": "可能影响生产计划",
                    "material": item["material_code"]
                })

        return constraints

    def get_recommendations(self) -> List[str]:
        """获取ERP相关建议"""
        recommendations = []

        # 检查高优先级订单
        orders_data = self._fetch_orders()
        high_priority_orders = [o for o in orders_data if o["priority"] == "high"]

        if high_priority_orders:
            recommendations.append(f"有{len(high_priority_orders)}个高优先级订单需要优先安排")

        return recommendations


class IoTSensorPlugin(DataSourcePlugin):
    """IoT传感器数据源插件"""

    def __init__(self, sensor_config: Dict[str, Any]):
        super().__init__("iot_sensors", "IoT传感器数据源")
        self.sensor_config = sensor_config

    def get_data_context(self) -> Dict[str, Any]:
        """获取传感器数据上下文"""
        try:
            sensor_readings = self._collect_sensor_data()

            return {
                "sensors": sensor_readings,
                "environmental_data": self._get_environmental_data(),
                "equipment_monitoring": self._get_equipment_monitoring(),
                "last_update": datetime.now().isoformat(),
                "status": "online"
            }
        except Exception as e:
            return {
                "error": str(e),
                "status": "offline",
                "last_update": datetime.now().isoformat()
            }

    def _collect_sensor_data(self) -> List[Dict[str, Any]]:
        """收集传感器数据"""
        # 模拟传感器数据
        return [
            {
                "sensor_id": "TEMP001",
                "sensor_type": "temperature",
                "location": "生产线L01",
                "value": 25.5,
                "unit": "°C",
                "timestamp": datetime.now().isoformat(),
                "status": "normal"
            },
            {
                "sensor_id": "VIBR001",
                "sensor_type": "vibration",
                "location": "设备M01",
                "value": 2.3,
                "unit": "mm/s",
                "timestamp": datetime.now().isoformat(),
                "status": "warning"
            },
            {
                "sensor_id": "PRES001",
                "sensor_type": "pressure",
                "location": "Tank01",
                "value": 0.35,
                "unit": "MPa",
                "timestamp": datetime.now().isoformat(),
                "status": "normal"
            }
        ]

    def _get_environmental_data(self) -> Dict[str, Any]:
        """获取环境数据"""
        return {
            "temperature": 22.5,
            "humidity": 65.2,
            "air_quality": "good",
            "timestamp": datetime.now().isoformat()
        }

    def _get_equipment_monitoring(self) -> List[Dict[str, Any]]:
        """获取设备监控数据"""
        return [
            {
                "equipment_id": "L01",
                "temperature": 45.2,
                "vibration": 1.8,
                "power_consumption": 85.5,
                "efficiency": 92.3,
                "status": "normal"
            },
            {
                "equipment_id": "L02",
                "temperature": 0.0,
                "vibration": 0.0,
                "power_consumption": 0.0,
                "efficiency": 0.0,
                "status": "stopped"
            }
        ]

    def get_constraints(self) -> List[Dict[str, Any]]:
        """获取传感器相关约束"""
        constraints = []

        sensor_data = self._collect_sensor_data()
        for sensor in sensor_data:
            if sensor["status"] == "warning":
                constraints.append({
                    "type": "sensor_warning",
                    "description": f"传感器 {sensor['sensor_id']} 检测到异常",
                    "impact": "可能影响设备运行",
                    "sensor_id": sensor["sensor_id"],
                    "location": sensor["location"]
                })

        return constraints


class QualityDataPlugin(DataSourcePlugin):
    """质量数据源插件"""

    def __init__(self, quality_db_path: str = "quality_data.db"):
        super().__init__("quality_system", "质量管理系统数据源")
        self.db_path = quality_db_path
        self._init_quality_db()

    def _init_quality_db(self):
        """初始化质量数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quality_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_id TEXT NOT NULL,
                product_code TEXT NOT NULL,
                test_date DATE NOT NULL,
                test_type TEXT NOT NULL,
                test_result REAL NOT NULL,
                pass_status INTEGER NOT NULL,
                operator TEXT,
                equipment_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 插入示例数据
        sample_data = [
            ("BATCH001", "PROD_A", "2024-01-15", "dimensional", 99.2, 1, "张三", "L01"),
            ("BATCH002", "PROD_B", "2024-01-15", "strength", 87.5, 0, "李四", "L02"),
            ("BATCH003", "PROD_A", "2024-01-16", "dimensional", 98.8, 1, "王五", "L01"),
        ]

        cursor.executemany('''
            INSERT OR IGNORE INTO quality_records
            (batch_id, product_code, test_date, test_type, test_result, pass_status, operator, equipment_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', sample_data)

        conn.commit()
        conn.close()

    def get_data_context(self) -> Dict[str, Any]:
        """获取质量数据上下文"""
        try:
            quality_stats = self._get_quality_statistics()
            recent_tests = self._get_recent_test_results()

            return {
                "quality_statistics": quality_stats,
                "recent_tests": recent_tests,
                "quality_trends": self._get_quality_trends(),
                "last_update": datetime.now().isoformat(),
                "status": "active"
            }
        except Exception as e:
            return {
                "error": str(e),
                "status": "error",
                "last_update": datetime.now().isoformat()
            }

    def _get_quality_statistics(self) -> Dict[str, Any]:
        """获取质量统计"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 总体合格率
        cursor.execute('''
            SELECT
                COUNT(*) as total_tests,
                SUM(pass_status) as passed_tests,
                AVG(test_result) as avg_result
            FROM quality_records
            WHERE test_date >= date('now', '-7 days')
        ''')

        result = cursor.fetchone()
        total_tests, passed_tests, avg_result = result

        pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        conn.close()

        return {
            "total_tests": total_tests,
            "pass_rate": round(pass_rate, 1),
            "average_result": round(avg_result, 2) if avg_result else 0,
            "period": "最近7天"
        }

    def _get_recent_test_results(self) -> List[Dict[str, Any]]:
        """获取最近测试结果"""
        conn = sqlite3.connect(self.db_path)
        df = pd.read_sql_query('''
            SELECT * FROM quality_records
            ORDER BY created_at DESC
            LIMIT 10
        ''', conn)
        conn.close()

        return df.to_dict('records')

    def _get_quality_trends(self) -> Dict[str, Any]:
        """获取质量趋势"""
        # 模拟质量趋势数据
        return {
            "trend_direction": "improving",
            "improvement_rate": 2.5,
            "critical_issues": 0,
            "recommendations": ["继续保持当前质量标准"]
        }

    def get_constraints(self) -> List[Dict[str, Any]]:
        """获取质量相关约束"""
        constraints = []

        quality_stats = self._get_quality_statistics()
        if quality_stats["pass_rate"] < 95:
            constraints.append({
                "type": "quality_constraint",
                "description": f"质量合格率仅为{quality_stats['pass_rate']}%，低于标准",
                "impact": "需要加强质量控制",
                "pass_rate": quality_stats["pass_rate"]
            })

        return constraints

    def get_recommendations(self) -> List[str]:
        """获取质量相关建议"""
        recommendations = []

        quality_stats = self._get_quality_statistics()
        if quality_stats["pass_rate"] < 90:
            recommendations.append("质量合格率偏低，建议检查生产工艺")
        elif quality_stats["pass_rate"] > 98:
            recommendations.append("质量表现优秀，可以考虑优化成本")

        return recommendations


# 示例：如何注册和使用插件
def register_example_plugins(data_integration_service):
    """注册示例插件"""

    # 注册ERP插件
    erp_plugin = ERPDataSourcePlugin(
        api_endpoint="http://erp.company.com/api",
        api_key="your_api_key"
    )
    data_integration_service.register_data_source_plugin(erp_plugin)

    # 注册IoT传感器插件
    iot_plugin = IoTSensorPlugin({
        "sensors": ["TEMP001", "VIBR001", "PRES001"],
        "update_interval": 60
    })
    data_integration_service.register_data_source_plugin(iot_plugin)

    # 注册质量数据插件
    quality_plugin = QualityDataPlugin()
    data_integration_service.register_data_source_plugin(quality_plugin)

    # 添加自定义数据源配置
    data_integration_service.add_custom_data_source(
        source_name="external_weather",
        source_type="api",
        config={
            "endpoint": "http://api.weather.com/v1/current",
            "api_key": "weather_api_key",
            "location": "factory_location"
        }
    )

    data_integration_service.add_custom_data_source(
        source_name="maintenance_logs",
        source_type="file",
        config={
            "file_path": "/data/maintenance/logs.csv",
            "format": "csv",
            "update_frequency": "daily"
        }
    )
