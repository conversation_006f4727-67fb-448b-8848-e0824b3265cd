"""
系统管理API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db, db_manager
from app.core.redis import redis_manager
from app.core.config import settings
from app.core.logging_config import business_logger
from app.services.system_service import SystemService
from app.api.dependencies import (
    get_current_user, require_permission, get_current_superuser
)

router = APIRouter()


@router.get("/status", summary="获取系统状态")
async def get_system_status(
    current_user: dict = Depends(require_permission("system.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取系统状态"""
    try:
        system_service = SystemService(db)
        
        # 获取系统状态
        status_info = await system_service.get_system_status()
        
        return {
            "success": True,
            "data": status_info
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统状态失败"
        )


@router.get("/health", summary="健康检查")
async def health_check():
    """系统健康检查"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": settings.get_current_timestamp(),
            "version": settings.VERSION,
            "environment": "development" if settings.DEBUG else "production"
        }
        
        # 检查数据库连接
        try:
            db_healthy = await db_manager.health_check()
            health_status["database"] = "healthy" if db_healthy else "unhealthy"
        except Exception:
            health_status["database"] = "unhealthy"
        
        # 检查Redis连接
        try:
            redis_healthy = await redis_manager.health_check()
            health_status["redis"] = "healthy" if redis_healthy else "unhealthy"
        except Exception:
            health_status["redis"] = "unhealthy"
        
        # 判断整体状态
        if health_status["database"] == "unhealthy" or health_status["redis"] == "unhealthy":
            health_status["status"] = "degraded"
        
        return health_status
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": settings.get_current_timestamp(),
            "error": str(e)
        }


@router.get("/info", summary="获取系统信息")
async def get_system_info(
    current_user: dict = Depends(require_permission("system.view"))
):
    """获取系统信息"""
    try:
        system_info = {
            "application": {
                "name": settings.PROJECT_NAME,
                "version": settings.VERSION,
                "environment": "development" if settings.DEBUG else "production",
                "api_version": settings.API_V1_STR
            },
            "configuration": {
                "debug_mode": settings.DEBUG,
                "max_upload_size": f"{settings.MAX_UPLOAD_SIZE / 1024 / 1024:.0f}MB",
                "default_llm_service": settings.DEFAULT_LLM_SERVICE,
                "cache_ttl": f"{settings.CACHE_TTL}秒",
                "rate_limit": f"{settings.RATE_LIMIT_REQUESTS}次/{settings.RATE_LIMIT_WINDOW}秒"
            },
            "features": {
                "file_upload": True,
                "production_planning": True,
                "llm_integration": True,
                "equipment_management": True,
                "user_management": True
            }
        }
        
        return {
            "success": True,
            "data": system_info
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统信息失败"
        )


@router.get("/metrics", summary="获取系统指标")
async def get_system_metrics(
    current_user: dict = Depends(require_permission("system.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取系统指标"""
    try:
        system_service = SystemService(db)
        metrics = await system_service.get_system_metrics()
        
        return {
            "success": True,
            "data": metrics
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统指标失败"
        )


@router.get("/logs", summary="获取系统日志")
async def get_system_logs(
    level: Optional[str] = Query(None, description="日志级别"),
    limit: int = Query(100, ge=1, le=1000, description="日志条数"),
    current_user: dict = Depends(require_permission("system.logs")),
    db: AsyncSession = Depends(get_db)
):
    """获取系统日志"""
    try:
        system_service = SystemService(db)
        logs = await system_service.get_system_logs(level=level, limit=limit)
        
        return {
            "success": True,
            "data": {
                "logs": logs,
                "total": len(logs)
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统日志失败"
        )


@router.post("/cache/clear", summary="清理缓存")
async def clear_cache(
    cache_type: Optional[str] = Query(None, description="缓存类型"),
    current_user: dict = Depends(require_permission("system.config")),
    db: AsyncSession = Depends(get_db)
):
    """清理系统缓存"""
    try:
        system_service = SystemService(db)
        
        if cache_type == "redis":
            # 清理Redis缓存
            cleared_count = await redis_manager.clear_cache()
            cache_info = {"type": "redis", "cleared_keys": cleared_count}
        elif cache_type == "all":
            # 清理所有缓存
            redis_count = await redis_manager.clear_cache()
            cache_info = {"type": "all", "redis_keys": redis_count}
        else:
            # 默认清理Redis缓存
            cleared_count = await redis_manager.clear_cache()
            cache_info = {"type": "redis", "cleared_keys": cleared_count}
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="cache_clear",
            details=cache_info
        )
        
        return {
            "success": True,
            "message": "缓存清理成功",
            "data": cache_info
        }
        
    except Exception as e:
        business_logger.log_error(
            error_type="cache_clear_error",
            error_message=str(e),
            details={"user_id": current_user["id"]}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清理缓存失败"
        )


@router.get("/database/info", summary="获取数据库信息")
async def get_database_info(
    current_user: dict = Depends(require_permission("system.view"))
):
    """获取数据库信息"""
    try:
        # 获取数据库连接信息
        connection_info = await db_manager.get_connection_info()
        
        database_info = {
            "connection_pool": connection_info,
            "database_url": settings.DATABASE_URL.split('@')[1] if '@' in settings.DATABASE_URL else "隐藏",
            "health_status": await db_manager.health_check()
        }
        
        return {
            "success": True,
            "data": database_info
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取数据库信息失败"
        )


@router.get("/redis/info", summary="获取Redis信息")
async def get_redis_info(
    current_user: dict = Depends(require_permission("system.view"))
):
    """获取Redis信息"""
    try:
        redis_info = await redis_manager.get_info()
        redis_info["health_status"] = await redis_manager.health_check()
        
        return {
            "success": True,
            "data": redis_info
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取Redis信息失败"
        )


@router.post("/backup", summary="创建系统备份")
async def create_system_backup(
    backup_type: str = Query("full", description="备份类型: full, data, config"),
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db)
):
    """创建系统备份"""
    try:
        system_service = SystemService(db)
        
        backup_info = await system_service.create_backup(
            backup_type=backup_type,
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="system_backup",
            details={"backup_type": backup_type, "backup_id": backup_info.get("backup_id")}
        )
        
        return {
            "success": True,
            "message": "系统备份已创建",
            "data": backup_info
        }
        
    except Exception as e:
        business_logger.log_error(
            error_type="system_backup_error",
            error_message=str(e),
            details={"user_id": current_user["id"], "backup_type": backup_type}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建系统备份失败"
        )


@router.get("/config", summary="获取系统配置")
async def get_system_config(
    current_user: dict = Depends(require_permission("system.config"))
):
    """获取系统配置"""
    try:
        # 返回非敏感的配置信息
        config_info = {
            "application": {
                "project_name": settings.PROJECT_NAME,
                "version": settings.VERSION,
                "debug": settings.DEBUG,
                "api_version": settings.API_V1_STR
            },
            "upload": {
                "max_upload_size": settings.MAX_UPLOAD_SIZE,
                "upload_dir": settings.UPLOAD_DIR,
                "allowed_file_types": settings.ALLOWED_FILE_TYPES
            },
            "llm": {
                "default_service": settings.DEFAULT_LLM_SERVICE,
                "ollama_base_url": settings.OLLAMA_BASE_URL,
                "ollama_model": settings.OLLAMA_MODEL
            },
            "cache": {
                "cache_ttl": settings.CACHE_TTL,
                "cache_max_size": settings.CACHE_MAX_SIZE
            },
            "security": {
                "rate_limit_requests": settings.RATE_LIMIT_REQUESTS,
                "rate_limit_window": settings.RATE_LIMIT_WINDOW,
                "access_token_expire_minutes": settings.ACCESS_TOKEN_EXPIRE_MINUTES
            }
        }
        
        return {
            "success": True,
            "data": config_info
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统配置失败"
        )


@router.post("/maintenance", summary="系统维护模式")
async def toggle_maintenance_mode(
    enable: bool = Query(description="启用维护模式"),
    message: Optional[str] = Query(None, description="维护消息"),
    current_user: dict = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db)
):
    """切换系统维护模式"""
    try:
        system_service = SystemService(db)
        
        result = await system_service.toggle_maintenance_mode(
            enable=enable,
            message=message,
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="maintenance_mode_toggle",
            details={"enable": enable, "message": message}
        )
        
        return {
            "success": True,
            "message": f"维护模式已{'启用' if enable else '禁用'}",
            "data": result
        }
        
    except Exception as e:
        business_logger.log_error(
            error_type="maintenance_mode_error",
            error_message=str(e),
            details={"user_id": current_user["id"], "enable": enable}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="切换维护模式失败"
        )
