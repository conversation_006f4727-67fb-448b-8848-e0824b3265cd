# 智能工厂生产管理规划系统 - Streamlit前端开发指南

## Streamlit技术栈选择

### 为什么选择Streamlit

1. **快速开发**：Python原生，无需前后端分离
2. **丰富图表**：内置Plotly、Altair等多种图表库
3. **交互性强**：支持实时数据更新和用户交互
4. **部署简单**：一键部署，适合小团队
5. **学习成本低**：Python开发者可快速上手

### 技术栈组合

```python
# 核心框架
streamlit >= 1.28.0

# 数据可视化
plotly >= 5.17.0
altair >= 5.1.0
matplotlib >= 3.7.0

# 数据处理
pandas >= 2.0.0
numpy >= 1.24.0

# UI增强
streamlit-aggrid >= 0.3.4  # 高级表格
streamlit-option-menu >= 0.3.6  # 导航菜单
streamlit-elements >= 0.1.0  # 自定义组件

# 文件处理
openpyxl >= 3.1.0  # Excel处理
python-multipart >= 0.0.6  # 文件上传
email-parser >= 0.2.0  # 邮件解析
```

## 项目结构设计

```
frontend/
├── main.py                 # 主应用入口
├── config/
│   ├── __init__.py
│   ├── settings.py         # 配置管理
│   └── theme.py           # 主题配置
├── pages/
│   ├── __init__.py
│   ├── 01_数据上传.py      # 文件上传页面
│   ├── 02_生产规划.py      # 生产规划页面
│   ├── 03_计划监控.py      # 计划监控页面
│   ├── 04_数据分析.py      # 数据分析页面
│   └── 05_系统设置.py      # 系统设置页面
├── components/
│   ├── __init__.py
│   ├── charts.py          # 图表组件
│   ├── tables.py          # 表格组件
│   ├── forms.py           # 表单组件
│   └── file_upload.py     # 文件上传组件
├── utils/
│   ├── __init__.py
│   ├── api_client.py      # API客户端
│   ├── data_processor.py  # 数据处理
│   └── validators.py      # 数据验证
├── assets/
│   ├── styles.css         # 自定义样式
│   └── images/           # 图片资源
└── requirements.txt       # 依赖列表
```

## 核心组件开发

### 1. 主应用入口 (main.py)

```python
import streamlit as st
from config.settings import AppConfig
from config.theme import apply_custom_theme
from utils.api_client import APIClient

# 页面配置
st.set_page_config(
    page_title="智能工厂生产管理规划系统",
    page_icon="🏭",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 应用自定义主题
apply_custom_theme()

# 初始化API客户端
if 'api_client' not in st.session_state:
    st.session_state.api_client = APIClient(AppConfig.API_BASE_URL)

# 用户认证
def check_authentication():
    """检查用户认证状态"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    
    if not st.session_state.authenticated:
        show_login_page()
        return False
    return True

def show_login_page():
    """显示登录页面"""
    st.title("🏭 智能工厂生产管理规划系统")
    
    with st.form("login_form"):
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col2:
            st.subheader("用户登录")
            username = st.text_input("用户名")
            password = st.text_input("密码", type="password")
            
            if st.form_submit_button("登录", use_container_width=True):
                if authenticate_user(username, password):
                    st.session_state.authenticated = True
                    st.session_state.username = username
                    st.rerun()
                else:
                    st.error("用户名或密码错误")

def authenticate_user(username: str, password: str) -> bool:
    """用户认证"""
    try:
        response = st.session_state.api_client.login(username, password)
        if response.get('success'):
            st.session_state.token = response['data']['access_token']
            st.session_state.user_info = response['data']['user_info']
            return True
    except Exception as e:
        st.error(f"登录失败: {str(e)}")
    return False

def main():
    """主应用"""
    if not check_authentication():
        return
    
    # 侧边栏导航
    with st.sidebar:
        st.title("📊 导航菜单")
        
        # 用户信息
        st.info(f"欢迎，{st.session_state.get('username', '用户')}！")
        
        # 导航菜单
        pages = {
            "📁 数据上传": "pages/01_数据上传.py",
            "📋 生产规划": "pages/02_生产规划.py", 
            "📈 计划监控": "pages/03_计划监控.py",
            "📊 数据分析": "pages/04_数据分析.py",
            "⚙️ 系统设置": "pages/05_系统设置.py"
        }
        
        # 退出登录
        if st.button("退出登录", use_container_width=True):
            for key in list(st.session_state.keys()):
                del st.session_state[key]
            st.rerun()
    
    # 主页面内容
    st.title("🏭 智能工厂生产管理规划系统")
    
    # 系统概览
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("今日订单", "25", "↑ 3")
    
    with col2:
        st.metric("设备利用率", "85%", "↑ 2%")
    
    with col3:
        st.metric("计划完成率", "92%", "↑ 5%")
    
    with col4:
        st.metric("在线用户", "8", "→ 0")
    
    # 快速操作
    st.subheader("🚀 快速操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📁 上传数据文件", use_container_width=True):
            st.switch_page("pages/01_数据上传.py")
    
    with col2:
        if st.button("📋 生成生产计划", use_container_width=True):
            st.switch_page("pages/02_生产规划.py")
    
    with col3:
        if st.button("📈 查看计划监控", use_container_width=True):
            st.switch_page("pages/03_计划监控.py")

if __name__ == "__main__":
    main()
```

### 2. 文件上传页面 (pages/01_数据上传.py)

```python
import streamlit as st
import pandas as pd
from components.file_upload import FileUploadComponent
from utils.data_processor import DataProcessor
from utils.validators import FileValidator

st.set_page_config(page_title="数据上传", page_icon="📁", layout="wide")

st.title("📁 数据上传与处理")

# 文件上传组件
upload_component = FileUploadComponent()
data_processor = DataProcessor()
validator = FileValidator()

# 选择上传类型
upload_type = st.selectbox(
    "选择数据类型",
    ["Excel订单数据", "邮件附件", "CSV库存数据", "设备状态数据"]
)

# 文件上传区域
uploaded_file = st.file_uploader(
    "选择文件",
    type=['xlsx', 'xls', 'csv', 'eml', 'msg'],
    help="支持Excel、CSV和邮件文件格式"
)

if uploaded_file is not None:
    # 文件信息显示
    file_details = {
        "文件名": uploaded_file.name,
        "文件大小": f"{uploaded_file.size / 1024:.2f} KB",
        "文件类型": uploaded_file.type
    }
    
    st.subheader("📄 文件信息")
    st.json(file_details)
    
    # 文件验证
    validation_result = validator.validate_file(uploaded_file)
    
    if validation_result['valid']:
        st.success("✅ 文件验证通过")
        
        # 处理不同类型的文件
        if upload_type == "Excel订单数据":
            process_excel_orders(uploaded_file)
        elif upload_type == "邮件附件":
            process_email_attachment(uploaded_file)
        elif upload_type == "CSV库存数据":
            process_csv_inventory(uploaded_file)
        elif upload_type == "设备状态数据":
            process_equipment_data(uploaded_file)
    else:
        st.error(f"❌ 文件验证失败: {validation_result['error']}")

def process_excel_orders(file):
    """处理Excel订单数据"""
    st.subheader("📊 Excel数据预览")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file)
        
        # 数据预览
        st.dataframe(df.head(10), use_container_width=True)
        
        # 数据统计
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("总行数", len(df))
            st.metric("总列数", len(df.columns))
        
        with col2:
            st.metric("空值数量", df.isnull().sum().sum())
            st.metric("重复行数", df.duplicated().sum())
        
        # 列映射配置
        st.subheader("🔗 字段映射配置")
        
        required_fields = ["订单号", "产品名称", "数量", "交期"]
        field_mapping = {}
        
        for field in required_fields:
            field_mapping[field] = st.selectbox(
                f"选择 '{field}' 对应的列",
                [""] + list(df.columns),
                key=f"mapping_{field}"
            )
        
        # 数据处理和保存
        if st.button("💾 处理并保存数据", use_container_width=True):
            if all(field_mapping.values()):
                processed_data = data_processor.process_excel_orders(df, field_mapping)
                
                # 调用API保存数据
                result = st.session_state.api_client.save_order_data(processed_data)
                
                if result.get('success'):
                    st.success(f"✅ 成功处理 {len(processed_data)} 条订单数据")
                    
                    # 显示处理结果
                    st.subheader("📈 处理结果")
                    st.dataframe(processed_data, use_container_width=True)
                else:
                    st.error(f"❌ 数据保存失败: {result.get('message')}")
            else:
                st.warning("⚠️ 请完成所有字段映射")
                
    except Exception as e:
        st.error(f"❌ 文件处理失败: {str(e)}")

def process_email_attachment(file):
    """处理邮件附件"""
    st.subheader("📧 邮件内容解析")
    
    try:
        # 解析邮件
        email_data = data_processor.parse_email(file)
        
        # 显示邮件信息
        col1, col2 = st.columns(2)
        
        with col1:
            st.text_input("发件人", email_data.get('sender', ''), disabled=True)
            st.text_input("主题", email_data.get('subject', ''), disabled=True)
        
        with col2:
            st.text_input("收件人", email_data.get('recipient', ''), disabled=True)
            st.text_input("发送时间", email_data.get('date', ''), disabled=True)
        
        # 提取的表格数据
        if email_data.get('tables'):
            st.subheader("📊 提取的表格数据")
            
            for i, table in enumerate(email_data['tables']):
                st.write(f"**表格 {i+1}**")
                st.dataframe(table, use_container_width=True)
                
                # 表格类型识别
                table_type = st.selectbox(
                    f"表格 {i+1} 数据类型",
                    ["订单数据", "库存数据", "设备数据", "其他"],
                    key=f"table_type_{i}"
                )
                
                if st.button(f"保存表格 {i+1}", key=f"save_table_{i}"):
                    # 保存表格数据
                    result = data_processor.save_table_data(table, table_type)
                    if result:
                        st.success(f"✅ 表格 {i+1} 保存成功")
        else:
            st.info("📝 邮件中未发现表格数据")
            
    except Exception as e:
        st.error(f"❌ 邮件解析失败: {str(e)}")

# 历史上传记录
st.subheader("📚 历史上传记录")

# 获取上传历史
upload_history = st.session_state.api_client.get_upload_history()

if upload_history.get('success'):
    history_df = pd.DataFrame(upload_history['data'])
    
    if not history_df.empty:
        # 添加操作列
        history_df['操作'] = history_df.apply(
            lambda row: f"[查看详情](#{row['id']})", axis=1
        )
        
        st.dataframe(
            history_df[['文件名', '上传时间', '文件类型', '状态', '操作']],
            use_container_width=True
        )
    else:
        st.info("📝 暂无上传记录")
else:
    st.error("❌ 获取上传历史失败")
```

### 3. 交互式图表组件 (components/charts.py)

```python
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import altair as alt

class ChartComponents:
    """图表组件类"""
    
    @staticmethod
    def gantt_chart(data: pd.DataFrame, title: str = "生产计划甘特图"):
        """生产计划甘特图"""
        fig = px.timeline(
            data,
            x_start="开始时间",
            x_end="结束时间", 
            y="设备",
            color="产品",
            title=title,
            hover_data=["订单号", "数量"]
        )
        
        fig.update_layout(
            height=600,
            xaxis_title="时间",
            yaxis_title="设备",
            showlegend=True
        )
        
        return fig
    
    @staticmethod
    def equipment_utilization_chart(data: pd.DataFrame):
        """设备利用率图表"""
        fig = go.Figure()
        
        # 添加利用率柱状图
        fig.add_trace(go.Bar(
            x=data['设备名称'],
            y=data['利用率'],
            name='当前利用率',
            marker_color='lightblue'
        ))
        
        # 添加目标线
        fig.add_hline(
            y=0.85, 
            line_dash="dash", 
            line_color="red",
            annotation_text="目标利用率 85%"
        )
        
        fig.update_layout(
            title="设备利用率分析",
            xaxis_title="设备",
            yaxis_title="利用率 (%)",
            yaxis=dict(range=[0, 1])
        )
        
        return fig
    
    @staticmethod
    def production_trend_chart(data: pd.DataFrame):
        """生产趋势图"""
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('生产数量趋势', '设备OEE趋势'),
            vertical_spacing=0.1
        )
        
        # 生产数量趋势
        fig.add_trace(
            go.Scatter(
                x=data['日期'],
                y=data['生产数量'],
                mode='lines+markers',
                name='生产数量',
                line=dict(color='blue')
            ),
            row=1, col=1
        )
        
        # OEE趋势
        fig.add_trace(
            go.Scatter(
                x=data['日期'],
                y=data['OEE'],
                mode='lines+markers',
                name='OEE',
                line=dict(color='green')
            ),
            row=2, col=1
        )
        
        fig.update_layout(
            height=600,
            title_text="生产趋势分析",
            showlegend=True
        )
        
        return fig
    
    @staticmethod
    def interactive_scatter_plot(data: pd.DataFrame):
        """交互式散点图"""
        chart = alt.Chart(data).mark_circle(size=100).add_selection(
            alt.selection_interval()
        ).encode(
            x=alt.X('利用率:Q', title='设备利用率'),
            y=alt.Y('OEE:Q', title='设备OEE'),
            color=alt.Color('设备类型:N', title='设备类型'),
            tooltip=['设备名称:N', '利用率:Q', 'OEE:Q', '设备类型:N']
        ).properties(
            width=600,
            height=400,
            title="设备性能分析"
        ).interactive()
        
        return chart
    
    @staticmethod
    def kpi_dashboard(kpi_data: dict):
        """KPI仪表盘"""
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=list(kpi_data.keys()),
            specs=[[{"type": "indicator"}, {"type": "indicator"}],
                   [{"type": "indicator"}, {"type": "indicator"}]]
        )
        
        positions = [(1, 1), (1, 2), (2, 1), (2, 2)]
        
        for i, (kpi_name, kpi_value) in enumerate(kpi_data.items()):
            row, col = positions[i]
            
            fig.add_trace(
                go.Indicator(
                    mode="gauge+number+delta",
                    value=kpi_value['current'],
                    delta={'reference': kpi_value['target']},
                    gauge={
                        'axis': {'range': [None, kpi_value['max']]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, kpi_value['target']*0.8], 'color': "lightgray"},
                            {'range': [kpi_value['target']*0.8, kpi_value['target']], 'color': "gray"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': kpi_value['target']
                        }
                    },
                    title={'text': kpi_name}
                ),
                row=row, col=col
            )
        
        fig.update_layout(height=600, title_text="关键绩效指标")
        
        return fig
```

这个Streamlit前端开发指南提供了完整的前端架构设计、核心组件开发和交互式图表实现。
