"""
用户相关模型
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from datetime import datetime

from app.models.base import BaseModel, UserTrackingMixin, AuditLogMixin


# 用户角色关联表
user_role_association = Table(
    'user_role_association',
    BaseModel.metadata,
    Column('user_id', String(50), ForeignKey('user.id'), primary_key=True),
    Column('role_id', String(50), ForeignKey('role.id'), primary_key=True),
    comment="用户角色关联表"
)


class User(BaseModel, UserTrackingMixin):
    """用户模型"""
    
    __tablename__ = "user"
    __table_args__ = {'comment': '用户表'}
    
    # 基本信息
    username = Column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        comment="用户名"
    )
    
    email = Column(
        String(100),
        unique=True,
        nullable=False,
        index=True,
        comment="邮箱"
    )
    
    full_name = Column(
        String(100),
        nullable=False,
        comment="姓名"
    )
    
    password_hash = Column(
        String(255),
        nullable=False,
        comment="密码哈希"
    )
    
    # 状态信息
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    is_superuser = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否超级用户"
    )
    
    # 扩展信息
    phone = Column(
        String(20),
        nullable=True,
        comment="电话号码"
    )
    
    department = Column(
        String(100),
        nullable=True,
        comment="部门"
    )
    
    position = Column(
        String(100),
        nullable=True,
        comment="职位"
    )
    
    avatar_url = Column(
        String(500),
        nullable=True,
        comment="头像URL"
    )
    
    # 登录信息
    last_login_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后登录时间"
    )
    
    last_login_ip = Column(
        String(45),
        nullable=True,
        comment="最后登录IP"
    )
    
    login_count = Column(
        String(10),
        default="0",
        comment="登录次数"
    )
    
    # 安全信息
    password_changed_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        comment="密码修改时间"
    )
    
    failed_login_attempts = Column(
        String(10),
        default="0",
        comment="失败登录次数"
    )
    
    locked_until = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="锁定到期时间"
    )
    
    # 关联关系
    roles = relationship(
        "Role",
        secondary=user_role_association,
        back_populates="users",
        lazy="selectin"
    )
    
    def has_permission(self, permission_code: str) -> bool:
        """检查用户是否有指定权限"""
        if self.is_superuser:
            return True
        
        for role in self.roles:
            if role.has_permission(permission_code):
                return True
        
        return False
    
    def has_role(self, role_code: str) -> bool:
        """检查用户是否有指定角色"""
        return any(role.code == role_code for role in self.roles)
    
    def get_permissions(self) -> set:
        """获取用户所有权限"""
        permissions = set()
        
        for role in self.roles:
            permissions.update(role.get_permissions())
        
        return permissions
    
    def is_locked(self) -> bool:
        """检查用户是否被锁定"""
        if self.locked_until:
            return datetime.utcnow() < self.locked_until
        return False
    
    def update_login_info(self, ip_address: str):
        """更新登录信息"""
        self.last_login_at = datetime.utcnow()
        self.last_login_ip = ip_address
        self.login_count = str(int(self.login_count) + 1)
        self.failed_login_attempts = "0"  # 重置失败次数


class Role(BaseModel, UserTrackingMixin):
    """角色模型"""
    
    __tablename__ = "role"
    __table_args__ = {'comment': '角色表'}
    
    code = Column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        comment="角色代码"
    )
    
    name = Column(
        String(100),
        nullable=False,
        comment="角色名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="角色描述"
    )
    
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    # 关联关系
    users = relationship(
        "User",
        secondary=user_role_association,
        back_populates="roles"
    )
    
    permissions = relationship(
        "Permission",
        secondary="role_permission_association",
        back_populates="roles",
        lazy="selectin"
    )
    
    def has_permission(self, permission_code: str) -> bool:
        """检查角色是否有指定权限"""
        return any(perm.code == permission_code for perm in self.permissions)
    
    def get_permissions(self) -> set:
        """获取角色所有权限代码"""
        return {perm.code for perm in self.permissions}


# 角色权限关联表
role_permission_association = Table(
    'role_permission_association',
    BaseModel.metadata,
    Column('role_id', String(50), ForeignKey('role.id'), primary_key=True),
    Column('permission_id', String(50), ForeignKey('permission.id'), primary_key=True),
    comment="角色权限关联表"
)


class Permission(BaseModel, UserTrackingMixin):
    """权限模型"""
    
    __tablename__ = "permission"
    __table_args__ = {'comment': '权限表'}
    
    code = Column(
        String(100),
        unique=True,
        nullable=False,
        index=True,
        comment="权限代码"
    )
    
    name = Column(
        String(100),
        nullable=False,
        comment="权限名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="权限描述"
    )
    
    module = Column(
        String(50),
        nullable=False,
        comment="所属模块"
    )
    
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    # 关联关系
    roles = relationship(
        "Role",
        secondary=role_permission_association,
        back_populates="permissions"
    )


class UserSession(BaseModel):
    """用户会话模型"""
    
    __tablename__ = "user_session"
    __table_args__ = {'comment': '用户会话表'}
    
    user_id = Column(
        String(50),
        ForeignKey('user.id'),
        nullable=False,
        index=True,
        comment="用户ID"
    )
    
    session_token = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        comment="会话令牌"
    )
    
    refresh_token = Column(
        String(255),
        unique=True,
        nullable=False,
        comment="刷新令牌"
    )
    
    expires_at = Column(
        DateTime(timezone=True),
        nullable=False,
        comment="过期时间"
    )
    
    ip_address = Column(
        String(45),
        nullable=True,
        comment="IP地址"
    )
    
    user_agent = Column(
        String(500),
        nullable=True,
        comment="用户代理"
    )
    
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    # 关联关系
    user = relationship("User", lazy="selectin")


class UserAuditLog(BaseModel, AuditLogMixin):
    """用户审计日志模型"""
    
    __tablename__ = "user_audit_log"
    __table_args__ = {'comment': '用户审计日志表'}
    
    user_id = Column(
        String(50),
        ForeignKey('user.id'),
        nullable=True,
        index=True,
        comment="用户ID"
    )
    
    target_user_id = Column(
        String(50),
        nullable=True,
        comment="目标用户ID"
    )
    
    # 关联关系
    user = relationship("User", lazy="selectin")
