"""
性能优化服务
提供数据缓存、异步处理和性能监控功能
"""

import streamlit as st
import pandas as pd
import time
import asyncio
import threading
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
import hashlib
import pickle
import os
from pathlib import Path
import logging

class PerformanceOptimizationService:
    """性能优化服务"""
    
    def __init__(self, cache_dir: str = "cache"):
        self.logger = logging.getLogger(__name__)
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # 缓存配置
        self.cache_ttl = 300  # 5分钟缓存时间
        self.max_cache_size = 100  # 最大缓存条目数
        
        # 性能监控
        self.performance_metrics = {
            'page_load_times': [],
            'cache_hit_rate': 0,
            'memory_usage': [],
            'api_response_times': []
        }
    
    @staticmethod
    @st.cache_data(ttl=300, max_entries=50)
    def cached_data_processing(data_hash: str, processing_func: Callable, *args, **kwargs):
        """缓存数据处理结果"""
        return processing_func(*args, **kwargs)
    
    @staticmethod
    @st.cache_data(ttl=600, max_entries=20)
    def cached_chart_data(chart_type: str, data_params: str):
        """缓存图表数据"""
        # 模拟图表数据生成
        import random
        
        if chart_type == "line":
            return {
                'x': list(range(30)),
                'y': [random.randint(50, 150) for _ in range(30)]
            }
        elif chart_type == "bar":
            return {
                'categories': ['A', 'B', 'C', 'D', 'E'],
                'values': [random.randint(20, 100) for _ in range(5)]
            }
        else:
            return {}
    
    def cache_key_generator(self, func_name: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = f"{func_name}_{str(args)}_{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_cached_result(self, cache_key: str) -> Optional[Any]:
        """获取缓存结果"""
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        if cache_file.exists():
            try:
                # 检查缓存是否过期
                cache_time = cache_file.stat().st_mtime
                if time.time() - cache_time < self.cache_ttl:
                    with open(cache_file, 'rb') as f:
                        return pickle.load(f)
                else:
                    # 删除过期缓存
                    cache_file.unlink()
            except Exception as e:
                self.logger.error(f"读取缓存失败: {str(e)}")
        
        return None
    
    def set_cached_result(self, cache_key: str, result: Any):
        """设置缓存结果"""
        try:
            cache_file = self.cache_dir / f"{cache_key}.pkl"
            with open(cache_file, 'wb') as f:
                pickle.dump(result, f)
            
            # 清理旧缓存
            self._cleanup_old_cache()
            
        except Exception as e:
            self.logger.error(f"设置缓存失败: {str(e)}")
    
    def _cleanup_old_cache(self):
        """清理旧缓存"""
        cache_files = list(self.cache_dir.glob("*.pkl"))
        
        if len(cache_files) > self.max_cache_size:
            # 按修改时间排序，删除最旧的文件
            cache_files.sort(key=lambda x: x.stat().st_mtime)
            for old_file in cache_files[:-self.max_cache_size]:
                try:
                    old_file.unlink()
                except Exception as e:
                    self.logger.error(f"删除旧缓存失败: {str(e)}")
    
    def measure_performance(self, func: Callable, *args, **kwargs):
        """测量函数性能"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        self.performance_metrics['api_response_times'].append(execution_time)
        
        # 只保留最近100次记录
        if len(self.performance_metrics['api_response_times']) > 100:
            self.performance_metrics['api_response_times'] = \
                self.performance_metrics['api_response_times'][-100:]
        
        return result, execution_time
    
    def async_data_loader(self, data_sources: List[str]) -> Dict[str, Any]:
        """异步数据加载"""
        async def load_data_source(source: str):
            # 模拟异步数据加载
            await asyncio.sleep(0.1)  # 模拟网络延迟
            return {
                'source': source,
                'data': f"Data from {source}",
                'timestamp': datetime.now().isoformat()
            }
        
        async def load_all_sources():
            tasks = [load_data_source(source) for source in data_sources]
            return await asyncio.gather(*tasks)
        
        # 在新的事件循环中运行
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(load_all_sources())
            loop.close()
            return {result['source']: result for result in results}
        except Exception as e:
            self.logger.error(f"异步数据加载失败: {str(e)}")
            return {}
    
    def lazy_load_component(self, component_id: str, load_func: Callable):
        """懒加载组件"""
        if f"lazy_loaded_{component_id}" not in st.session_state:
            with st.spinner(f"加载 {component_id}..."):
                result = load_func()
                st.session_state[f"lazy_loaded_{component_id}"] = result
        
        return st.session_state[f"lazy_loaded_{component_id}"]
    
    def paginated_data_display(self, data: List[Dict[str, Any]], 
                             page_size: int = 20, 
                             page_key: str = "page"):
        """分页数据显示"""
        total_items = len(data)
        total_pages = (total_items + page_size - 1) // page_size
        
        # 页面选择器
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col1:
            if st.button("⬅️ 上一页", key=f"{page_key}_prev"):
                if st.session_state.get(f"{page_key}_current", 1) > 1:
                    st.session_state[f"{page_key}_current"] -= 1
        
        with col2:
            current_page = st.session_state.get(f"{page_key}_current", 1)
            st.markdown(f"<div style='text-align: center;'>第 {current_page} 页，共 {total_pages} 页</div>", 
                       unsafe_allow_html=True)
        
        with col3:
            if st.button("下一页 ➡️", key=f"{page_key}_next"):
                if st.session_state.get(f"{page_key}_current", 1) < total_pages:
                    st.session_state[f"{page_key}_current"] = \
                        st.session_state.get(f"{page_key}_current", 1) + 1
        
        # 显示当前页数据
        current_page = st.session_state.get(f"{page_key}_current", 1)
        start_idx = (current_page - 1) * page_size
        end_idx = min(start_idx + page_size, total_items)
        
        return data[start_idx:end_idx]
    
    def optimize_dataframe_display(self, df: pd.DataFrame, max_rows: int = 1000):
        """优化DataFrame显示"""
        if len(df) > max_rows:
            st.warning(f"数据量较大（{len(df)}行），仅显示前{max_rows}行")
            return df.head(max_rows)
        return df
    
    def memory_efficient_processing(self, data: pd.DataFrame, 
                                  chunk_size: int = 1000) -> pd.DataFrame:
        """内存高效的数据处理"""
        if len(data) <= chunk_size:
            return data
        
        # 分块处理大数据
        processed_chunks = []
        total_chunks = (len(data) + chunk_size - 1) // chunk_size
        
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        for i in range(0, len(data), chunk_size):
            chunk = data.iloc[i:i+chunk_size]
            # 处理数据块（这里只是示例）
            processed_chunk = chunk.copy()
            processed_chunks.append(processed_chunk)
            
            # 更新进度
            progress = (i // chunk_size + 1) / total_chunks
            progress_bar.progress(progress)
            status_text.text(f"处理进度: {progress:.1%}")
        
        progress_bar.empty()
        status_text.empty()
        
        return pd.concat(processed_chunks, ignore_index=True)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        api_times = self.performance_metrics['api_response_times']
        
        if api_times:
            avg_response_time = sum(api_times) / len(api_times)
            max_response_time = max(api_times)
            min_response_time = min(api_times)
        else:
            avg_response_time = max_response_time = min_response_time = 0
        
        return {
            'cache_files_count': len(list(self.cache_dir.glob("*.pkl"))),
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'min_response_time': min_response_time,
            'total_api_calls': len(api_times),
            'cache_hit_rate': self.performance_metrics['cache_hit_rate']
        }
    
    def create_performance_dashboard(self):
        """创建性能监控仪表板"""
        st.markdown("### ⚡ 系统性能监控")
        
        report = self.get_performance_report()
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="平均响应时间",
                value=f"{report['avg_response_time']:.3f}s",
                delta=None
            )
        
        with col2:
            st.metric(
                label="缓存文件数",
                value=report['cache_files_count'],
                delta=None
            )
        
        with col3:
            st.metric(
                label="API调用次数",
                value=report['total_api_calls'],
                delta=None
            )
        
        with col4:
            st.metric(
                label="缓存命中率",
                value=f"{report['cache_hit_rate']:.1%}",
                delta=None
            )
        
        # 性能优化建议
        st.markdown("### 💡 性能优化建议")
        
        suggestions = []
        
        if report['avg_response_time'] > 1.0:
            suggestions.append("⚠️ 平均响应时间较长，建议启用更多缓存")
        
        if report['cache_files_count'] > 80:
            suggestions.append("🗂️ 缓存文件较多，建议清理旧缓存")
        
        if report['cache_hit_rate'] < 0.5:
            suggestions.append("📈 缓存命中率较低，建议调整缓存策略")
        
        if not suggestions:
            suggestions.append("✅ 系统性能良好，无需优化")
        
        for suggestion in suggestions:
            st.info(suggestion)
    
    def clear_all_cache(self):
        """清理所有缓存"""
        try:
            cache_files = list(self.cache_dir.glob("*.pkl"))
            for cache_file in cache_files:
                cache_file.unlink()
            
            # 清理Streamlit缓存
            st.cache_data.clear()
            
            st.success(f"已清理 {len(cache_files)} 个缓存文件")
            
        except Exception as e:
            st.error(f"清理缓存失败: {str(e)}")


# 创建全局实例
performance_service = PerformanceOptimizationService()
