"""
生产计划API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.logging_config import business_logger
from app.schemas.production import (
    ProductionPlanResponse, ProductionPlanCreate, ProductionPlanUpdate,
    ProductionPlanListResponse, PlanExecutionRequest, OptimizationRequest
)
from app.services.production_service import ProductionService
from app.api.dependencies import (
    get_current_user, require_permission, get_pagination_params, PaginationParams
)

router = APIRouter()


@router.get("", response_model=ProductionPlanListResponse, summary="获取生产计划列表")
async def get_production_plans(
    pagination: PaginationParams = Depends(get_pagination_params),
    status_filter: Optional[str] = Query(None, description="状态过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: dict = Depends(require_permission("plan.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取生产计划列表"""
    try:
        production_service = ProductionService(db)
        plans, total = await production_service.get_production_plans(
            page=pagination.page,
            page_size=pagination.page_size,
            status_filter=status_filter,
            search=search
        )
        
        total_pages = (total + pagination.page_size - 1) // pagination.page_size
        
        return {
            "plans": plans,
            "total": total,
            "page": pagination.page,
            "page_size": pagination.page_size,
            "total_pages": total_pages
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取生产计划列表失败"
        )


@router.get("/{plan_id}", response_model=ProductionPlanResponse, summary="获取生产计划详情")
async def get_production_plan(
    plan_id: str,
    current_user: dict = Depends(require_permission("plan.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取生产计划详情"""
    try:
        production_service = ProductionService(db)
        plan = await production_service.get_plan_by_id(plan_id)
        
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="生产计划不存在"
            )
        
        return plan
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取生产计划详情失败"
        )


@router.post("", response_model=ProductionPlanResponse, summary="创建生产计划")
async def create_production_plan(
    plan_data: ProductionPlanCreate,
    current_user: dict = Depends(require_permission("plan.create")),
    db: AsyncSession = Depends(get_db)
):
    """创建生产计划"""
    try:
        production_service = ProductionService(db)
        
        # 检查计划编码是否已存在
        existing_plan = await production_service.get_plan_by_code(plan_data.plan_code)
        if existing_plan:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="计划编码已存在"
            )
        
        plan = await production_service.create_production_plan(
            plan_data=plan_data,
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="production_plan_create",
            details={
                "plan_id": plan.id,
                "plan_code": plan.plan_code,
                "plan_name": plan.plan_name
            }
        )
        
        return plan
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="production_plan_create_error",
            error_message=str(e),
            details={"plan_code": plan_data.plan_code}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建生产计划失败"
        )


@router.put("/{plan_id}", response_model=ProductionPlanResponse, summary="更新生产计划")
async def update_production_plan(
    plan_id: str,
    plan_data: ProductionPlanUpdate,
    current_user: dict = Depends(require_permission("plan.update")),
    db: AsyncSession = Depends(get_db)
):
    """更新生产计划"""
    try:
        production_service = ProductionService(db)
        
        # 检查计划是否存在
        existing_plan = await production_service.get_plan_by_id(plan_id)
        if not existing_plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="生产计划不存在"
            )
        
        # 检查计划状态是否允许修改
        if existing_plan.status in ["executing", "completed"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="计划正在执行或已完成，无法修改"
            )
        
        plan = await production_service.update_production_plan(
            plan_id=plan_id,
            plan_data=plan_data,
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="production_plan_update",
            details={
                "plan_id": plan_id,
                "changes": plan_data.dict(exclude_unset=True)
            }
        )
        
        return plan
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="production_plan_update_error",
            error_message=str(e),
            details={"plan_id": plan_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新生产计划失败"
        )


@router.delete("/{plan_id}", summary="删除生产计划")
async def delete_production_plan(
    plan_id: str,
    current_user: dict = Depends(require_permission("plan.delete")),
    db: AsyncSession = Depends(get_db)
):
    """删除生产计划"""
    try:
        production_service = ProductionService(db)
        
        # 检查计划是否存在
        existing_plan = await production_service.get_plan_by_id(plan_id)
        if not existing_plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="生产计划不存在"
            )
        
        # 检查计划状态是否允许删除
        if existing_plan.status in ["executing", "completed"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="计划正在执行或已完成，无法删除"
            )
        
        success = await production_service.delete_production_plan(plan_id)
        
        if success:
            business_logger.log_user_action(
                user_id=current_user["id"],
                action="production_plan_delete",
                details={
                    "plan_id": plan_id,
                    "plan_code": existing_plan.plan_code
                }
            )
            
            return {"success": True, "message": "生产计划删除成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="生产计划删除失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="production_plan_delete_error",
            error_message=str(e),
            details={"plan_id": plan_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除生产计划失败"
        )


@router.post("/{plan_id}/optimize", summary="执行计划优化")
async def optimize_production_plan(
    plan_id: str,
    optimization_request: OptimizationRequest,
    current_user: dict = Depends(require_permission("plan.execute")),
    db: AsyncSession = Depends(get_db)
):
    """执行生产计划优化"""
    try:
        production_service = ProductionService(db)
        
        # 检查计划是否存在
        plan = await production_service.get_plan_by_id(plan_id)
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="生产计划不存在"
            )
        
        # 检查计划状态
        if plan.status not in ["draft", "optimized"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="计划状态不允许优化"
            )
        
        # 启动优化任务
        task_id = await production_service.start_optimization(
            plan_id=plan_id,
            optimization_config=optimization_request.dict(),
            user_id=current_user["id"]
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="production_plan_optimize",
            details={
                "plan_id": plan_id,
                "task_id": task_id,
                "algorithm": optimization_request.algorithm
            }
        )
        
        return {
            "success": True,
            "message": "优化任务已启动",
            "data": {
                "task_id": task_id,
                "plan_id": plan_id,
                "status": "optimizing"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="production_plan_optimize_error",
            error_message=str(e),
            details={"plan_id": plan_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启动优化失败"
        )


@router.post("/{plan_id}/execute", summary="执行生产计划")
async def execute_production_plan(
    plan_id: str,
    execution_request: PlanExecutionRequest,
    current_user: dict = Depends(require_permission("plan.execute")),
    db: AsyncSession = Depends(get_db)
):
    """执行生产计划"""
    try:
        production_service = ProductionService(db)
        
        # 检查计划是否存在
        plan = await production_service.get_plan_by_id(plan_id)
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="生产计划不存在"
            )
        
        # 检查计划状态
        if plan.status != "optimized":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="计划必须先完成优化才能执行"
            )
        
        # 启动执行
        execution = await production_service.start_execution(
            plan_id=plan_id,
            executor_id=current_user["id"],
            notes=execution_request.notes
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="production_plan_execute",
            details={
                "plan_id": plan_id,
                "execution_id": execution.id
            }
        )
        
        return {
            "success": True,
            "message": "生产计划执行已启动",
            "data": {
                "execution_id": execution.id,
                "plan_id": plan_id,
                "status": "executing"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="production_plan_execute_error",
            error_message=str(e),
            details={"plan_id": plan_id}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启动执行失败"
        )


@router.get("/{plan_id}/gantt", summary="获取甘特图数据")
async def get_gantt_chart_data(
    plan_id: str,
    current_user: dict = Depends(require_permission("plan.view")),
    db: AsyncSession = Depends(get_db)
):
    """获取生产计划甘特图数据"""
    try:
        production_service = ProductionService(db)
        
        # 检查计划是否存在
        plan = await production_service.get_plan_by_id(plan_id)
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="生产计划不存在"
            )
        
        # 获取甘特图数据
        gantt_data = await production_service.get_gantt_chart_data(plan_id)
        
        return {
            "success": True,
            "data": gantt_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取甘特图数据失败"
        )
