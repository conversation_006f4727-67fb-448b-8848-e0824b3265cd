"""
智能推荐服务
基于用户行为和系统数据提供智能推荐
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import logging
from dataclasses import dataclass

@dataclass
class Recommendation:
    """推荐项"""
    id: str
    type: str  # action, page, setting, optimization
    title: str
    description: str
    priority: int  # 1-5, 5最高
    confidence: float  # 0-1
    category: str
    action_data: Dict[str, Any]
    created_at: str

class IntelligentRecommendationService:
    """智能推荐服务"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 用户行为跟踪
        self.user_behavior = {
            'page_visits': {},
            'feature_usage': {},
            'time_patterns': {},
            'preferences': {}
        }

        # 推荐规则
        self.recommendation_rules = self._initialize_recommendation_rules()

        # 系统状态监控
        self.system_metrics = {}

    def _initialize_recommendation_rules(self) -> Dict[str, Any]:
        """初始化推荐规则"""
        return {
            'workflow_optimization': {
                'frequent_page_sequence': {
                    'condition': 'user_visits_same_sequence_3_times',
                    'recommendation': 'create_workflow_shortcut',
                    'priority': 4
                },
                'unused_features': {
                    'condition': 'feature_not_used_7_days',
                    'recommendation': 'feature_tutorial',
                    'priority': 2
                }
            },
            'performance_optimization': {
                'slow_loading': {
                    'condition': 'page_load_time_gt_3s',
                    'recommendation': 'enable_caching',
                    'priority': 5
                },
                'large_dataset': {
                    'condition': 'dataset_size_gt_10mb',
                    'recommendation': 'use_pagination',
                    'priority': 3
                }
            },
            'user_experience': {
                'mobile_usage': {
                    'condition': 'mobile_device_detected',
                    'recommendation': 'enable_mobile_mode',
                    'priority': 4
                },
                'frequent_errors': {
                    'condition': 'error_rate_gt_5_percent',
                    'recommendation': 'check_data_quality',
                    'priority': 5
                }
            }
        }

    def track_user_behavior(self, action: str, data: Dict[str, Any]):
        """跟踪用户行为"""
        timestamp = datetime.now().isoformat()

        if action == 'page_visit':
            page = data.get('page')
            if page:
                if page not in self.user_behavior['page_visits']:
                    self.user_behavior['page_visits'][page] = []
                self.user_behavior['page_visits'][page].append(timestamp)

        elif action == 'feature_use':
            feature = data.get('feature')
            if feature:
                if feature not in self.user_behavior['feature_usage']:
                    self.user_behavior['feature_usage'][feature] = []
                self.user_behavior['feature_usage'][feature].append(timestamp)

        elif action == 'time_spent':
            page = data.get('page')
            duration = data.get('duration', 0)
            if page:
                if page not in self.user_behavior['time_patterns']:
                    self.user_behavior['time_patterns'][page] = []
                self.user_behavior['time_patterns'][page].append(duration)

        # 保存到session state
        st.session_state['user_behavior'] = self.user_behavior

    def analyze_user_patterns(self) -> Dict[str, Any]:
        """分析用户使用模式"""
        patterns = {
            'most_visited_pages': [],
            'least_used_features': [],
            'peak_usage_hours': [],
            'workflow_sequences': []
        }

        # 分析最常访问的页面
        page_counts = {}
        for page, visits in self.user_behavior['page_visits'].items():
            page_counts[page] = len(visits)

        patterns['most_visited_pages'] = sorted(
            page_counts.items(), key=lambda x: x[1], reverse=True
        )[:5]

        # 分析最少使用的功能
        feature_counts = {}
        for feature, usage in self.user_behavior['feature_usage'].items():
            feature_counts[feature] = len(usage)

        patterns['least_used_features'] = sorted(
            feature_counts.items(), key=lambda x: x[1]
        )[:5]

        # 分析使用时间模式
        all_timestamps = []
        for visits in self.user_behavior['page_visits'].values():
            all_timestamps.extend(visits)

        if all_timestamps:
            hours = [datetime.fromisoformat(ts).hour for ts in all_timestamps]
            hour_counts = {}
            for hour in hours:
                hour_counts[hour] = hour_counts.get(hour, 0) + 1

            patterns['peak_usage_hours'] = sorted(
                hour_counts.items(), key=lambda x: x[1], reverse=True
            )[:3]

        return patterns

    def generate_recommendations(self) -> List[Recommendation]:
        """生成智能推荐"""
        recommendations = []
        patterns = self.analyze_user_patterns()

        # 基于用户模式的推荐
        recommendations.extend(self._generate_workflow_recommendations(patterns))
        recommendations.extend(self._generate_feature_recommendations(patterns))
        recommendations.extend(self._generate_performance_recommendations())
        recommendations.extend(self._generate_learning_recommendations(patterns))

        # 按优先级排序
        recommendations.sort(key=lambda x: x.priority, reverse=True)

        return recommendations[:10]  # 返回前10个推荐

    def _generate_workflow_recommendations(self, patterns: Dict[str, Any]) -> List[Recommendation]:
        """生成工作流推荐"""
        recommendations = []

        # 推荐常用页面快捷方式
        if patterns['most_visited_pages']:
            top_page = patterns['most_visited_pages'][0]
            if top_page[1] > 5:  # 访问超过5次
                recommendations.append(Recommendation(
                    id="workflow_shortcut_1",
                    type="action",
                    title="创建快捷方式",
                    description=f"为常用页面 '{top_page[0]}' 创建桌面快捷方式",
                    priority=3,
                    confidence=0.8,
                    category="工作流优化",
                    action_data={"page": top_page[0], "action": "create_shortcut"},
                    created_at=datetime.now().isoformat()
                ))

        # 推荐页面组合
        if len(patterns['most_visited_pages']) >= 2:
            recommendations.append(Recommendation(
                id="workflow_combination_1",
                type="action",
                title="页面组合建议",
                description="根据您的使用习惯，建议将相关页面组合为工作流",
                priority=2,
                confidence=0.7,
                category="工作流优化",
                action_data={"action": "create_workflow", "pages": patterns['most_visited_pages'][:3]},
                created_at=datetime.now().isoformat()
            ))

        return recommendations

    def _generate_feature_recommendations(self, patterns: Dict[str, Any]) -> List[Recommendation]:
        """生成功能推荐"""
        recommendations = []

        # 推荐未使用的功能
        all_features = [
            "智能预测", "异常检测", "自动报告", "决策支持",
            "个性化仪表板", "数据可视化", "算法优化"
        ]

        used_features = set(self.user_behavior['feature_usage'].keys())
        unused_features = [f for f in all_features if f not in used_features]

        for feature in unused_features[:3]:
            recommendations.append(Recommendation(
                id=f"feature_tutorial_{feature}",
                type="tutorial",
                title=f"探索 {feature} 功能",
                description=f"了解如何使用 {feature} 功能来提升工作效率",
                priority=2,
                confidence=0.6,
                category="功能探索",
                action_data={"feature": feature, "action": "show_tutorial"},
                created_at=datetime.now().isoformat()
            ))

        return recommendations

    def _generate_performance_recommendations(self) -> List[Recommendation]:
        """生成性能优化推荐"""
        recommendations = []

        # 检查缓存使用情况
        if 'performance_service' in st.session_state:
            perf_report = st.session_state['performance_service'].get_performance_report()

            if perf_report['avg_response_time'] > 2.0:
                recommendations.append(Recommendation(
                    id="performance_cache_1",
                    type="optimization",
                    title="启用数据缓存",
                    description="系统响应较慢，建议启用数据缓存以提升性能",
                    priority=4,
                    confidence=0.9,
                    category="性能优化",
                    action_data={"action": "enable_cache"},
                    created_at=datetime.now().isoformat()
                ))

            if perf_report['cache_files_count'] > 50:
                recommendations.append(Recommendation(
                    id="performance_cleanup_1",
                    type="optimization",
                    title="清理缓存文件",
                    description="缓存文件较多，建议清理以释放存储空间",
                    priority=2,
                    confidence=0.8,
                    category="性能优化",
                    action_data={"action": "cleanup_cache"},
                    created_at=datetime.now().isoformat()
                ))

        return recommendations

    def _generate_learning_recommendations(self, patterns: Dict[str, Any]) -> List[Recommendation]:
        """生成学习建议推荐"""
        recommendations = []

        # 基于使用模式推荐学习内容
        if patterns['least_used_features']:
            least_used = patterns['least_used_features'][0][0]
            recommendations.append(Recommendation(
                id="learning_feature_1",
                type="learning",
                title="功能学习建议",
                description=f"学习如何更好地使用 {least_used} 功能",
                priority=1,
                confidence=0.5,
                category="学习提升",
                action_data={"feature": least_used, "action": "show_guide"},
                created_at=datetime.now().isoformat()
            ))

        # 推荐最佳实践
        recommendations.append(Recommendation(
            id="learning_best_practice_1",
            type="learning",
            title="最佳实践指南",
            description="了解Smart Planning系统的最佳使用实践",
            priority=1,
            confidence=0.6,
            category="学习提升",
            action_data={"action": "show_best_practices"},
            created_at=datetime.now().isoformat()
        ))

        return recommendations

    def display_recommendations(self, max_recommendations: int = 5):
        """显示推荐内容"""
        recommendations = self.generate_recommendations()[:max_recommendations]

        if not recommendations:
            st.info("🎉 暂无推荐，系统运行良好！")
            return

        st.markdown("### 💡 智能推荐")

        for rec in recommendations:
            with st.expander(f"{self._get_priority_icon(rec.priority)} {rec.title}", expanded=False):
                st.markdown(f"**类别**: {rec.category}")
                st.markdown(f"**描述**: {rec.description}")
                st.markdown(f"**置信度**: {rec.confidence:.1%}")

                col1, col2 = st.columns([1, 1])

                with col1:
                    if st.button("执行建议", key=f"exec_{rec.id}"):
                        self._execute_recommendation(rec)

                with col2:
                    if st.button("忽略", key=f"ignore_{rec.id}"):
                        st.success("已忽略此建议")

    def _get_priority_icon(self, priority: int) -> str:
        """获取优先级图标"""
        icons = {5: "🔥", 4: "⚡", 3: "💡", 2: "📝", 1: "💭"}
        return icons.get(priority, "📌")

    def _execute_recommendation(self, recommendation: Recommendation):
        """执行推荐操作"""
        action = recommendation.action_data.get('action')

        if action == "create_shortcut":
            st.success(f"已为页面 '{recommendation.action_data['page']}' 创建快捷方式")

        elif action == "enable_cache":
            st.success("已启用数据缓存，系统性能将得到提升")

        elif action == "cleanup_cache":
            if 'performance_service' in st.session_state:
                st.session_state['performance_service'].clear_all_cache()

        elif action == "show_tutorial":
            feature = recommendation.action_data.get('feature')
            st.info(f"正在为您展示 {feature} 功能教程...")

        elif action == "show_guide":
            feature = recommendation.action_data.get('feature')
            st.info(f"正在为您展示 {feature} 使用指南...")

        elif action == "show_best_practices":
            st.info("正在为您展示最佳实践指南...")

        else:
            st.info("推荐操作已记录，将在后续版本中实现")

    def create_recommendation_widget(self):
        """创建推荐小部件"""
        with st.sidebar:
            st.markdown("### 🤖 智能助手")

            # 显示简化的推荐
            recommendations = self.generate_recommendations()[:3]

            if recommendations:
                for rec in recommendations:
                    with st.container():
                        st.markdown(f"""
                        <div style="
                            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                            padding: 12px;
                            border-radius: 8px;
                            margin: 8px 0;
                            border-left: 4px solid #4A90E2;
                        ">
                            <div style="font-weight: 600; color: #2C3E50; margin-bottom: 4px;">
                                {self._get_priority_icon(rec.priority)} {rec.title}
                            </div>
                            <div style="font-size: 12px; color: #6c757d;">
                                {rec.description[:50]}...
                            </div>
                        </div>
                        """, unsafe_allow_html=True)

                if st.button("查看全部推荐", key="view_all_recommendations"):
                    st.session_state['show_recommendations'] = True
            else:
                st.success("🎉 系统运行良好")


# 创建全局实例
recommendation_service = IntelligentRecommendationService()
