"""
PCI管理页面 - FS数据管理与消耗策略
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from datetime import datetime, timedelta
import sqlite3
import json

# 页面配置
st.set_page_config(
    page_title="PCI管理",
    page_icon="🔬",
    layout="wide"
)

st.title("🔬 PCI管理中心")
st.markdown("### FS数据管理与消耗策略优化")

# 侧边栏 - PCI配置和过滤
with st.sidebar:
    st.markdown("### 🎛️ PCI配置")

    # FIFO配置
    st.markdown("#### 📅 FIFO配置")
    fifo_threshold_days = st.number_input(
        "优先使用阈值(天)",
        min_value=30,
        max_value=365,
        value=180,
        help="超过此天数的FS优先使用"
    )

    enable_fifo_strict = st.checkbox(
        "严格FIFO模式",
        value=True,
        help="是否严格按照生产日期排序"
    )

    # 消耗策略权重
    st.markdown("#### ⚖️ 消耗策略权重")

    weight_age = st.slider(
        "库龄权重",
        min_value=0.0,
        max_value=1.0,
        value=0.4,
        step=0.1,
        help="库龄在消耗策略中的权重"
    )

    weight_yield = st.slider(
        "Yield权重",
        min_value=0.0,
        max_value=1.0,
        value=0.3,
        step=0.1,
        help="Yield在消耗策略中的权重"
    )

    weight_urgency = st.slider(
        "紧急程度权重",
        min_value=0.0,
        max_value=1.0,
        value=0.2,
        step=0.1,
        help="出货紧急程度在消耗策略中的权重"
    )

    weight_gu = st.slider(
        "GU权重",
        min_value=0.0,
        max_value=1.0,
        value=0.1,
        step=0.1,
        help="GU因子在消耗策略中的权重"
    )

    # 显示配置
    st.markdown("#### 🎨 显示配置")

    show_expired_only = st.checkbox(
        "仅显示超期FS",
        value=False,
        help="仅显示超过阈值天数的FS"
    )

    color_coding_enabled = st.checkbox(
        "启用颜色编码",
        value=True,
        help="根据条件用颜色标识FS"
    )

    # 数据刷新
    st.markdown("---")
    if st.button("🔄 刷新数据", type="primary"):
        st.rerun()

# 获取FS数据 - 在所有tab之前获取，确保所有tab都能访问
fs_data = get_fs_data_with_strategy(
    fifo_threshold_days=fifo_threshold_days,
    weights={
        'age': weight_age,
        'yield': weight_yield,
        'urgency': weight_urgency,
        'gu': weight_gu
    },
    filters={
        'product': 'all',  # 默认值，在tab1中会被覆盖
        'location': 'all',
        'status': 'all'
    }
)

# 主要内容区域
tab1, tab2, tab3, tab4 = st.tabs(["📋 FS数据列表", "📊 消耗策略分析", "📈 库龄分析", "⚙️ SQL查询"])

with tab1:
    st.markdown("#### 📋 FS数据管理")

    # 数据过滤器
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        product_filter = st.selectbox(
            "产品筛选",
            options=["全部", "产品A", "产品B", "产品C", "产品D"],
            index=0
        )

    with col2:
        location_filter = st.selectbox(
            "库位筛选",
            options=["全部", "A区", "B区", "C区", "D区"],
            index=0
        )

    with col3:
        status_filter = st.selectbox(
            "状态筛选",
            options=["全部", "可用", "预留", "冻结", "已用"],
            index=0
        )

    with col4:
        sort_by = st.selectbox(
            "排序方式",
            options=["生产日期", "消耗优先级", "Yield", "库存量"],
            index=1
        )

    # 应用过滤器到已获取的数据
    filtered_fs_data = apply_filters_to_fs_data(
        fs_data,
        product_filter,
        location_filter,
        status_filter
    )

    if filtered_fs_data:
        # 应用过滤
        display_fs_data = filtered_fs_data.copy()
        if show_expired_only:
            display_fs_data = [fs for fs in display_fs_data if fs['days_old'] > fifo_threshold_days]

        # 排序
        if sort_by == "生产日期":
            display_fs_data.sort(key=lambda x: x['production_date'])
        elif sort_by == "消耗优先级":
            display_fs_data.sort(key=lambda x: x['consumption_priority'], reverse=True)
        elif sort_by == "Yield":
            display_fs_data.sort(key=lambda x: x['yield'], reverse=True)
        elif sort_by == "库存量":
            display_fs_data.sort(key=lambda x: x['quantity'], reverse=True)

        # 创建DataFrame用于显示
        df_display = create_fs_display_dataframe(display_fs_data, color_coding_enabled, fifo_threshold_days)

        # 显示统计信息
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_fs = len(display_fs_data)
            st.metric("FS总数", total_fs)

        with col2:
            expired_fs = len([fs for fs in display_fs_data if fs['days_old'] > fifo_threshold_days])
            st.metric("超期FS", expired_fs, delta=f"{expired_fs/total_fs*100:.1f}%" if total_fs > 0 else "0%")

        with col3:
            total_quantity = sum(fs['quantity'] for fs in display_fs_data)
            st.metric("总库存量", f"{total_quantity:,}")

        with col4:
            avg_yield = np.mean([fs['yield'] for fs in display_fs_data])
            st.metric("平均Yield", f"{avg_yield:.1f}%")

        # 显示FS数据表格
        st.markdown("##### 📊 FS数据详情")

        # 使用HTML表格以支持颜色显示
        if color_coding_enabled:
            display_colored_fs_table(df_display, fifo_threshold_days)
        else:
            st.dataframe(df_display, use_container_width=True, hide_index=True)

        # 批量操作
        st.markdown("##### 🔧 批量操作")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📤 导出数据"):
                export_fs_data(display_fs_data)

        with col2:
            if st.button("🔄 更新消耗策略"):
                update_consumption_strategy(display_fs_data)

        with col3:
            if st.button("⚠️ 标记超期FS"):
                mark_expired_fs(display_fs_data, fifo_threshold_days)

    else:
        st.info("暂无FS数据")

with tab2:
    st.markdown("#### 📊 消耗策略分析")

    if fs_data:
        # 消耗优先级分布
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("##### 🎯 消耗优先级分布")

            priority_ranges = ["高优先级(>80)", "中优先级(60-80)", "低优先级(<60)"]
            priority_counts = [
                len([fs for fs in fs_data if fs['consumption_priority'] > 80]),
                len([fs for fs in fs_data if 60 <= fs['consumption_priority'] <= 80]),
                len([fs for fs in fs_data if fs['consumption_priority'] < 60])
            ]

            fig_priority = px.pie(
                values=priority_counts,
                names=priority_ranges,
                title="消耗优先级分布",
                color_discrete_sequence=['#ff4444', '#ffaa44', '#44ff44']
            )

            st.plotly_chart(fig_priority, use_container_width=True)

        with col2:
            st.markdown("##### 📈 Yield vs 库龄散点图")

            df_scatter = pd.DataFrame(fs_data)

            fig_scatter = px.scatter(
                df_scatter,
                x='days_old',
                y='yield',
                size='quantity',
                color='consumption_priority',
                hover_data=['fs_id', 'product_name'],
                title="Yield vs 库龄关系",
                color_continuous_scale='RdYlGn'
            )

            fig_scatter.add_vline(x=fifo_threshold_days, line_dash="dash",
                                 line_color="red", annotation_text="FIFO阈值")

            st.plotly_chart(fig_scatter, use_container_width=True)

        # 消耗策略建议
        st.markdown("##### 💡 消耗策略建议")

        recommendations = generate_consumption_recommendations(fs_data, fifo_threshold_days)

        for rec in recommendations:
            if rec['type'] == 'urgent':
                st.error(f"🚨 {rec['message']}")
            elif rec['type'] == 'warning':
                st.warning(f"⚠️ {rec['message']}")
            else:
                st.info(f"💡 {rec['message']}")

with tab3:
    st.markdown("#### 📈 库龄分析")

    if fs_data:
        # 库龄分布直方图
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("##### 📊 库龄分布")

            ages = [fs['days_old'] for fs in fs_data]

            fig_hist = px.histogram(
                x=ages,
                nbins=20,
                title="FS库龄分布",
                labels={'x': '库龄(天)', 'y': '数量'}
            )

            fig_hist.add_vline(x=fifo_threshold_days, line_dash="dash",
                              line_color="red", annotation_text="FIFO阈值")

            st.plotly_chart(fig_hist, use_container_width=True)

        with col2:
            st.markdown("##### 📈 库龄趋势")

            # 按月统计库龄
            df_trend = create_age_trend_data(fs_data)

            fig_trend = px.line(
                df_trend,
                x='month',
                y='avg_age',
                title="平均库龄趋势",
                markers=True
            )

            st.plotly_chart(fig_trend, use_container_width=True)

        # 库龄统计表
        st.markdown("##### 📋 库龄统计")

        age_stats = calculate_age_statistics(fs_data, fifo_threshold_days)

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("平均库龄", f"{age_stats['avg_age']:.1f} 天")

        with col2:
            st.metric("最大库龄", f"{age_stats['max_age']} 天")

        with col3:
            st.metric("超期比例", f"{age_stats['expired_ratio']:.1f}%")

        with col4:
            st.metric("库存周转天数", f"{age_stats['turnover_days']:.1f} 天")

with tab4:
    st.markdown("#### ⚙️ SQL查询管理")

    # SQL查询编辑器
    st.markdown("##### 📝 SQL查询编辑器")

    # 预设查询模板
    query_templates = {
        "基础FS查询": """
SELECT
    fs_id,
    product_name,
    production_date,
    quantity,
    location,
    yield_rate as yield,
    status,
    gu_factor,
    urgency_level,
    JULIANDAY('now') - JULIANDAY(production_date) as days_old
FROM fs_inventory
WHERE status = 'available'
ORDER BY production_date ASC;
        """,
        "超期FS查询": """
SELECT
    fs_id,
    product_name,
    production_date,
    quantity,
    location,
    yield_rate as yield,
    JULIANDAY('now') - JULIANDAY(production_date) as days_old
FROM fs_inventory
WHERE status = 'available'
    AND JULIANDAY('now') - JULIANDAY(production_date) > 180
ORDER BY production_date ASC;
        """,
        "低Yield FS查询": """
SELECT
    fs_id,
    product_name,
    production_date,
    quantity,
    yield_rate as yield,
    location
FROM fs_inventory
WHERE status = 'available'
    AND yield_rate < 85
ORDER BY yield_rate ASC;
        """,
        "紧急出货FS查询": """
SELECT
    fs_id,
    product_name,
    production_date,
    quantity,
    urgency_level,
    yield_rate as yield
FROM fs_inventory
WHERE status = 'available'
    AND urgency_level = 'high'
ORDER BY production_date ASC;
        """
    }

    # 查询模板选择
    selected_template = st.selectbox(
        "选择查询模板",
        options=list(query_templates.keys()),
        index=0
    )

    # SQL编辑器
    sql_query = st.text_area(
        "SQL查询语句",
        value=query_templates[selected_template],
        height=200,
        help="编写SQL查询语句来获取FS数据"
    )

    # 查询执行
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("▶️ 执行查询"):
            execute_sql_query(sql_query)

    with col2:
        if st.button("💾 保存查询"):
            save_sql_query(sql_query, selected_template)

    with col3:
        if st.button("📋 查询历史"):
            show_query_history()

    # 查询结果显示区域
    st.markdown("##### 📊 查询结果")

    # 这里会显示SQL查询的结果
    if 'sql_result' in st.session_state:
        st.dataframe(st.session_state.sql_result, use_container_width=True)
    else:
        st.info("请执行SQL查询以查看结果")


# 辅助函数定义
def get_fs_data_with_strategy(fifo_threshold_days, weights, filters):
    """获取带有消耗策略的FS数据"""
    # 模拟FS数据
    mock_data = [
        {
            'fs_id': 'FS001',
            'product_name': '产品A',
            'production_date': '2023-06-15',
            'quantity': 1500,
            'location': 'A区-01',
            'yield': 92.5,
            'status': '可用',
            'gu_factor': 1.2,
            'urgency_level': 'high',
            'days_old': 220
        },
        {
            'fs_id': 'FS002',
            'product_name': '产品B',
            'production_date': '2023-08-20',
            'quantity': 800,
            'location': 'B区-03',
            'yield': 88.3,
            'status': '可用',
            'gu_factor': 1.0,
            'urgency_level': 'medium',
            'days_old': 155
        },
        {
            'fs_id': 'FS003',
            'product_name': '产品A',
            'production_date': '2023-05-10',
            'quantity': 2000,
            'location': 'A区-05',
            'yield': 85.7,
            'status': '可用',
            'gu_factor': 1.5,
            'urgency_level': 'high',
            'days_old': 256
        },
        {
            'fs_id': 'FS004',
            'product_name': '产品C',
            'production_date': '2023-10-01',
            'quantity': 1200,
            'location': 'C区-02',
            'yield': 94.1,
            'status': '可用',
            'gu_factor': 0.8,
            'urgency_level': 'low',
            'days_old': 113
        }
    ]

    # 计算消耗优先级
    for fs in mock_data:
        priority = calculate_consumption_priority(fs, fifo_threshold_days, weights)
        fs['consumption_priority'] = priority

    return mock_data


def calculate_consumption_priority(fs, fifo_threshold_days, weights):
    """计算消耗优先级"""
    # 库龄得分 (超过阈值的天数越多得分越高)
    age_score = min(100, (fs['days_old'] / fifo_threshold_days) * 100)

    # Yield得分 (Yield越低得分越高，优先消耗)
    yield_score = max(0, 100 - fs['yield'])

    # 紧急程度得分
    urgency_scores = {'high': 100, 'medium': 60, 'low': 20}
    urgency_score = urgency_scores.get(fs['urgency_level'], 20)

    # GU得分 (GU越高得分越高)
    gu_score = min(100, fs['gu_factor'] * 50)

    # 加权计算总优先级
    total_priority = (
        age_score * weights['age'] +
        yield_score * weights['yield'] +
        urgency_score * weights['urgency'] +
        gu_score * weights['gu']
    )

    return round(total_priority, 1)


def create_fs_display_dataframe(fs_data, color_coding_enabled, fifo_threshold_days):
    """创建用于显示的FS数据表格"""
    display_data = []

    for fs in fs_data:
        # 确定颜色标识
        color_class = ""
        if color_coding_enabled:
            if fs['days_old'] > fifo_threshold_days:
                color_class = "expired"  # 红色 - 超期
            elif fs['yield'] < 85:
                color_class = "low_yield"  # 黄色 - 低Yield
            elif fs['urgency_level'] == 'high':
                color_class = "urgent"  # 橙色 - 紧急
            elif fs['consumption_priority'] > 80:
                color_class = "high_priority"  # 绿色 - 高优先级

        display_data.append({
            'FS编号': fs['fs_id'],
            '产品名称': fs['product_name'],
            '生产日期': fs['production_date'],
            '库龄(天)': fs['days_old'],
            '库存量': f"{fs['quantity']:,}",
            '库位': fs['location'],
            'Yield(%)': f"{fs['yield']:.1f}",
            '状态': fs['status'],
            'GU因子': fs['gu_factor'],
            '紧急程度': fs['urgency_level'],
            '消耗优先级': f"{fs['consumption_priority']:.1f}",
            '颜色标识': color_class
        })

    return pd.DataFrame(display_data)


def display_colored_fs_table(df, fifo_threshold_days):
    """显示带颜色标识的FS表格"""

    # 定义颜色样式
    color_styles = {
        'expired': 'background-color: #ffebee; border-left: 4px solid #f44336;',  # 红色 - 超期
        'low_yield': 'background-color: #fff8e1; border-left: 4px solid #ff9800;',  # 黄色 - 低Yield
        'urgent': 'background-color: #fce4ec; border-left: 4px solid #e91e63;',  # 粉色 - 紧急
        'high_priority': 'background-color: #e8f5e8; border-left: 4px solid #4caf50;'  # 绿色 - 高优先级
    }

    # 创建HTML表格
    html_table = "<div style='overflow-x: auto;'><table style='width: 100%; border-collapse: collapse;'>"

    # 表头
    html_table += "<thead><tr style='background-color: #f5f5f5;'>"
    for col in df.columns:
        if col != '颜色标识':
            html_table += f"<th style='padding: 8px; text-align: left; border: 1px solid #ddd;'>{col}</th>"
    html_table += "</tr></thead><tbody>"

    # 表格内容
    for _, row in df.iterrows():
        color_class = row['颜色标识']
        style = color_styles.get(color_class, '')

        html_table += f"<tr style='{style}'>"
        for col in df.columns:
            if col != '颜色标识':
                html_table += f"<td style='padding: 8px; border: 1px solid #ddd;'>{row[col]}</td>"
        html_table += "</tr>"

    html_table += "</tbody></table></div>"

    # 显示颜色图例
    st.markdown("##### 🎨 颜色图例")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown('<div style="background-color: #ffebee; padding: 5px; border-left: 4px solid #f44336;">🔴 超期FS (>180天)</div>', unsafe_allow_html=True)

    with col2:
        st.markdown('<div style="background-color: #fff8e1; padding: 5px; border-left: 4px solid #ff9800;">🟡 低Yield (<85%)</div>', unsafe_allow_html=True)

    with col3:
        st.markdown('<div style="background-color: #fce4ec; padding: 5px; border-left: 4px solid #e91e63;">🟠 紧急出货</div>', unsafe_allow_html=True)

    with col4:
        st.markdown('<div style="background-color: #e8f5e8; padding: 5px; border-left: 4px solid #4caf50;">🟢 高优先级</div>', unsafe_allow_html=True)

    # 显示表格
    st.markdown(html_table, unsafe_allow_html=True)


def generate_consumption_recommendations(fs_data, fifo_threshold_days):
    """生成消耗策略建议"""
    recommendations = []

    # 检查超期FS
    expired_fs = [fs for fs in fs_data if fs['days_old'] > fifo_threshold_days]
    if expired_fs:
        expired_count = len(expired_fs)
        total_expired_qty = sum(fs['quantity'] for fs in expired_fs)
        recommendations.append({
            'type': 'urgent',
            'message': f"发现 {expired_count} 个超期FS，总库存量 {total_expired_qty:,}，建议立即安排消耗"
        })

    # 检查低Yield FS
    low_yield_fs = [fs for fs in fs_data if fs['yield'] < 85]
    if low_yield_fs:
        recommendations.append({
            'type': 'warning',
            'message': f"发现 {len(low_yield_fs)} 个低Yield FS，建议优先用于内部测试或降级使用"
        })

    # 检查紧急出货
    urgent_fs = [fs for fs in fs_data if fs['urgency_level'] == 'high']
    if urgent_fs:
        recommendations.append({
            'type': 'warning',
            'message': f"有 {len(urgent_fs)} 个FS标记为紧急出货，请优先安排生产"
        })

    # 检查高GU因子
    high_gu_fs = [fs for fs in fs_data if fs['gu_factor'] > 1.3]
    if high_gu_fs:
        recommendations.append({
            'type': 'info',
            'message': f"发现 {len(high_gu_fs)} 个高GU因子FS，可考虑用于高价值产品"
        })

    return recommendations


def create_age_trend_data(fs_data):
    """创建库龄趋势数据"""
    # 模拟月度库龄趋势数据
    months = ['2023-06', '2023-07', '2023-08', '2023-09', '2023-10', '2023-11', '2023-12', '2024-01']
    avg_ages = [145, 152, 148, 156, 162, 158, 165, 171]

    return pd.DataFrame({
        'month': months,
        'avg_age': avg_ages
    })


def calculate_age_statistics(fs_data, fifo_threshold_days):
    """计算库龄统计信息"""
    ages = [fs['days_old'] for fs in fs_data]

    return {
        'avg_age': np.mean(ages),
        'max_age': max(ages),
        'expired_ratio': len([age for age in ages if age > fifo_threshold_days]) / len(ages) * 100,
        'turnover_days': np.mean(ages) * 1.2  # 简化的周转天数计算
    }


def execute_sql_query(sql_query):
    """执行SQL查询"""
    try:
        # 这里应该连接到实际的数据库
        # 现在使用模拟数据

        if "超期" in sql_query or "180" in sql_query:
            # 模拟超期FS查询结果
            result_data = [
                ['FS001', '产品A', '2023-06-15', 1500, 'A区-01', 92.5, 220],
                ['FS003', '产品A', '2023-05-10', 2000, 'A区-05', 85.7, 256]
            ]
            columns = ['FS编号', '产品名称', '生产日期', '库存量', '库位', 'Yield(%)', '库龄(天)']

        elif "低Yield" in sql_query or "yield_rate < 85" in sql_query:
            # 模拟低Yield查询结果
            result_data = [
                ['FS003', '产品A', '2023-05-10', 2000, 85.7, 'A区-05']
            ]
            columns = ['FS编号', '产品名称', '生产日期', '库存量', 'Yield(%)', '库位']

        else:
            # 模拟基础查询结果
            result_data = [
                ['FS001', '产品A', '2023-06-15', 1500, 'A区-01', 92.5, '可用', 1.2, 'high', 220],
                ['FS002', '产品B', '2023-08-20', 800, 'B区-03', 88.3, '可用', 1.0, 'medium', 155],
                ['FS003', '产品A', '2023-05-10', 2000, 'A区-05', 85.7, '可用', 1.5, 'high', 256],
                ['FS004', '产品C', '2023-10-01', 1200, 'C区-02', 94.1, '可用', 0.8, 'low', 113]
            ]
            columns = ['FS编号', '产品名称', '生产日期', '库存量', '库位', 'Yield(%)', '状态', 'GU因子', '紧急程度', '库龄(天)']

        # 创建结果DataFrame
        result_df = pd.DataFrame(result_data, columns=columns)
        st.session_state.sql_result = result_df

        st.success(f"✅ 查询执行成功，返回 {len(result_data)} 条记录")

    except Exception as e:
        st.error(f"❌ 查询执行失败: {str(e)}")


def save_sql_query(sql_query, template_name):
    """保存SQL查询"""
    # 这里应该保存到数据库或文件
    st.success(f"✅ 查询已保存为模板: {template_name}")


def show_query_history():
    """显示查询历史"""
    # 模拟查询历史
    history_data = [
        {'时间': '2024-01-16 10:30', '查询类型': '超期FS查询', '结果数': 2},
        {'时间': '2024-01-16 09:15', '查询类型': '基础FS查询', '结果数': 4},
        {'时间': '2024-01-15 16:45', '查询类型': '低Yield FS查询', '结果数': 1}
    ]

    st.dataframe(pd.DataFrame(history_data), use_container_width=True, hide_index=True)


def export_fs_data(fs_data):
    """导出FS数据"""
    st.success("✅ FS数据已导出到Excel文件")


def update_consumption_strategy(fs_data):
    """更新消耗策略"""
    st.success("✅ 消耗策略已更新")


def mark_expired_fs(fs_data, fifo_threshold_days):
    """标记超期FS"""
    expired_count = len([fs for fs in fs_data if fs['days_old'] > fifo_threshold_days])
    st.success(f"✅ 已标记 {expired_count} 个超期FS")


def apply_filters_to_fs_data(fs_data, product_filter, location_filter, status_filter):
    """应用过滤器到FS数据"""
    filtered_data = fs_data.copy()

    # 产品过滤
    if product_filter and product_filter != "全部":
        filtered_data = [fs for fs in filtered_data if fs['product_name'] == product_filter]

    # 库位过滤
    if location_filter and location_filter != "全部":
        filtered_data = [fs for fs in filtered_data if fs['location'].startswith(location_filter)]

    # 状态过滤
    if status_filter and status_filter != "全部":
        status_map = {"可用": "available", "预留": "reserved", "冻结": "frozen", "已用": "used"}
        target_status = status_map.get(status_filter, status_filter)
        filtered_data = [fs for fs in filtered_data if fs['status'] == target_status]

    return filtered_data
