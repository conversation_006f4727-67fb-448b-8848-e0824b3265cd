"""
文件上传相关模型
"""

from sqlalchemy import Column, String, Integer, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from datetime import datetime

from app.models.base import BaseModel, UserTrackingMixin


class UploadedFile(BaseModel, UserTrackingMixin):
    """上传文件模型"""
    
    __tablename__ = "uploaded_file"
    __table_args__ = {'comment': '上传文件表'}
    
    # 文件基本信息
    original_filename = Column(
        String(255),
        nullable=False,
        comment="原始文件名"
    )
    
    stored_filename = Column(
        String(255),
        nullable=False,
        comment="存储文件名"
    )
    
    file_path = Column(
        String(500),
        nullable=False,
        comment="文件路径"
    )
    
    file_size = Column(
        Integer,
        nullable=False,
        comment="文件大小(字节)"
    )
    
    file_type = Column(
        String(100),
        nullable=False,
        comment="文件类型(MIME)"
    )
    
    file_extension = Column(
        String(10),
        nullable=True,
        comment="文件扩展名"
    )
    
    # 处理状态
    status = Column(
        String(20),
        default="uploaded",
        nullable=False,
        comment="处理状态: uploaded, processing, processed, failed"
    )
    
    # 处理结果
    processing_result = Column(
        JSON,
        nullable=True,
        comment="处理结果(JSON)"
    )
    
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    # 元数据
    metadata = Column(
        JSON,
        nullable=True,
        comment="文件元数据(JSON)"
    )
    
    # 处理时间
    processed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="处理完成时间"
    )
    
    # 关联关系
    extracted_data = relationship(
        "ExtractedData",
        back_populates="file",
        cascade="all, delete-orphan"
    )


class ExtractedData(BaseModel, UserTrackingMixin):
    """提取数据模型"""
    
    __tablename__ = "extracted_data"
    __table_args__ = {'comment': '提取数据表'}
    
    file_id = Column(
        String(50),
        ForeignKey('uploaded_file.id'),
        nullable=False,
        index=True,
        comment="文件ID"
    )
    
    # 数据类型
    data_type = Column(
        String(50),
        nullable=False,
        comment="数据类型: order, inventory, equipment, email_table"
    )
    
    # 表格信息
    table_name = Column(
        String(100),
        nullable=True,
        comment="表格名称"
    )
    
    sheet_name = Column(
        String(100),
        nullable=True,
        comment="工作表名称"
    )
    
    # 原始数据
    raw_data = Column(
        JSON,
        nullable=False,
        comment="原始数据(JSON)"
    )
    
    # 处理后数据
    processed_data = Column(
        JSON,
        nullable=True,
        comment="处理后数据(JSON)"
    )
    
    # 字段映射
    field_mapping = Column(
        JSON,
        nullable=True,
        comment="字段映射配置(JSON)"
    )
    
    # 验证结果
    validation_result = Column(
        JSON,
        nullable=True,
        comment="数据验证结果(JSON)"
    )
    
    # 统计信息
    row_count = Column(
        Integer,
        default=0,
        comment="数据行数"
    )
    
    column_count = Column(
        Integer,
        default=0,
        comment="数据列数"
    )
    
    # 导入状态
    import_status = Column(
        String(20),
        default="pending",
        nullable=False,
        comment="导入状态: pending, imported, failed"
    )
    
    imported_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="导入时间"
    )
    
    # 关联关系
    file = relationship("UploadedFile", back_populates="extracted_data")


class DataImportLog(BaseModel, UserTrackingMixin):
    """数据导入日志模型"""
    
    __tablename__ = "data_import_log"
    __table_args__ = {'comment': '数据导入日志表'}
    
    file_id = Column(
        String(50),
        ForeignKey('uploaded_file.id'),
        nullable=False,
        index=True,
        comment="文件ID"
    )
    
    extracted_data_id = Column(
        String(50),
        ForeignKey('extracted_data.id'),
        nullable=True,
        index=True,
        comment="提取数据ID"
    )
    
    # 导入信息
    target_table = Column(
        String(100),
        nullable=False,
        comment="目标表名"
    )
    
    operation_type = Column(
        String(20),
        nullable=False,
        comment="操作类型: insert, update, upsert"
    )
    
    # 统计信息
    total_records = Column(
        Integer,
        default=0,
        comment="总记录数"
    )
    
    success_records = Column(
        Integer,
        default=0,
        comment="成功记录数"
    )
    
    failed_records = Column(
        Integer,
        default=0,
        comment="失败记录数"
    )
    
    # 结果详情
    import_result = Column(
        JSON,
        nullable=True,
        comment="导入结果详情(JSON)"
    )
    
    error_details = Column(
        JSON,
        nullable=True,
        comment="错误详情(JSON)"
    )
    
    # 执行时间
    started_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        comment="开始时间"
    )
    
    completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="完成时间"
    )
    
    duration_seconds = Column(
        Integer,
        nullable=True,
        comment="执行时长(秒)"
    )


class FileProcessingQueue(BaseModel):
    """文件处理队列模型"""
    
    __tablename__ = "file_processing_queue"
    __table_args__ = {'comment': '文件处理队列表'}
    
    file_id = Column(
        String(50),
        ForeignKey('uploaded_file.id'),
        nullable=False,
        index=True,
        comment="文件ID"
    )
    
    # 处理类型
    processing_type = Column(
        String(50),
        nullable=False,
        comment="处理类型: parse_excel, parse_email, extract_tables"
    )
    
    # 优先级
    priority = Column(
        Integer,
        default=5,
        comment="优先级(1-10, 数字越小优先级越高)"
    )
    
    # 状态
    status = Column(
        String(20),
        default="pending",
        nullable=False,
        comment="状态: pending, processing, completed, failed"
    )
    
    # 处理参数
    processing_params = Column(
        JSON,
        nullable=True,
        comment="处理参数(JSON)"
    )
    
    # 重试信息
    retry_count = Column(
        Integer,
        default=0,
        comment="重试次数"
    )
    
    max_retries = Column(
        Integer,
        default=3,
        comment="最大重试次数"
    )
    
    # 时间信息
    scheduled_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        comment="计划执行时间"
    )
    
    started_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="开始处理时间"
    )
    
    completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="完成时间"
    )
    
    # 错误信息
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    # 关联关系
    file = relationship("UploadedFile")
