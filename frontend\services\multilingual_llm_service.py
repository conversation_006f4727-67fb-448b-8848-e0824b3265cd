"""
多语言LLM服务
支持中英文对话和智能响应
"""

import json
from typing import Dict, List, Any
from .llm_service import EnhancedLLMService


class MultilingualLLMService(EnhancedLLMService):
    """多语言LLM服务"""
    
    def __init__(self):
        super().__init__()
        self.response_templates = {
            "zh": {
                "production_planning": """基于当前系统数据分析，我为您提供以下生产计划建议：

📊 **当前状态分析**：
- 生产线L01正常运行，产能50件/小时
- 生产线L02停线（原料短缺），需要调整
- 储罐Tank01-Tank05状态良好

🎯 **优化建议**：
1. **设备调整**：将L02的订单转移到L01或L04
2. **物料优化**：优先消耗超过180天的老旧物料
3. **排程优化**：建议延长L01工作时间补偿L02停线

📈 **预期效果**：
- 总体延期：1-2天
- 产能利用率：提升至85%
- 成本影响：增加5-8%

🚀 **执行建议**：
我建议立即生成新的生产计划，整合当前设备状态和物料约束。是否需要我为您生成详细的生产计划？""",
                
                "equipment": """关于设备管理，我基于当前数据为您分析：

⚙️ **设备状态概览**：
- L01: 🟢 正常运行 (利用率85.2%)
- L02: 🔴 停线 (原料短缺)
- L03: 🟠 维护中 (预防性维护)
- L04: 🔵 忙碌生产 (利用率76.3%)

🔍 **问题分析**：
- L02停线影响总产能45件/小时
- 需要及时解决原料供应问题
- L03维护预计今晚20:00完成

💡 **建议措施**：
1. 立即联系供应商解决L02原料问题
2. 将L02订单临时分配给L01和L04
3. 监控L03维护进度，确保按时完成
4. 考虑启用备用设备应对紧急情况

是否需要我帮您调整设备配置或生成设备维护计划？""",
                
                "quality": """关于质量管理，我为您提供以下分析：

📊 **质量状态分析**：
- 整体合格率：98.5%
- 最近7天测试：156次
- 关键质量指标：正常范围内

🎯 **质量趋势**：
- 质量表现稳定向好
- 无重大质量事故
- 持续改进效果显著

💡 **优化建议**：
1. 继续保持当前质量标准
2. 加强过程控制监测
3. 定期校准检测设备
4. 强化员工质量意识培训

📈 **预防措施**：
- 建立质量预警机制
- 实施统计过程控制
- 定期质量审核

是否需要我为您生成详细的质量改进计划？""",
                
                "inventory": """关于库存管理，我基于PCI数据为您分析：

📦 **库存状态**：
- FS物料总数：245个
- 超过180天物料：12个（需优先消耗）
- 高优先级物料：8个

🔄 **FIFO建议**：
1. 优先安排超龄物料的生产任务
2. 调整生产顺序，确保先进先出
3. 监控物料消耗速度

⚠️ **风险提醒**：
- 部分物料接近过期，需加快消耗
- 建议调整采购计划，避免积压

💡 **优化方案**：
- 制定专项消耗计划
- 优化库存周转率
- 建立预警机制

是否需要我为您生成库存优化计划和消耗策略？""",
                
                "general": """感谢您的咨询！作为您的专业生产管理AI助手，我可以为您提供：

🏭 **生产管理服务**：
- 生产计划优化与调整
- 设备状态监控与维护建议
- 质量管理分析与改进
- 库存优化与FIFO管理

📊 **数据分析能力**：
- 实时数据解读
- 趋势分析预测
- 异常检测预警
- 性能指标评估

🎯 **智能决策支持**：
- 基于数据的决策建议
- 多目标优化方案
- 风险评估与预防
- 持续改进建议

请告诉我您具体需要哪方面的帮助，我会基于当前系统数据为您提供专业的分析和建议。"""
            },
            
            "en": {
                "production_planning": """Based on current system data analysis, I provide the following production planning recommendations:

📊 **Current Status Analysis**:
- Production Line L01 running normally, capacity 50 units/hour
- Production Line L02 stopped (material shortage), needs adjustment
- Tanks Tank01-Tank05 in good condition

🎯 **Optimization Recommendations**:
1. **Equipment Adjustment**: Transfer L02 orders to L01 or L04
2. **Material Optimization**: Prioritize consumption of materials over 180 days old
3. **Scheduling Optimization**: Suggest extending L01 working hours to compensate for L02 downtime

📈 **Expected Results**:
- Overall delay: 1-2 days
- Capacity utilization: Increase to 85%
- Cost impact: Increase 5-8%

🚀 **Implementation Recommendation**:
I recommend immediately generating a new production plan that integrates current equipment status and material constraints. Would you like me to generate a detailed production plan for you?""",
                
                "equipment": """Regarding equipment management, I analyze based on current data:

⚙️ **Equipment Status Overview**:
- L01: 🟢 Running normally (utilization 85.2%)
- L02: 🔴 Stopped (material shortage)
- L03: 🟠 Under maintenance (preventive maintenance)
- L04: 🔵 Busy production (utilization 76.3%)

🔍 **Problem Analysis**:
- L02 downtime affects total capacity by 45 units/hour
- Need to resolve material supply issue promptly
- L03 maintenance expected to complete at 20:00 tonight

💡 **Recommended Actions**:
1. Contact suppliers immediately to resolve L02 material issue
2. Temporarily allocate L02 orders to L01 and L04
3. Monitor L03 maintenance progress to ensure timely completion
4. Consider activating backup equipment for emergencies

Would you like me to help adjust equipment configuration or generate equipment maintenance plan?""",
                
                "quality": """Regarding quality management, I provide the following analysis:

📊 **Quality Status Analysis**:
- Overall pass rate: 98.5%
- Tests in last 7 days: 156
- Key quality indicators: Within normal range

🎯 **Quality Trends**:
- Quality performance steadily improving
- No major quality incidents
- Continuous improvement showing significant results

💡 **Optimization Recommendations**:
1. Continue maintaining current quality standards
2. Strengthen process control monitoring
3. Regular calibration of testing equipment
4. Enhance employee quality awareness training

📈 **Preventive Measures**:
- Establish quality early warning system
- Implement statistical process control
- Regular quality audits

Would you like me to generate a detailed quality improvement plan for you?""",
                
                "inventory": """Regarding inventory management, I analyze based on PCI data:

📦 **Inventory Status**:
- Total FS materials: 245 items
- Materials over 180 days: 12 items (need priority consumption)
- High priority materials: 8 items

🔄 **FIFO Recommendations**:
1. Prioritize production tasks for aged materials
2. Adjust production sequence to ensure first-in-first-out
3. Monitor material consumption rate

⚠️ **Risk Alerts**:
- Some materials approaching expiration, need accelerated consumption
- Recommend adjusting procurement plan to avoid accumulation

💡 **Optimization Solutions**:
- Develop special consumption plan
- Optimize inventory turnover rate
- Establish early warning system

Would you like me to generate inventory optimization plan and consumption strategy for you?""",
                
                "general": """Thank you for your inquiry! As your professional production management AI assistant, I can provide:

🏭 **Production Management Services**:
- Production planning optimization and adjustment
- Equipment status monitoring and maintenance recommendations
- Quality management analysis and improvement
- Inventory optimization and FIFO management

📊 **Data Analysis Capabilities**:
- Real-time data interpretation
- Trend analysis and forecasting
- Anomaly detection and alerts
- Performance indicator assessment

🎯 **Intelligent Decision Support**:
- Data-driven decision recommendations
- Multi-objective optimization solutions
- Risk assessment and prevention
- Continuous improvement suggestions

Please tell me specifically what kind of help you need, and I will provide professional analysis and recommendations based on current system data."""
            }
        }
    
    def _generate_production_planning_response(self, user_message: str, prompt: str, language: str = "zh") -> str:
        """生成生产计划相关响应"""
        return self.response_templates[language]["production_planning"]
    
    def _generate_equipment_response(self, user_message: str, prompt: str, language: str = "zh") -> str:
        """生成设备相关响应"""
        return self.response_templates[language]["equipment"]
    
    def _generate_quality_response(self, user_message: str, prompt: str, language: str = "zh") -> str:
        """生成质量相关响应"""
        return self.response_templates[language]["quality"]
    
    def _generate_inventory_response(self, user_message: str, prompt: str, language: str = "zh") -> str:
        """生成库存相关响应"""
        return self.response_templates[language]["inventory"]
    
    def _generate_general_response(self, user_message: str, prompt: str, language: str = "zh") -> str:
        """生成通用响应"""
        return self.response_templates[language]["general"]
    
    def _analyze_plan_generation_need(self, user_message: str, llm_response: Dict[str, Any], language: str = "zh") -> Dict[str, Any]:
        """分析是否需要生成生产计划"""
        if language == "en":
            plan_keywords = ["generate plan", "adjust plan", "optimize schedule", "reschedule", "create plan"]
            message_needs_plan = any(keyword in user_message.lower() for keyword in plan_keywords)
            response_suggests_plan = any(keyword in llm_response["content"].lower() for keyword in ["generate", "create", "adjust"])
            
            if message_needs_plan or response_suggests_plan:
                return {
                    "needed": True,
                    "type": "production_plan",
                    "reason": "User request or AI suggestion to generate production plan",
                    "priority": "high" if message_needs_plan else "medium"
                }
        else:
            plan_keywords = ["生成计划", "调整计划", "优化排程", "重新安排", "制定计划"]
            message_needs_plan = any(keyword in user_message for keyword in plan_keywords)
            response_suggests_plan = any(keyword in llm_response["content"] for keyword in ["生成", "制定", "调整"])
            
            if message_needs_plan or response_suggests_plan:
                return {
                    "needed": True,
                    "type": "production_plan",
                    "reason": "用户请求或AI建议生成生产计划",
                    "priority": "high" if message_needs_plan else "medium"
                }
        
        return {"needed": False}


# 全局多语言LLM服务实例
multilingual_llm_service = MultilingualLLMService()
