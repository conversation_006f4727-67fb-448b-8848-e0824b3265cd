"""
Streamlit主题配置
"""

import streamlit as st


def apply_custom_theme():
    """应用自定义主题"""
    
    # 自定义CSS样式
    custom_css = """
    <style>
    /* 主要颜色配置 */
    :root {
        --primary-color: #FF6B6B;
        --background-color: #FFFFFF;
        --secondary-background-color: #F0F2F6;
        --text-color: #262730;
        --border-color: #E0E0E0;
    }
    
    /* 隐藏Streamlit默认元素 */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    
    /* 自定义侧边栏样式 */
    .css-1d391kg {
        background-color: var(--secondary-background-color);
    }
    
    /* 自定义按钮样式 */
    .stButton > button {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
    }
    
    /* 自定义指标卡片样式 */
    [data-testid="metric-container"] {
        background-color: var(--background-color);
        border: 1px solid var(--border-color);
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* 自定义文件上传器样式 */
    .stFileUploader > div > div {
        border: 2px dashed var(--border-color);
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        transition: border-color 0.3s ease;
    }
    
    .stFileUploader > div > div:hover {
        border-color: var(--primary-color);
    }
    
    /* 自定义数据框样式 */
    .stDataFrame {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* 自定义选择框样式 */
    .stSelectbox > div > div {
        border-radius: 8px;
    }
    
    /* 自定义输入框样式 */
    .stTextInput > div > div > input {
        border-radius: 8px;
    }
    
    /* 自定义多选框样式 */
    .stMultiSelect > div > div {
        border-radius: 8px;
    }
    
    /* 自定义标签页样式 */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }
    
    .stTabs [data-baseweb="tab"] {
        border-radius: 8px 8px 0 0;
        padding: 0.5rem 1rem;
    }
    
    /* 自定义警告框样式 */
    .stAlert {
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
    }
    
    /* 自定义进度条样式 */
    .stProgress > div > div {
        border-radius: 8px;
    }
    
    /* 自定义图表容器样式 */
    .js-plotly-plot {
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .css-1d391kg {
            width: 100% !important;
        }
        
        [data-testid="metric-container"] {
            margin-bottom: 1rem;
        }
    }
    
    /* 自定义加载动画 */
    .stSpinner > div {
        border-top-color: var(--primary-color) !important;
    }
    
    /* 自定义成功消息样式 */
    .stSuccess {
        background-color: #D4EDDA;
        border-color: #C3E6CB;
        color: #155724;
    }
    
    /* 自定义错误消息样式 */
    .stError {
        background-color: #F8D7DA;
        border-color: #F5C6CB;
        color: #721C24;
    }
    
    /* 自定义警告消息样式 */
    .stWarning {
        background-color: #FFF3CD;
        border-color: #FFEAA7;
        color: #856404;
    }
    
    /* 自定义信息消息样式 */
    .stInfo {
        background-color: #D1ECF1;
        border-color: #BEE5EB;
        color: #0C5460;
    }
    
    /* 自定义代码块样式 */
    .stCode {
        background-color: #F8F9FA;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 1rem;
    }
    
    /* 自定义表格样式 */
    .stTable {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .stTable th {
        background-color: var(--secondary-background-color);
        font-weight: 600;
    }
    
    /* 自定义展开器样式 */
    .streamlit-expanderHeader {
        border-radius: 8px;
        background-color: var(--secondary-background-color);
    }
    
    /* 自定义容器样式 */
    .stContainer {
        padding: 1rem;
        border-radius: 8px;
        background-color: var(--background-color);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* 自定义分隔线样式 */
    hr {
        border: none;
        height: 1px;
        background-color: var(--border-color);
        margin: 2rem 0;
    }
    
    /* 自定义链接样式 */
    a {
        color: var(--primary-color);
        text-decoration: none;
    }
    
    a:hover {
        text-decoration: underline;
    }
    
    /* 自定义标题样式 */
    h1, h2, h3, h4, h5, h6 {
        color: var(--text-color);
        font-weight: 600;
    }
    
    /* 自定义段落样式 */
    p {
        color: var(--text-color);
        line-height: 1.6;
    }
    
    /* 自定义列表样式 */
    ul, ol {
        color: var(--text-color);
    }
    
    /* 自定义引用样式 */
    blockquote {
        border-left: 4px solid var(--primary-color);
        padding-left: 1rem;
        margin-left: 0;
        font-style: italic;
        color: #666;
    }
    </style>
    """
    
    st.markdown(custom_css, unsafe_allow_html=True)


def get_chart_theme():
    """获取图表主题配置"""
    return {
        "layout": {
            "colorway": [
                "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", 
                "#FFEAA7", "#DDA0DD", "#98D8C8", "#F7DC6F"
            ],
            "font": {
                "family": "Arial, sans-serif",
                "size": 12,
                "color": "#262730"
            },
            "paper_bgcolor": "#FFFFFF",
            "plot_bgcolor": "#FFFFFF",
            "hovermode": "closest"
        }
    }


def apply_plotly_theme(fig):
    """为Plotly图表应用主题"""
    theme = get_chart_theme()
    
    fig.update_layout(
        font=theme["layout"]["font"],
        paper_bgcolor=theme["layout"]["paper_bgcolor"],
        plot_bgcolor=theme["layout"]["plot_bgcolor"],
        hovermode=theme["layout"]["hovermode"],
        colorway=theme["layout"]["colorway"]
    )
    
    return fig
