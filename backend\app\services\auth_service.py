"""
认证服务
"""

from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
import logging

from app.models.user import User, UserSession
from app.core.security import password_manager
from app.core.config import settings
from .ldap_service import LDAPService
from .sso_service import SSOService

logger = logging.getLogger(__name__)


class AuthService:
    """认证服务类 - 支持本地、LDAP和SSO认证"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.ldap_service = LDAPService(db) if settings.LDAP_ENABLED else None
        self.sso_service = SSOService(db) if settings.SSO_ENABLED else None

    async def authenticate_user(
        self,
        username: str,
        password: str,
        ip_address: str = None,
        auth_method: str = "auto"  # auto, local, ldap
    ) -> Optional[User]:
        """验证用户身份 - 支持多种认证方式"""
        try:
            # 自动检测认证方式
            if auth_method == "auto":
                # 优先尝试LDAP认证（如果启用）
                if self.ldap_service:
                    ldap_user = self.ldap_service.authenticate_user(username, password)
                    if ldap_user:
                        # 同步LDAP用户到数据库
                        user = await self.ldap_service.sync_user_to_database(ldap_user)
                        if user and ip_address:
                            user.update_login_info(ip_address)
                            await self.db.commit()
                        return user

                # 回退到本地认证
                return await self._authenticate_local_user(username, password, ip_address)

            elif auth_method == "ldap":
                if not self.ldap_service:
                    logger.error("LDAP认证未启用")
                    return None

                ldap_user = self.ldap_service.authenticate_user(username, password)
                if ldap_user:
                    user = await self.ldap_service.sync_user_to_database(ldap_user)
                    if user and ip_address:
                        user.update_login_info(ip_address)
                        await self.db.commit()
                    return user
                return None

            else:  # local
                return await self._authenticate_local_user(username, password, ip_address)

        except Exception as e:
            logger.error(f"用户认证失败: {str(e)}")
            await self.db.rollback()
            return None

    async def _authenticate_local_user(
        self,
        username: str,
        password: str,
        ip_address: str = None
    ) -> Optional[User]:
        """本地用户认证"""
        try:
            # 查找用户（支持用户名或邮箱登录）
            stmt = select(User).where(
                (User.username == username) | (User.email == username),
                User.is_deleted == False
            )
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()

            if not user:
                return None

            # 检查账户锁定
            if user.is_locked():
                return None

            # 验证密码
            if not password_manager.verify_password(password, user.password_hash):
                # 增加失败登录次数
                await self._increment_failed_login(user.id)
                return None

            # 更新登录信息
            if ip_address:
                user.update_login_info(ip_address)
                await self.db.commit()

            return user

        except Exception as e:
            logger.error(f"本地用户认证失败: {str(e)}")
            await self.db.rollback()
            return None

    async def _increment_failed_login(self, user_id: str):
        """增加失败登录次数"""
        try:
            stmt = select(User).where(User.id == user_id)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()

            if user:
                failed_attempts = int(user.failed_login_attempts) + 1
                user.failed_login_attempts = str(failed_attempts)

                # 如果失败次数超过5次，锁定账户30分钟
                if failed_attempts >= 5:
                    user.locked_until = datetime.utcnow() + timedelta(minutes=30)
                    logger.warning(f"用户账户已锁定: {user.username}")

                await self.db.commit()

        except Exception as e:
            logger.error(f"更新失败登录次数失败: {str(e)}")
            await self.db.rollback()

    async def create_session(
        self,
        user_id: str,
        access_token: str,
        refresh_token: str,
        ip_address: str = None,
        user_agent: str = None
    ) -> UserSession:
        """创建用户会话"""
        try:
            # 计算过期时间
            expires_at = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )

            # 创建会话记录
            session = UserSession(
                user_id=user_id,
                session_token=access_token,
                refresh_token=refresh_token,
                expires_at=expires_at,
                ip_address=ip_address,
                user_agent=user_agent,
                is_active=True
            )

            self.db.add(session)
            await self.db.commit()
            await self.db.refresh(session)

            return session

        except Exception as e:
            logger.error(f"创建用户会话失败: {str(e)}")
            await self.db.rollback()
            raise

    async def get_session_by_token(self, token: str) -> Optional[UserSession]:
        """根据访问令牌获取会话"""
        try:
            stmt = select(UserSession).where(
                UserSession.session_token == token,
                UserSession.is_active == True,
                UserSession.is_deleted == False
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"获取会话失败: {str(e)}")
            return None

    async def get_session_by_refresh_token(self, refresh_token: str) -> Optional[UserSession]:
        """根据刷新令牌获取会话"""
        try:
            stmt = select(UserSession).where(
                UserSession.refresh_token == refresh_token,
                UserSession.is_active == True,
                UserSession.is_deleted == False
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"获取会话失败: {str(e)}")
            return None

    async def update_session_token(self, session_id: str, new_token: str):
        """更新会话令牌"""
        try:
            # 计算新的过期时间
            new_expires_at = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )

            stmt = update(UserSession).where(
                UserSession.id == session_id
            ).values(
                session_token=new_token,
                expires_at=new_expires_at,
                updated_at=datetime.utcnow()
            )

            await self.db.execute(stmt)
            await self.db.commit()

        except Exception as e:
            logger.error(f"更新会话令牌失败: {str(e)}")
            await self.db.rollback()
            raise

    async def logout_session(self, token: str):
        """注销会话"""
        try:
            stmt = update(UserSession).where(
                UserSession.session_token == token
            ).values(
                is_active=False,
                updated_at=datetime.utcnow()
            )

            await self.db.execute(stmt)
            await self.db.commit()

        except Exception as e:
            logger.error(f"注销会话失败: {str(e)}")
            await self.db.rollback()
            raise

    async def logout_all_sessions(self, user_id: str):
        """注销用户所有会话"""
        try:
            stmt = update(UserSession).where(
                UserSession.user_id == user_id,
                UserSession.is_active == True
            ).values(
                is_active=False,
                updated_at=datetime.utcnow()
            )

            await self.db.execute(stmt)
            await self.db.commit()

        except Exception as e:
            logger.error(f"注销所有会话失败: {str(e)}")
            await self.db.rollback()
            raise

    async def cleanup_expired_sessions(self):
        """清理过期会话"""
        try:
            current_time = datetime.utcnow()

            stmt = update(UserSession).where(
                UserSession.expires_at < current_time,
                UserSession.is_active == True
            ).values(
                is_active=False,
                updated_at=current_time
            )

            result = await self.db.execute(stmt)
            await self.db.commit()

            cleaned_count = result.rowcount
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个过期会话")

            return cleaned_count

        except Exception as e:
            logger.error(f"清理过期会话失败: {str(e)}")
            await self.db.rollback()
            return 0

    async def get_active_sessions(self, user_id: str) -> list:
        """获取用户活跃会话"""
        try:
            stmt = select(UserSession).where(
                UserSession.user_id == user_id,
                UserSession.is_active == True,
                UserSession.expires_at > datetime.utcnow(),
                UserSession.is_deleted == False
            ).order_by(UserSession.created_at.desc())

            result = await self.db.execute(stmt)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"获取活跃会话失败: {str(e)}")
            return []

    # SSO相关方法
    def get_sso_login_url(self, state: str = None) -> Optional[str]:
        """获取SSO登录URL"""
        if not self.sso_service:
            return None

        try:
            if settings.SSO_TYPE in ['oauth', 'oidc']:
                return self.sso_service.get_oauth_login_url(state)
            elif settings.SSO_TYPE == 'saml':
                return self.sso_service.get_saml_login_url(state)
            else:
                return None
        except Exception as e:
            logger.error(f"获取SSO登录URL失败: {str(e)}")
            return None

    async def handle_sso_callback(self, **kwargs) -> Optional[User]:
        """处理SSO回调"""
        if not self.sso_service:
            return None

        try:
            if settings.SSO_TYPE in ['oauth', 'oidc']:
                code = kwargs.get('code')
                state = kwargs.get('state')
                if not code:
                    return None

                sso_user = self.sso_service.handle_oauth_callback(code, state)
            elif settings.SSO_TYPE == 'saml':
                saml_response = kwargs.get('saml_response')
                relay_state = kwargs.get('relay_state')
                if not saml_response:
                    return None

                sso_user = self.sso_service.handle_saml_response(saml_response, relay_state)
            else:
                return None

            if sso_user:
                # 同步SSO用户到数据库
                user = await self.sso_service.sync_user_to_database(sso_user)
                return user

            return None

        except Exception as e:
            logger.error(f"处理SSO回调失败: {str(e)}")
            return None

    def test_ldap_connection(self) -> Dict[str, Any]:
        """测试LDAP连接"""
        if not self.ldap_service:
            return {
                'success': False,
                'message': 'LDAP服务未启用'
            }

        return self.ldap_service.test_connection()

    def test_sso_config(self) -> Dict[str, Any]:
        """测试SSO配置"""
        if not self.sso_service:
            return {
                'success': False,
                'message': 'SSO服务未启用'
            }

        return self.sso_service.test_sso_config()

    def get_auth_methods(self) -> Dict[str, bool]:
        """获取可用的认证方法"""
        return {
            'local': True,  # 本地认证始终可用
            'ldap': settings.LDAP_ENABLED and self.ldap_service is not None,
            'sso': settings.SSO_ENABLED and self.sso_service is not None,
            'sso_type': settings.SSO_TYPE if settings.SSO_ENABLED else None
        }
