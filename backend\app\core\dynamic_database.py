"""
动态数据库配置管理器
支持运行时添加新的数据库类型和连接
"""

import logging
from typing import Dict, List, Any, Optional, Union
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import text
import os
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class DatabaseEngine(str, Enum):
    """支持的数据库引擎类型"""
    MYSQL = "mysql"
    ORACLE = "oracle"
    POSTGRESQL = "postgresql"
    SQLSERVER = "sqlserver"
    SQLITE = "sqlite"


@dataclass
class DatabaseConfig:
    """数据库配置"""
    name: str                           # 数据库名称/标识
    engine_type: DatabaseEngine         # 数据库引擎类型
    host: str                          # 主机地址
    port: int                          # 端口
    username: str                      # 用户名
    password: str                      # 密码
    database: str                      # 数据库名
    description: str = ""              # 描述
    connection_url: Optional[str] = None  # 完整连接字符串
    extra_params: Dict[str, Any] = None  # 额外参数

    def __post_init__(self):
        if self.extra_params is None:
            self.extra_params = {}

    def get_connection_url(self) -> str:
        """生成连接字符串"""
        if self.connection_url:
            return self.connection_url

        # 根据数据库类型生成连接字符串
        if self.engine_type == DatabaseEngine.MYSQL:
            driver = "aiomysql"
            charset = self.extra_params.get("charset", "utf8mb4")
            return f"mysql+{driver}://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={charset}"

        elif self.engine_type == DatabaseEngine.ORACLE:
            driver = "oracledb"
            service_name = self.extra_params.get("service_name", self.database)
            return f"oracle+{driver}://{self.username}:{self.password}@{self.host}:{self.port}/?service_name={service_name}"

        elif self.engine_type == DatabaseEngine.POSTGRESQL:
            driver = "asyncpg"
            return f"postgresql+{driver}://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

        elif self.engine_type == DatabaseEngine.SQLSERVER:
            driver = "aioodbc"
            return f"mssql+{driver}://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?driver=ODBC+Driver+17+for+SQL+Server"

        elif self.engine_type == DatabaseEngine.SQLITE:
            return f"sqlite+aiosqlite:///{self.database}"

        else:
            raise ValueError(f"不支持的数据库引擎类型: {self.engine_type}")


class DynamicDatabaseManager:
    """动态数据库管理器"""

    def __init__(self):
        self.database_configs: Dict[str, DatabaseConfig] = {}
        self.engines: Dict[str, Any] = {}
        self.session_factories: Dict[str, Any] = {}
        self._load_from_environment()

    def _load_from_environment(self):
        """从环境变量加载数据库配置"""
        # 预定义的数据库类型配置
        predefined_configs = {
            "main": ("DB_", DatabaseEngine.MYSQL, "主数据库"),
            "inventory": ("INVENTORY_DB_", DatabaseEngine.MYSQL, "库存数据库"),
            "production_analysis": ("PRODUCTION_ANALYSIS_DB_", DatabaseEngine.MYSQL, "生产数据分析数据库"),
            "erp": ("ERP_DB_", DatabaseEngine.MYSQL, "ERP系统数据库"),
            "mes": ("MES_DB_", DatabaseEngine.MYSQL, "MES系统数据库"),
            "pci": ("PCI_DB_", DatabaseEngine.MYSQL, "PCI数据库"),
            "quality": ("QUALITY_DB_", DatabaseEngine.MYSQL, "质量数据库"),
            "logistics": ("LOGISTICS_DB_", DatabaseEngine.MYSQL, "物流数据库"),
            "finance": ("FINANCE_DB_", DatabaseEngine.MYSQL, "财务数据库"),
            "hr": ("HR_DB_", DatabaseEngine.MYSQL, "人力资源数据库"),
            "warehouse": ("WAREHOUSE_DB_", DatabaseEngine.MYSQL, "仓储数据库"),
            "maintenance": ("MAINTENANCE_DB_", DatabaseEngine.MYSQL, "维护数据库"),
            "custom1": ("CUSTOM1_DB_", DatabaseEngine.MYSQL, "自定义数据库1"),
            "custom2": ("CUSTOM2_DB_", DatabaseEngine.MYSQL, "自定义数据库2"),
            "custom3": ("CUSTOM3_DB_", DatabaseEngine.MYSQL, "自定义数据库3"),
        }

        for db_name, (prefix, default_engine, description) in predefined_configs.items():
            self._load_database_config(db_name, prefix, default_engine, description)

    def _load_database_config(self, db_name: str, prefix: str, default_engine: DatabaseEngine, description: str):
        """加载单个数据库配置"""
        # 检查是否有完整的连接URL
        url_key = f"{prefix}URL" if prefix.endswith("_") else f"{prefix}_URL"
        connection_url = os.getenv(url_key)

        if connection_url:
            # 从URL推断数据库类型
            if connection_url.startswith("mysql"):
                engine_type = DatabaseEngine.MYSQL
            elif connection_url.startswith("oracle"):
                engine_type = DatabaseEngine.ORACLE
            elif connection_url.startswith("postgresql"):
                engine_type = DatabaseEngine.POSTGRESQL
            elif connection_url.startswith("mssql"):
                engine_type = DatabaseEngine.SQLSERVER
            elif connection_url.startswith("sqlite"):
                engine_type = DatabaseEngine.SQLITE
            else:
                engine_type = default_engine

            config = DatabaseConfig(
                name=db_name,
                engine_type=engine_type,
                host="",
                port=0,
                username="",
                password="",
                database="",
                description=description,
                connection_url=connection_url
            )
            self.add_database_config(config)

        else:
            # 检查分项配置
            host = os.getenv(f"{prefix}HOST")
            port = os.getenv(f"{prefix}PORT")
            username = os.getenv(f"{prefix}USER")
            password = os.getenv(f"{prefix}PASSWORD")
            database = os.getenv(f"{prefix}NAME")
            engine_str = os.getenv(f"{prefix}ENGINE", default_engine.value)

            if all([host, port, username, password, database]):
                try:
                    engine_type = DatabaseEngine(engine_str)
                except ValueError:
                    engine_type = default_engine

                config = DatabaseConfig(
                    name=db_name,
                    engine_type=engine_type,
                    host=host,
                    port=int(port),
                    username=username,
                    password=password,
                    database=database,
                    description=description
                )
                self.add_database_config(config)

    def add_database_config(self, config: DatabaseConfig) -> bool:
        """添加数据库配置"""
        try:
            self.database_configs[config.name] = config

            # 创建数据库引擎
            engine = create_async_engine(
                config.get_connection_url(),
                echo=False,
                pool_size=10,
                max_overflow=20,
                pool_timeout=30,
                pool_recycle=3600,
                pool_pre_ping=True,
            )

            self.engines[config.name] = engine

            # 创建会话工厂
            self.session_factories[config.name] = async_sessionmaker(
                engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False
            )

            logger.info(f"✅ 数据库配置添加成功: {config.name} ({config.engine_type.value})")
            return True

        except Exception as e:
            logger.error(f"❌ 添加数据库配置失败 {config.name}: {str(e)}")
            return False

    async def remove_database_config(self, db_name: str) -> bool:
        """移除数据库配置"""
        try:
            if db_name in self.engines:
                # 关闭引擎
                engine = self.engines[db_name]
                if engine:
                    try:
                        await engine.dispose()
                        logger.info(f"✅ 数据库引擎 {db_name} 已关闭")
                    except Exception as e:
                        logger.warning(f"⚠️ 关闭数据库引擎失败 {db_name}: {str(e)}")

                # 清理配置
                if db_name in self.engines:
                    del self.engines[db_name]
                if db_name in self.session_factories:
                    del self.session_factories[db_name]
                if db_name in self.database_configs:
                    del self.database_configs[db_name]

                logger.info(f"✅ 数据库配置移除成功: {db_name}")
                return True
            else:
                logger.warning(f"⚠️ 数据库配置 {db_name} 不存在")
                return False
        except Exception as e:
            logger.error(f"❌ 移除数据库配置失败 {db_name}: {str(e)}")
            return False

    def get_database_config(self, db_name: str) -> Optional[DatabaseConfig]:
        """获取数据库配置"""
        return self.database_configs.get(db_name)

    def list_database_configs(self) -> List[DatabaseConfig]:
        """列出所有数据库配置"""
        return list(self.database_configs.values())

    def is_database_configured(self, db_name: str) -> bool:
        """检查数据库是否已配置"""
        return db_name in self.database_configs

    async def get_session(self, db_name: str) -> AsyncSession:
        """获取数据库会话"""
        session_factory = self.session_factories.get(db_name)
        if not session_factory:
            raise ValueError(f"数据库 {db_name} 未配置")
        return session_factory()

    async def execute_query(self, db_name: str, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """执行查询"""
        try:
            async with self.get_session(db_name) as session:
                result = await session.execute(text(query), params or {})
                columns = result.keys()
                rows = result.fetchall()
                return [dict(zip(columns, row)) for row in rows]
        except Exception as e:
            logger.error(f"查询数据库 {db_name} 失败: {str(e)}")
            raise

    async def health_check(self, db_name: str) -> bool:
        """数据库健康检查"""
        try:
            async with self.get_session(db_name) as session:
                # 根据数据库类型使用不同的健康检查查询
                config = self.get_database_config(db_name)
                if config.engine_type == DatabaseEngine.ORACLE:
                    await session.execute(text("SELECT 1 FROM DUAL"))
                else:
                    await session.execute(text("SELECT 1"))
                return True
        except Exception as e:
            logger.error(f"数据库健康检查失败 {db_name}: {str(e)}")
            return False

    async def health_check_all(self) -> Dict[str, bool]:
        """检查所有数据库健康状态"""
        results = {}
        for db_name in self.database_configs.keys():
            results[db_name] = await self.health_check(db_name)
        return results

    def get_supported_engines(self) -> List[str]:
        """获取支持的数据库引擎列表"""
        return [engine.value for engine in DatabaseEngine]


# 全局动态数据库管理器实例
dynamic_db_manager = DynamicDatabaseManager()
