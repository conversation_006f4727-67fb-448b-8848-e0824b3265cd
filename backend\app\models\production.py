"""
生产计划相关模型
"""

from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, ForeignKey, JSON, Text
from sqlalchemy.orm import relationship
from datetime import datetime

from app.models.base import BaseModel, UserTrackingMixin


class ProductionPlan(BaseModel, UserTrackingMixin):
    """生产计划模型"""
    
    __tablename__ = "production_plan"
    __table_args__ = {'comment': '生产计划表'}
    
    # 计划基本信息
    plan_name = Column(
        String(200),
        nullable=False,
        comment="计划名称"
    )
    
    plan_code = Column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        comment="计划编码"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="计划描述"
    )
    
    # 计划时间
    plan_start_date = Column(
        DateTime(timezone=True),
        nullable=False,
        comment="计划开始时间"
    )
    
    plan_end_date = Column(
        DateTime(timezone=True),
        nullable=False,
        comment="计划结束时间"
    )
    
    # 计划状态
    status = Column(
        String(20),
        default="draft",
        nullable=False,
        comment="状态: draft, optimizing, optimized, executing, completed, cancelled"
    )
    
    # 优化配置
    optimization_config = Column(
        JSON,
        nullable=True,
        comment="优化配置(JSON)"
    )
    
    # 约束条件
    constraints = Column(
        JSON,
        nullable=True,
        comment="约束条件(JSON)"
    )
    
    # 优化结果
    optimization_result = Column(
        JSON,
        nullable=True,
        comment="优化结果(JSON)"
    )
    
    # 执行统计
    total_orders = Column(
        Integer,
        default=0,
        comment="总订单数"
    )
    
    total_quantity = Column(
        Float,
        default=0.0,
        comment="总生产数量"
    )
    
    equipment_count = Column(
        Integer,
        default=0,
        comment="涉及设备数"
    )
    
    # 关联关系
    plan_details = relationship(
        "ProductionPlanDetail",
        back_populates="plan",
        cascade="all, delete-orphan"
    )
    
    executions = relationship(
        "PlanExecution",
        back_populates="plan",
        cascade="all, delete-orphan"
    )


class ProductionPlanDetail(BaseModel, UserTrackingMixin):
    """生产计划明细模型"""
    
    __tablename__ = "production_plan_detail"
    __table_args__ = {'comment': '生产计划明细表'}
    
    plan_id = Column(
        String(50),
        ForeignKey('production_plan.id'),
        nullable=False,
        index=True,
        comment="计划ID"
    )
    
    # 订单信息
    order_id = Column(
        String(50),
        nullable=False,
        comment="订单ID"
    )
    
    order_code = Column(
        String(100),
        nullable=False,
        comment="订单编号"
    )
    
    # 产品信息
    product_id = Column(
        String(50),
        nullable=False,
        comment="产品ID"
    )
    
    product_code = Column(
        String(100),
        nullable=False,
        comment="产品编码"
    )
    
    product_name = Column(
        String(200),
        nullable=False,
        comment="产品名称"
    )
    
    # 数量信息
    planned_quantity = Column(
        Float,
        nullable=False,
        comment="计划数量"
    )
    
    unit = Column(
        String(20),
        nullable=False,
        comment="单位"
    )
    
    # 时间安排
    scheduled_start_time = Column(
        DateTime(timezone=True),
        nullable=False,
        comment="计划开始时间"
    )
    
    scheduled_end_time = Column(
        DateTime(timezone=True),
        nullable=False,
        comment="计划结束时间"
    )
    
    # 设备分配
    equipment_id = Column(
        String(50),
        nullable=False,
        comment="分配设备ID"
    )
    
    equipment_code = Column(
        String(100),
        nullable=False,
        comment="设备编码"
    )
    
    # 优先级
    priority = Column(
        Integer,
        default=5,
        comment="优先级(1-10)"
    )
    
    # 状态
    status = Column(
        String(20),
        default="planned",
        nullable=False,
        comment="状态: planned, executing, completed, cancelled"
    )
    
    # 执行信息
    actual_start_time = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="实际开始时间"
    )
    
    actual_end_time = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="实际结束时间"
    )
    
    actual_quantity = Column(
        Float,
        nullable=True,
        comment="实际完成数量"
    )
    
    # 关联关系
    plan = relationship("ProductionPlan", back_populates="plan_details")


class Equipment(BaseModel, UserTrackingMixin):
    """设备模型"""
    
    __tablename__ = "equipment"
    __table_args__ = {'comment': '设备表'}
    
    # 设备基本信息
    equipment_code = Column(
        String(100),
        unique=True,
        nullable=False,
        index=True,
        comment="设备编码"
    )
    
    equipment_name = Column(
        String(200),
        nullable=False,
        comment="设备名称"
    )
    
    equipment_type = Column(
        String(100),
        nullable=False,
        comment="设备类型"
    )
    
    # 设备规格
    specifications = Column(
        JSON,
        nullable=True,
        comment="设备规格(JSON)"
    )
    
    # 产能信息
    capacity_per_hour = Column(
        Float,
        nullable=True,
        comment="小时产能"
    )
    
    capacity_unit = Column(
        String(20),
        nullable=True,
        comment="产能单位"
    )
    
    # 状态信息
    status = Column(
        String(20),
        default="available",
        nullable=False,
        comment="状态: available, busy, maintenance, offline"
    )
    
    # 位置信息
    location = Column(
        String(200),
        nullable=True,
        comment="设备位置"
    )
    
    workshop = Column(
        String(100),
        nullable=True,
        comment="所属车间"
    )
    
    # 维护信息
    last_maintenance_date = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="上次维护时间"
    )
    
    next_maintenance_date = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="下次维护时间"
    )
    
    # 性能指标
    oee = Column(
        Float,
        nullable=True,
        comment="设备综合效率(OEE)"
    )
    
    utilization_rate = Column(
        Float,
        nullable=True,
        comment="利用率"
    )
    
    # 关联关系
    capabilities = relationship(
        "EquipmentCapability",
        back_populates="equipment",
        cascade="all, delete-orphan"
    )


class EquipmentCapability(BaseModel, UserTrackingMixin):
    """设备能力模型"""
    
    __tablename__ = "equipment_capability"
    __table_args__ = {'comment': '设备能力表'}
    
    equipment_id = Column(
        String(50),
        ForeignKey('equipment.id'),
        nullable=False,
        index=True,
        comment="设备ID"
    )
    
    # 产品能力
    product_type = Column(
        String(100),
        nullable=False,
        comment="产品类型"
    )
    
    process_type = Column(
        String(100),
        nullable=False,
        comment="工艺类型"
    )
    
    # 效率信息
    efficiency = Column(
        Float,
        default=1.0,
        comment="效率系数"
    )
    
    setup_time = Column(
        Float,
        default=0.0,
        comment="换产时间(分钟)"
    )
    
    # 质量信息
    quality_rate = Column(
        Float,
        default=1.0,
        comment="质量合格率"
    )
    
    # 关联关系
    equipment = relationship("Equipment", back_populates="capabilities")


class PlanExecution(BaseModel, UserTrackingMixin):
    """计划执行模型"""
    
    __tablename__ = "plan_execution"
    __table_args__ = {'comment': '计划执行表'}
    
    plan_id = Column(
        String(50),
        ForeignKey('production_plan.id'),
        nullable=False,
        index=True,
        comment="计划ID"
    )
    
    # 执行信息
    execution_date = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        comment="执行日期"
    )
    
    executor_id = Column(
        String(50),
        nullable=False,
        comment="执行人ID"
    )
    
    # 执行结果
    execution_status = Column(
        String(20),
        default="running",
        nullable=False,
        comment="执行状态: running, completed, failed, cancelled"
    )
    
    completion_rate = Column(
        Float,
        default=0.0,
        comment="完成率"
    )
    
    # 统计信息
    completed_orders = Column(
        Integer,
        default=0,
        comment="完成订单数"
    )
    
    completed_quantity = Column(
        Float,
        default=0.0,
        comment="完成数量"
    )
    
    # 执行备注
    notes = Column(
        Text,
        nullable=True,
        comment="执行备注"
    )
    
    # 关联关系
    plan = relationship("ProductionPlan", back_populates="executions")
