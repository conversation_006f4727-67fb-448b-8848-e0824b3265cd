"""
文件处理工具函数
"""

import pandas as pd
import io
import email
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import streamlit as st
from typing import Dict, Any, Optional
import logging

from config.settings import AppConfig

logger = logging.getLogger(__name__)


def validate_file(uploaded_file) -> Dict[str, Any]:
    """验证上传的文件"""
    try:
        # 检查文件大小
        if uploaded_file.size > AppConfig.MAX_UPLOAD_SIZE:
            return {
                "valid": False,
                "message": f"文件大小超过限制 ({AppConfig.MAX_UPLOAD_SIZE // 1024 // 1024}MB)"
            }

        # 检查文件类型
        if uploaded_file.type not in AppConfig.ALLOWED_FILE_TYPES:
            return {
                "valid": False,
                "message": f"不支持的文件类型: {uploaded_file.type}"
            }

        # 检查文件名
        if not uploaded_file.name or len(uploaded_file.name.strip()) == 0:
            return {
                "valid": False,
                "message": "文件名不能为空"
            }

        # 特定文件类型验证
        validation_result = _validate_file_content(uploaded_file)
        if not validation_result["valid"]:
            return validation_result

        return {
            "valid": True,
            "message": "文件验证通过",
            "file_info": {
                "name": uploaded_file.name,
                "size": uploaded_file.size,
                "type": uploaded_file.type
            }
        }

    except Exception as e:
        logger.error(f"文件验证失败: {str(e)}")
        return {
            "valid": False,
            "message": f"文件验证失败: {str(e)}"
        }


def _validate_file_content(uploaded_file) -> Dict[str, Any]:
    """验证文件内容"""
    try:
        file_type = uploaded_file.type

        if file_type in ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        "application/vnd.ms-excel"]:
            return _validate_excel_file(uploaded_file)

        elif file_type == "text/csv":
            return _validate_csv_file(uploaded_file)

        elif file_type in ["message/rfc822", "application/vnd.ms-outlook"]:
            return _validate_email_file(uploaded_file)

        else:
            return {"valid": True, "message": "文件类型验证通过"}

    except Exception as e:
        return {"valid": False, "message": f"文件内容验证失败: {str(e)}"}


def _validate_excel_file(uploaded_file) -> Dict[str, Any]:
    """验证Excel文件"""
    try:
        # 重置文件指针
        uploaded_file.seek(0)

        # 尝试读取Excel文件
        excel_file = pd.ExcelFile(uploaded_file)

        if len(excel_file.sheet_names) == 0:
            return {"valid": False, "message": "Excel文件没有工作表"}

        # 检查是否有数据
        has_data = False
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=1)
            if not df.empty:
                has_data = True
                break

        if not has_data:
            return {"valid": False, "message": "Excel文件没有数据"}

        return {
            "valid": True,
            "message": f"Excel文件验证通过，包含 {len(excel_file.sheet_names)} 个工作表"
        }

    except Exception as e:
        return {"valid": False, "message": f"Excel文件格式错误: {str(e)}"}


def _validate_csv_file(uploaded_file) -> Dict[str, Any]:
    """验证CSV文件"""
    try:
        # 重置文件指针
        uploaded_file.seek(0)

        # 尝试读取CSV文件
        df = pd.read_csv(uploaded_file, nrows=5)

        if df.empty:
            return {"valid": False, "message": "CSV文件没有数据"}

        if len(df.columns) < 2:
            return {"valid": False, "message": "CSV文件列数太少"}

        return {
            "valid": True,
            "message": f"CSV文件验证通过，包含 {len(df.columns)} 列"
        }

    except Exception as e:
        return {"valid": False, "message": f"CSV文件格式错误: {str(e)}"}


def _validate_email_file(uploaded_file) -> Dict[str, Any]:
    """验证邮件文件"""
    try:
        # 重置文件指针
        uploaded_file.seek(0)

        # 读取邮件内容
        email_content = uploaded_file.read()

        if isinstance(email_content, bytes):
            email_content = email_content.decode('utf-8', errors='ignore')

        # 尝试解析邮件
        msg = email.message_from_string(email_content)

        if not msg:
            return {"valid": False, "message": "邮件文件格式错误"}

        # 检查基本字段
        if not msg.get('Subject') and not msg.get('From'):
            return {"valid": False, "message": "邮件缺少基本信息"}

        return {
            "valid": True,
            "message": "邮件文件验证通过"
        }

    except Exception as e:
        return {"valid": False, "message": f"邮件文件格式错误: {str(e)}"}


def preview_file_content(uploaded_file, max_rows: int = 10) -> Optional[pd.DataFrame]:
    """预览文件内容"""
    try:
        file_type = uploaded_file.type

        # 重置文件指针
        uploaded_file.seek(0)

        if file_type in ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        "application/vnd.ms-excel"]:
            return _preview_excel_file(uploaded_file, max_rows)

        elif file_type == "text/csv":
            return _preview_csv_file(uploaded_file, max_rows)

        elif file_type in ["message/rfc822", "application/vnd.ms-outlook"]:
            return _preview_email_file(uploaded_file)

        else:
            return None

    except Exception as e:
        logger.error(f"文件预览失败: {str(e)}")
        st.error(f"文件预览失败: {str(e)}")
        return None


def _preview_excel_file(uploaded_file, max_rows: int) -> Optional[pd.DataFrame]:
    """预览Excel文件"""
    try:
        excel_file = pd.ExcelFile(uploaded_file)

        # 读取第一个有数据的工作表
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=max_rows)
            if not df.empty:
                return df

        return None

    except Exception as e:
        logger.error(f"Excel预览失败: {str(e)}")
        return None


def _preview_csv_file(uploaded_file, max_rows: int) -> Optional[pd.DataFrame]:
    """预览CSV文件"""
    try:
        df = pd.read_csv(uploaded_file, nrows=max_rows)
        return df

    except Exception as e:
        logger.error(f"CSV预览失败: {str(e)}")
        return None


def _preview_email_file(uploaded_file) -> Optional[pd.DataFrame]:
    """预览邮件文件"""
    try:
        # 读取邮件内容
        email_content = uploaded_file.read()

        if isinstance(email_content, bytes):
            email_content = email_content.decode('utf-8', errors='ignore')

        # 解析邮件
        msg = email.message_from_string(email_content)

        # 提取邮件信息
        email_info = {
            "字段": ["发件人", "收件人", "主题", "日期", "内容预览"],
            "值": [
                msg.get('From', ''),
                msg.get('To', ''),
                msg.get('Subject', ''),
                msg.get('Date', ''),
                _get_email_body_preview(msg)
            ]
        }

        return pd.DataFrame(email_info)

    except Exception as e:
        logger.error(f"邮件预览失败: {str(e)}")
        return None


def _get_email_body_preview(msg, max_length: int = 200) -> str:
    """获取邮件正文预览"""
    try:
        body = ""

        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    body = part.get_payload(decode=True)
                    if isinstance(body, bytes):
                        body = body.decode('utf-8', errors='ignore')
                    break
        else:
            body = msg.get_payload(decode=True)
            if isinstance(body, bytes):
                body = body.decode('utf-8', errors='ignore')

        # 截取预览长度
        if len(body) > max_length:
            body = body[:max_length] + "..."

        return body

    except Exception:
        return "无法获取邮件内容"


def extract_tables_from_file(uploaded_file) -> Dict[str, Any]:
    """从文件中提取表格数据"""
    try:
        file_type = uploaded_file.type

        # 重置文件指针
        uploaded_file.seek(0)

        if file_type in ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        "application/vnd.ms-excel"]:
            return _extract_excel_tables(uploaded_file)

        elif file_type == "text/csv":
            return _extract_csv_table(uploaded_file)

        elif file_type in ["message/rfc822", "application/vnd.ms-outlook"]:
            return _extract_email_tables(uploaded_file)

        else:
            return {"success": False, "message": "不支持的文件类型"}

    except Exception as e:
        logger.error(f"表格提取失败: {str(e)}")
        return {"success": False, "message": f"表格提取失败: {str(e)}"}


def _extract_excel_tables(uploaded_file) -> Dict[str, Any]:
    """从Excel文件提取表格"""
    try:
        excel_file = pd.ExcelFile(uploaded_file)
        tables = []

        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(excel_file, sheet_name=sheet_name)

            if not df.empty:
                tables.append({
                    "sheet_name": sheet_name,
                    "table_name": sheet_name,
                    "data": df.to_dict('records'),
                    "columns": df.columns.tolist(),
                    "row_count": len(df),
                    "column_count": len(df.columns)
                })

        return {
            "success": True,
            "tables": tables,
            "total_tables": len(tables)
        }

    except Exception as e:
        return {"success": False, "message": f"Excel表格提取失败: {str(e)}"}


def _extract_csv_table(uploaded_file) -> Dict[str, Any]:
    """从CSV文件提取表格"""
    try:
        df = pd.read_csv(uploaded_file)

        table = {
            "sheet_name": "CSV数据",
            "table_name": "CSV数据",
            "data": df.to_dict('records'),
            "columns": df.columns.tolist(),
            "row_count": len(df),
            "column_count": len(df.columns)
        }

        return {
            "success": True,
            "tables": [table],
            "total_tables": 1
        }

    except Exception as e:
        return {"success": False, "message": f"CSV表格提取失败: {str(e)}"}


def _extract_email_tables(uploaded_file) -> Dict[str, Any]:
    """从邮件最新回复中提取表格数据"""
    try:
        import re
        from io import StringIO

        email_content = uploaded_file.read()

        if isinstance(email_content, bytes):
            email_content = email_content.decode('utf-8', errors='ignore')

        msg = email.message_from_string(email_content)

        # 获取邮件完整正文
        body_text = _get_email_body_full(msg)

        # 提取最新回复内容
        latest_reply = _extract_latest_reply(body_text)

        # 从最新回复中提取表格数据
        tables = []

        # 方法1: 提取HTML表格
        html_tables = _extract_html_tables_from_text(latest_reply)
        tables.extend(html_tables)

        # 方法2: 提取文本表格（制表符分隔、空格分隔等）
        text_tables = _extract_text_tables_from_text(latest_reply)
        tables.extend(text_tables)

        # 方法3: 提取CSV格式的表格
        csv_tables = _extract_csv_tables_from_text(latest_reply)
        tables.extend(csv_tables)

        # 如果在最新回复中没有找到表格，尝试从整个邮件正文中提取
        if not tables:
            html_tables = _extract_html_tables_from_text(body_text)
            tables.extend(html_tables)

            text_tables = _extract_text_tables_from_text(body_text)
            tables.extend(text_tables)

            csv_tables = _extract_csv_tables_from_text(body_text)
            tables.extend(csv_tables)

        # 如果仍然没有找到表格，返回邮件基本信息
        if not tables:
            email_data = {
                "发件人": msg.get('From', ''),
                "收件人": msg.get('To', ''),
                "主题": msg.get('Subject', ''),
                "日期": msg.get('Date', ''),
                "最新回复内容": latest_reply[:500] + "..." if len(latest_reply) > 500 else latest_reply,
                "完整内容": body_text[:500] + "..." if len(body_text) > 500 else body_text
            }

            tables.append({
                "sheet_name": "邮件信息",
                "table_name": "邮件信息",
                "data": [email_data],
                "columns": list(email_data.keys()),
                "row_count": 1,
                "column_count": len(email_data)
            })

        return {
            "success": True,
            "tables": tables,
            "total_tables": len(tables),
            "latest_reply_extracted": len(latest_reply) > 0
        }

    except Exception as e:
        return {"success": False, "message": f"邮件表格提取失败: {str(e)}"}


def _get_email_body_full(msg) -> str:
    """获取邮件完整正文"""
    try:
        body = ""

        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                if content_type == "text/plain":
                    body = part.get_payload(decode=True)
                    if isinstance(body, bytes):
                        body = body.decode('utf-8', errors='ignore')
                    break
                elif content_type == "text/html" and not body:
                    # 如果没有纯文本，使用HTML
                    html_body = part.get_payload(decode=True)
                    if isinstance(html_body, bytes):
                        html_body = html_body.decode('utf-8', errors='ignore')
                    # 简单的HTML标签清理
                    import re
                    body = re.sub(r'<[^>]+>', '', html_body)
        else:
            body = msg.get_payload(decode=True)
            if isinstance(body, bytes):
                body = body.decode('utf-8', errors='ignore')

        return body

    except Exception:
        return ""


def _extract_latest_reply(email_body: str) -> str:
    """从邮件正文中提取最新回复内容"""
    try:
        import re

        # 常见的邮件回复分隔符模式
        reply_separators = [
            r'-----\s*Original Message\s*-----',  # Outlook
            r'-----\s*原始邮件\s*-----',  # Outlook中文
            r'From:.*?Sent:.*?To:.*?Subject:',  # 标准回复格式
            r'发件人:.*?发送时间:.*?收件人:.*?主题:',  # 中文回复格式
            r'On.*?wrote:',  # Gmail格式
            r'在.*?写道:',  # Gmail中文格式
            r'>\s*On.*?at.*?wrote:',  # 引用回复
            r'>\s*在.*?于.*?写道:',  # 中文引用回复
            r'________________________________',  # 分隔线
            r'=+',  # 等号分隔线
            r'-{10,}',  # 长横线分隔
            r'^\s*>.*$',  # 引用行（多行处理）
        ]

        # 首先尝试按分隔符分割
        latest_content = email_body

        for pattern in reply_separators:
            match = re.search(pattern, email_body, re.MULTILINE | re.IGNORECASE)
            if match:
                # 取分隔符之前的内容作为最新回复
                latest_content = email_body[:match.start()].strip()
                break

        # 如果没有找到分隔符，尝试其他方法
        if latest_content == email_body:
            # 方法2: 按引用行分割（以>开头的行）
            lines = email_body.split('\n')
            latest_lines = []

            for line in lines:
                # 如果遇到引用行，停止
                if re.match(r'^\s*>', line.strip()):
                    break
                latest_lines.append(line)

            if latest_lines:
                latest_content = '\n'.join(latest_lines).strip()

        # 如果仍然没有分离出来，尝试按时间戳分割
        if latest_content == email_body:
            # 查找时间戳模式
            timestamp_patterns = [
                r'\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{2}',  # 2024-01-01 12:00
                r'\d{1,2}[-/]\d{1,2}[-/]\d{4}\s+\d{1,2}:\d{2}',  # 01-01-2024 12:00
                r'(Mon|Tue|Wed|Thu|Fri|Sat|Sun).*?\d{1,2}:\d{2}',  # Mon Jan 01 12:00
                r'(周一|周二|周三|周四|周五|周六|周日).*?\d{1,2}:\d{2}',  # 中文星期
            ]

            for pattern in timestamp_patterns:
                matches = list(re.finditer(pattern, email_body, re.IGNORECASE))
                if len(matches) > 1:
                    # 如果有多个时间戳，取第一个之前的内容
                    latest_content = email_body[:matches[1].start()].strip()
                    break

        # 清理内容
        latest_content = _clean_email_content(latest_content)

        return latest_content

    except Exception:
        return email_body


def _clean_email_content(content: str) -> str:
    """清理邮件内容"""
    try:
        import re

        # 移除多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

        # 移除邮件头部信息（如果存在）
        header_patterns = [
            r'^(From|To|Cc|Bcc|Subject|Date|Sent):.*?\n',
            r'^(发件人|收件人|抄送|密送|主题|日期|发送时间):.*?\n',
        ]

        for pattern in header_patterns:
            content = re.sub(pattern, '', content, flags=re.MULTILINE | re.IGNORECASE)

        # 移除邮件签名（常见模式）
        signature_patterns = [
            r'\n--\s*\n.*$',  # -- 签名分隔符
            r'\n\s*Best regards.*$',  # 英文签名
            r'\n\s*此致.*$',  # 中文签名
            r'\n\s*谢谢.*$',  # 中文感谢
            r'\n\s*Thanks.*$',  # 英文感谢
        ]

        for pattern in signature_patterns:
            content = re.sub(pattern, '', content, flags=re.MULTILINE | re.IGNORECASE | re.DOTALL)

        return content.strip()

    except Exception:
        return content


def _extract_html_tables_from_text(text: str) -> List[Dict[str, Any]]:
    """从文本中提取HTML表格"""
    try:
        import re

        tables = []

        # 简单的HTML表格提取（不依赖BeautifulSoup）
        table_pattern = r'<table[^>]*>(.*?)</table>'
        html_tables = re.findall(table_pattern, text, re.DOTALL | re.IGNORECASE)

        for i, table_content in enumerate(html_tables):
            try:
                # 提取行
                row_pattern = r'<tr[^>]*>(.*?)</tr>'
                rows = re.findall(row_pattern, table_content, re.DOTALL | re.IGNORECASE)

                if rows:
                    # 提取第一行作为表头
                    cell_pattern = r'<t[hd][^>]*>(.*?)</t[hd]>'
                    headers = []
                    first_row_cells = re.findall(cell_pattern, rows[0], re.DOTALL | re.IGNORECASE)
                    for cell in first_row_cells:
                        # 清理HTML标签
                        clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                        headers.append(clean_cell)

                    # 提取数据行
                    data_rows = []
                    for row in rows[1:] if len(rows) > 1 else rows:
                        cells = re.findall(cell_pattern, row, re.DOTALL | re.IGNORECASE)
                        row_data = {}
                        for j, cell in enumerate(cells):
                            clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                            header = headers[j] if j < len(headers) else f"列{j+1}"
                            row_data[header] = clean_cell
                        if any(row_data.values()):
                            data_rows.append(row_data)

                    if data_rows:
                        tables.append({
                            "sheet_name": f"HTML表格{i+1}",
                            "table_name": f"HTML表格{i+1}",
                            "data": data_rows,
                            "columns": headers,
                            "row_count": len(data_rows),
                            "column_count": len(headers)
                        })
            except Exception:
                continue

        return tables

    except Exception:
        return []


def _extract_text_tables_from_text(text: str) -> List[Dict[str, Any]]:
    """从文本中提取制表符或空格分隔的表格"""
    try:
        import re

        tables = []
        lines = text.split('\n')

        # 查找可能的表格模式
        table_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                if table_lines and len(table_lines) >= 2:
                    table = _parse_text_table(table_lines)
                    if table:
                        tables.append(table)
                table_lines = []
                continue

            # 检查是否是表格行
            if '\t' in line or re.search(r'\s{3,}', line):
                table_lines.append(line)

        # 处理最后一个表格
        if table_lines and len(table_lines) >= 2:
            table = _parse_text_table(table_lines)
            if table:
                tables.append(table)

        return tables

    except Exception:
        return []


def _extract_csv_tables_from_text(text: str) -> List[Dict[str, Any]]:
    """从文本中提取CSV格式的表格"""
    try:
        tables = []
        lines = text.split('\n')

        csv_lines = []
        for line in lines:
            line = line.strip()
            if ',' in line and len(line.split(',')) > 1:
                csv_lines.append(line)
            elif csv_lines and len(csv_lines) >= 2:
                table = _parse_csv_table(csv_lines)
                if table:
                    tables.append(table)
                csv_lines = []

        if csv_lines and len(csv_lines) >= 2:
            table = _parse_csv_table(csv_lines)
            if table:
                tables.append(table)

        return tables

    except Exception:
        return []


def _parse_text_table(lines: List[str]) -> Optional[Dict[str, Any]]:
    """解析文本表格"""
    try:
        if len(lines) < 2:
            return None

        # 尝试制表符分隔
        if '\t' in lines[0]:
            headers = [h.strip() for h in lines[0].split('\t') if h.strip()]
            data_rows = []

            for line in lines[1:]:
                if '\t' in line:
                    cells = [c.strip() for c in line.split('\t')]
                    if len(cells) >= len(headers):
                        row_data = {}
                        for i, header in enumerate(headers):
                            row_data[header] = cells[i] if i < len(cells) else ""
                        data_rows.append(row_data)

            if data_rows:
                return {
                    "sheet_name": "文本表格",
                    "table_name": "文本表格",
                    "data": data_rows,
                    "columns": headers,
                    "row_count": len(data_rows),
                    "column_count": len(headers)
                }

        return None

    except Exception:
        return None


def _parse_csv_table(lines: List[str]) -> Optional[Dict[str, Any]]:
    """解析CSV表格"""
    try:
        if len(lines) < 2:
            return None

        # 简单的CSV解析
        headers = [h.strip().strip('"') for h in lines[0].split(',')]
        data_rows = []

        for line in lines[1:]:
            cells = [c.strip().strip('"') for c in line.split(',')]
            if len(cells) >= len(headers):
                row_data = {}
                for i, header in enumerate(headers):
                    row_data[header] = cells[i] if i < len(cells) else ""
                data_rows.append(row_data)

        if data_rows:
            return {
                "sheet_name": "CSV表格",
                "table_name": "CSV表格",
                "data": data_rows,
                "columns": headers,
                "row_count": len(data_rows),
                "column_count": len(headers)
            }

        return None

    except Exception:
        return None


def get_file_type_display_name(mime_type: str) -> str:
    """获取文件类型显示名称"""
    return AppConfig.get_file_type_display_name(mime_type)


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / 1024 / 1024:.1f} MB"
    else:
        return f"{size_bytes / 1024 / 1024 / 1024:.1f} GB"
