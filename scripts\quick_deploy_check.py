#!/usr/bin/env python3
"""
Smart Planning 快速部署检查脚本
检查系统是否准备好部署
"""

import os
import sys
from pathlib import Path

def check_file(file_path, description):
    """检查文件是否存在"""
    if Path(file_path).exists():
        print(f"✅ {description}")
        return True
    else:
        print(f"❌ {description} - 文件不存在: {file_path}")
        return False

def main():
    """主检查函数"""
    print("🚀 Smart Planning 快速部署检查")
    print("=" * 50)
    
    checks = []
    
    # 核心文件检查
    print("\n📁 核心文件检查:")
    checks.append(check_file("README.md", "项目说明文档"))
    checks.append(check_file(".env.example", "环境配置示例"))
    checks.append(check_file("docker-compose.yml", "Docker编排配置"))
    checks.append(check_file("scripts/mysql/init.sql", "数据库初始化脚本"))
    
    # 后端文件检查
    print("\n🔧 后端文件检查:")
    checks.append(check_file("backend/Dockerfile", "后端Docker文件"))
    checks.append(check_file("backend/requirements.txt", "后端依赖文件"))
    checks.append(check_file("backend/app/main.py", "后端主应用"))
    checks.append(check_file("backend/scripts/start.sh", "后端启动脚本"))
    
    # 前端文件检查
    print("\n🎨 前端文件检查:")
    checks.append(check_file("frontend/Dockerfile", "前端Docker文件"))
    checks.append(check_file("frontend/requirements.txt", "前端依赖文件"))
    checks.append(check_file("frontend/main.py", "前端主应用"))
    
    # 检查前端页面
    pages_dir = Path("frontend/pages")
    if pages_dir.exists():
        page_count = len(list(pages_dir.glob("*.py")))
        if page_count >= 15:
            print(f"✅ 前端页面文件 ({page_count} 个页面)")
            checks.append(True)
        else:
            print(f"⚠️  前端页面文件不完整 (只有 {page_count} 个页面)")
            checks.append(False)
    else:
        print("❌ 前端页面目录不存在")
        checks.append(False)
    
    # 部署脚本检查
    print("\n🚀 部署脚本检查:")
    checks.append(check_file("deploy.sh", "Linux部署脚本"))
    checks.append(check_file("deploy.bat", "Windows部署脚本"))
    
    # 统计结果
    success_count = sum(checks)
    total_count = len(checks)
    success_rate = (success_count / total_count) * 100
    
    print("\n" + "=" * 50)
    print("📊 检查结果:")
    print(f"✅ 成功: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("\n🎉 系统检查通过！可以开始部署。")
        print("\n🚀 推荐部署命令:")
        print("   docker-compose up -d")
        print("\n📖 详细部署说明请查看 README.md")
        return True
    else:
        print("\n⚠️  系统检查发现问题，请修复后再部署。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
