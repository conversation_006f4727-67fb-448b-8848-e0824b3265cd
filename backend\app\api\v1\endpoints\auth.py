"""
认证相关API端点
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import password_manager, token_manager
from app.core.config import settings
from app.core.logging_config import business_logger
from app.schemas.auth import Token, UserLogin, UserRegister
from app.schemas.user import UserResponse
from app.services.auth_service import AuthService
from app.services.user_service import UserService

router = APIRouter()


@router.post("/login", response_model=Token, summary="用户登录")
async def login(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    用户登录
    
    - **username**: 用户名或邮箱
    - **password**: 密码
    """
    try:
        # 获取客户端IP
        client_ip = request.headers.get("x-forwarded-for", "").split(",")[0].strip()
        if not client_ip:
            client_ip = request.client.host if request.client else "unknown"
        
        # 验证用户
        auth_service = AuthService(db)
        user = await auth_service.authenticate_user(
            username=form_data.username,
            password=form_data.password,
            ip_address=client_ip
        )
        
        if not user:
            business_logger.log_user_action(
                user_id="unknown",
                action="login_failed",
                details={"username": form_data.username, "ip": client_ip}
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账户已被禁用"
            )
        
        if user.is_locked():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户账户已被锁定，请稍后再试"
            )
        
        # 生成令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = token_manager.create_access_token(
            data={"sub": user.id, "username": user.username},
            expires_delta=access_token_expires
        )
        
        refresh_token = token_manager.create_refresh_token(
            data={"sub": user.id, "username": user.username}
        )
        
        # 创建会话
        await auth_service.create_session(
            user_id=user.id,
            access_token=access_token,
            refresh_token=refresh_token,
            ip_address=client_ip,
            user_agent=request.headers.get("user-agent", "")
        )
        
        # 更新登录信息
        user_service = UserService(db)
        await user_service.update_login_info(user.id, client_ip)
        
        # 记录登录日志
        business_logger.log_user_action(
            user_id=user.id,
            action="login_success",
            details={"ip": client_ip}
        )
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user_info": {
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email,
                "is_superuser": user.is_superuser,
                "permissions": list(user.get_permissions())
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="login_error",
            error_message=str(e),
            details={"username": form_data.username}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录过程中发生错误"
        )


@router.post("/register", response_model=UserResponse, summary="用户注册")
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    用户注册
    
    - **username**: 用户名
    - **email**: 邮箱
    - **full_name**: 姓名
    - **password**: 密码
    """
    try:
        user_service = UserService(db)
        
        # 检查用户名是否已存在
        existing_user = await user_service.get_by_username(user_data.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 检查邮箱是否已存在
        existing_email = await user_service.get_by_email(user_data.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
        
        # 创建用户
        user = await user_service.create_user(user_data)
        
        # 记录注册日志
        business_logger.log_user_action(
            user_id=user.id,
            action="user_registered",
            details={"username": user.username, "email": user.email}
        )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="register_error",
            error_message=str(e),
            details={"username": user_data.username}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册过程中发生错误"
        )


@router.post("/refresh", response_model=Token, summary="刷新令牌")
async def refresh_token(
    refresh_token: str,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    刷新访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    try:
        # 验证刷新令牌
        payload = token_manager.verify_token(refresh_token, "refresh")
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的令牌载荷"
            )
        
        # 验证会话
        auth_service = AuthService(db)
        session = await auth_service.get_session_by_refresh_token(refresh_token)
        if not session or not session.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="会话已失效"
            )
        
        # 生成新的访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        new_access_token = token_manager.create_access_token(
            data={"sub": user_id, "username": payload.get("username")},
            expires_delta=access_token_expires
        )
        
        # 更新会话
        await auth_service.update_session_token(session.id, new_access_token)
        
        return {
            "access_token": new_access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        }
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="refresh_token_error",
            error_message=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新令牌过程中发生错误"
        )


@router.post("/logout", summary="用户登出")
async def logout(
    request: Request,
    current_user: dict = Depends(),  # 这里需要实现依赖注入
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    用户登出
    """
    try:
        # 获取当前会话令牌
        authorization = request.headers.get("Authorization")
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="缺少认证令牌"
            )
        
        token = authorization.split(" ")[1]
        
        # 注销会话
        auth_service = AuthService(db)
        await auth_service.logout_session(token)
        
        # 记录登出日志
        business_logger.log_user_action(
            user_id=current_user.get("id"),
            action="logout",
            details={}
        )
        
        return {"message": "登出成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        business_logger.log_error(
            error_type="logout_error",
            error_message=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出过程中发生错误"
        )
