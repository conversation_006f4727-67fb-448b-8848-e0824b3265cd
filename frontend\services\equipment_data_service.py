"""
设备数据服务
为LLM和算法提供设备信息的专用服务
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any


class EquipmentDataService:
    """设备数据服务类"""
    
    def __init__(self):
        self.equipment_data = self._load_equipment_data()
    
    def _load_equipment_data(self) -> Dict[str, Any]:
        """加载设备数据"""
        # 这里应该从数据库加载，现在使用模拟数据
        return {
            "equipment": [
                # L系列生产线设备
                {
                    "id": "1",
                    "equipment_code": "L01",
                    "equipment_name": "生产线01",
                    "equipment_type": "生产线",
                    "status": "running",
                    "workshop": "生产线区域",
                    "location": "生产区-L01",
                    "utilization_rate": 85.2,
                    "oee": 82.1,
                    "capacity_per_hour": 50.0,
                    "capacity_unit": "件",
                    "current_order": "订单A001",
                    "estimated_completion": "2024-01-16 18:00",
                    "shift_schedule": ["08:00-16:00", "16:00-00:00", "00:00-08:00"],
                    "maintenance_schedule": "每周日 02:00-06:00"
                },
                {
                    "id": "2",
                    "equipment_code": "L02",
                    "equipment_name": "生产线02",
                    "equipment_type": "生产线",
                    "status": "stopped",
                    "workshop": "生产线区域",
                    "location": "生产区-L02",
                    "utilization_rate": 0.0,
                    "oee": 0.0,
                    "capacity_per_hour": 45.0,
                    "capacity_unit": "件",
                    "stop_reason": "原料短缺",
                    "stop_time": "2024-01-16 14:30",
                    "expected_resume": "2024-01-17 08:00"
                },
                {
                    "id": "3",
                    "equipment_code": "L03",
                    "equipment_name": "生产线03",
                    "equipment_type": "生产线",
                    "status": "maintenance",
                    "workshop": "生产线区域",
                    "location": "生产区-L03",
                    "utilization_rate": 0.0,
                    "oee": 0.0,
                    "capacity_per_hour": 48.0,
                    "capacity_unit": "件",
                    "maintenance_type": "预防性维护",
                    "maintenance_start": "2024-01-16 08:00",
                    "maintenance_end": "2024-01-16 20:00"
                },
                {
                    "id": "4",
                    "equipment_code": "L04",
                    "equipment_name": "生产线04",
                    "equipment_type": "生产线",
                    "status": "busy",
                    "workshop": "生产线区域",
                    "location": "生产区-L04",
                    "utilization_rate": 76.3,
                    "oee": 74.8,
                    "capacity_per_hour": 52.0,
                    "capacity_unit": "件",
                    "current_order": "订单B002",
                    "estimated_completion": "2024-01-16 20:00"
                },
                # Tank系列储罐设备
                {
                    "id": "5",
                    "equipment_code": "Tank01",
                    "equipment_name": "储罐01",
                    "equipment_type": "储罐设备",
                    "status": "running",
                    "workshop": "储罐区域",
                    "location": "储罐区-T01",
                    "utilization_rate": 68.5,
                    "oee": 95.2,
                    "capacity_per_hour": 1000.0,
                    "capacity_unit": "L",
                    "current_level": 750.0,
                    "max_capacity": 1000.0,
                    "material": "原料A",
                    "temperature": 25.5,
                    "pressure": 0.3
                },
                {
                    "id": "6",
                    "equipment_code": "Tank02",
                    "equipment_name": "储罐02",
                    "equipment_type": "储罐设备",
                    "status": "busy",
                    "workshop": "储罐区域",
                    "location": "储罐区-T02",
                    "utilization_rate": 89.2,
                    "oee": 92.8,
                    "capacity_per_hour": 1200.0,
                    "capacity_unit": "L",
                    "current_level": 1100.0,
                    "max_capacity": 1200.0,
                    "material": "原料B",
                    "temperature": 22.8,
                    "pressure": 0.4
                },
                {
                    "id": "7",
                    "equipment_code": "Tank03",
                    "equipment_name": "储罐03",
                    "equipment_type": "储罐设备",
                    "status": "standby",
                    "workshop": "储罐区域",
                    "location": "储罐区-T03",
                    "utilization_rate": 0.0,
                    "oee": 0.0,
                    "capacity_per_hour": 800.0,
                    "capacity_unit": "L",
                    "current_level": 0.0,
                    "max_capacity": 800.0,
                    "material": "空置"
                },
                {
                    "id": "8",
                    "equipment_code": "Tank04",
                    "equipment_name": "储罐04",
                    "equipment_type": "储罐设备",
                    "status": "maintenance",
                    "workshop": "储罐区域",
                    "location": "储罐区-T04",
                    "utilization_rate": 0.0,
                    "oee": 0.0,
                    "capacity_per_hour": 1500.0,
                    "capacity_unit": "L",
                    "current_level": 0.0,
                    "max_capacity": 1500.0,
                    "material": "维护中",
                    "maintenance_type": "清洗维护"
                },
                {
                    "id": "9",
                    "equipment_code": "Tank05",
                    "equipment_name": "储罐05",
                    "equipment_type": "储罐设备",
                    "status": "available",
                    "workshop": "储罐区域",
                    "location": "储罐区-T05",
                    "utilization_rate": 65.8,
                    "oee": 91.3,
                    "capacity_per_hour": 900.0,
                    "capacity_unit": "L",
                    "current_level": 450.0,
                    "max_capacity": 900.0,
                    "material": "原料C",
                    "temperature": 24.2,
                    "pressure": 0.35
                }
            ]
        }
    
    def get_equipment_for_planning(self) -> Dict[str, Any]:
        """获取用于生产计划的设备信息"""
        equipment_list = self.equipment_data.get("equipment", [])
        
        planning_data = {
            "available_equipment": [],
            "unavailable_equipment": [],
            "capacity_summary": {
                "production_lines": {
                    "total_capacity": 0,
                    "available_capacity": 0,
                    "utilization_rate": 0
                },
                "tanks": {
                    "total_capacity": 0,
                    "available_capacity": 0,
                    "current_usage": 0
                }
            },
            "constraints": [],
            "recommendations": []
        }
        
        for eq in equipment_list:
            equipment_info = {
                "code": eq.get("equipment_code"),
                "name": eq.get("equipment_name"),
                "type": eq.get("equipment_type"),
                "status": eq.get("status"),
                "capacity_per_hour": eq.get("capacity_per_hour"),
                "capacity_unit": eq.get("capacity_unit"),
                "utilization_rate": eq.get("utilization_rate"),
                "oee": eq.get("oee")
            }
            
            # 判断设备是否可用于计划
            if eq.get("status") in ["running", "available", "standby"]:
                planning_data["available_equipment"].append(equipment_info)
                
                # 计算产能
                if eq.get("equipment_type") == "生产线":
                    planning_data["capacity_summary"]["production_lines"]["total_capacity"] += eq.get("capacity_per_hour", 0)
                    planning_data["capacity_summary"]["production_lines"]["available_capacity"] += eq.get("capacity_per_hour", 0) * (eq.get("utilization_rate", 0) / 100)
                
                elif eq.get("equipment_type") == "储罐设备":
                    planning_data["capacity_summary"]["tanks"]["total_capacity"] += eq.get("max_capacity", 0)
                    planning_data["capacity_summary"]["tanks"]["current_usage"] += eq.get("current_level", 0)
            
            else:
                planning_data["unavailable_equipment"].append({
                    **equipment_info,
                    "unavailable_reason": self._get_unavailable_reason(eq)
                })
                
                # 添加约束条件
                planning_data["constraints"].append({
                    "equipment": eq.get("equipment_code"),
                    "type": "unavailable",
                    "reason": self._get_unavailable_reason(eq),
                    "expected_available": self._get_expected_available_time(eq)
                })
        
        # 计算利用率
        total_production_capacity = planning_data["capacity_summary"]["production_lines"]["total_capacity"]
        available_production_capacity = planning_data["capacity_summary"]["production_lines"]["available_capacity"]
        
        if total_production_capacity > 0:
            planning_data["capacity_summary"]["production_lines"]["utilization_rate"] = (available_production_capacity / total_production_capacity) * 100
        
        # 计算储罐利用率
        total_tank_capacity = planning_data["capacity_summary"]["tanks"]["total_capacity"]
        current_tank_usage = planning_data["capacity_summary"]["tanks"]["current_usage"]
        
        if total_tank_capacity > 0:
            planning_data["capacity_summary"]["tanks"]["available_capacity"] = total_tank_capacity - current_tank_usage
        
        # 生成建议
        planning_data["recommendations"] = self._generate_planning_recommendations(planning_data)
        
        return planning_data
    
    def _get_unavailable_reason(self, equipment: Dict[str, Any]) -> str:
        """获取设备不可用原因"""
        status = equipment.get("status")
        if status == "stopped":
            return equipment.get("stop_reason", "停线")
        elif status == "maintenance":
            return f"维护中 - {equipment.get('maintenance_type', '维护')}"
        elif status == "fault":
            return "设备故障"
        elif status == "offline":
            return "设备离线"
        else:
            return "不可用"
    
    def _get_expected_available_time(self, equipment: Dict[str, Any]) -> Optional[str]:
        """获取设备预期可用时间"""
        status = equipment.get("status")
        if status == "stopped":
            return equipment.get("expected_resume")
        elif status == "maintenance":
            return equipment.get("maintenance_end")
        else:
            return None
    
    def _generate_planning_recommendations(self, planning_data: Dict[str, Any]) -> List[str]:
        """生成计划建议"""
        recommendations = []
        
        # 检查生产线利用率
        production_utilization = planning_data["capacity_summary"]["production_lines"]["utilization_rate"]
        if production_utilization < 70:
            recommendations.append("生产线利用率较低，建议优化生产计划以提高效率")
        elif production_utilization > 90:
            recommendations.append("生产线利用率很高，注意避免过载，考虑增加维护时间")
        
        # 检查不可用设备
        unavailable_count = len(planning_data["unavailable_equipment"])
        if unavailable_count > 0:
            recommendations.append(f"当前有{unavailable_count}台设备不可用，建议调整生产计划")
        
        # 检查储罐容量
        tank_capacity = planning_data["capacity_summary"]["tanks"]["total_capacity"]
        tank_usage = planning_data["capacity_summary"]["tanks"]["current_usage"]
        if tank_capacity > 0:
            tank_utilization = (tank_usage / tank_capacity) * 100
            if tank_utilization > 80:
                recommendations.append("储罐容量使用率较高，建议及时安排原料消耗")
            elif tank_utilization < 20:
                recommendations.append("储罐容量使用率较低，可以考虑增加原料储备")
        
        return recommendations
    
    def get_llm_context(self) -> str:
        """获取LLM上下文信息"""
        planning_data = self.get_equipment_for_planning()
        
        context = f"""
当前设备状态概览：

生产线设备：
- 总产能：{planning_data['capacity_summary']['production_lines']['total_capacity']} 件/小时
- 可用产能：{planning_data['capacity_summary']['production_lines']['available_capacity']:.1f} 件/小时
- 利用率：{planning_data['capacity_summary']['production_lines']['utilization_rate']:.1f}%

储罐设备：
- 总容量：{planning_data['capacity_summary']['tanks']['total_capacity']} L
- 已使用：{planning_data['capacity_summary']['tanks']['current_usage']} L
- 可用容量：{planning_data['capacity_summary']['tanks']['available_capacity']} L

可用设备：{len(planning_data['available_equipment'])} 台
不可用设备：{len(planning_data['unavailable_equipment'])} 台

约束条件：
"""
        
        for constraint in planning_data['constraints']:
            context += f"- {constraint['equipment']}: {constraint['reason']}\n"
        
        context += "\n建议：\n"
        for recommendation in planning_data['recommendations']:
            context += f"- {recommendation}\n"
        
        return context
    
    def get_equipment_status_summary(self) -> Dict[str, Any]:
        """获取设备状态汇总"""
        equipment_list = self.equipment_data.get("equipment", [])
        
        status_summary = {
            "total": len(equipment_list),
            "by_status": {},
            "by_type": {},
            "critical_alerts": []
        }
        
        for eq in equipment_list:
            status = eq.get("status")
            eq_type = eq.get("equipment_type")
            
            # 按状态统计
            if status not in status_summary["by_status"]:
                status_summary["by_status"][status] = 0
            status_summary["by_status"][status] += 1
            
            # 按类型统计
            if eq_type not in status_summary["by_type"]:
                status_summary["by_type"][eq_type] = 0
            status_summary["by_type"][eq_type] += 1
            
            # 检查关键警报
            if status == "stopped":
                status_summary["critical_alerts"].append({
                    "equipment": eq.get("equipment_code"),
                    "alert": "设备停线",
                    "reason": eq.get("stop_reason", "未知原因")
                })
            elif status == "fault":
                status_summary["critical_alerts"].append({
                    "equipment": eq.get("equipment_code"),
                    "alert": "设备故障",
                    "reason": "需要紧急维修"
                })
        
        return status_summary
