"""
用户相关的Pydantic模式
"""

from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, EmailStr, validator
import re


class UserBase(BaseModel):
    """用户基础模式"""
    username: str
    email: EmailStr
    full_name: str
    phone: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None
    is_active: bool = True


class UserCreate(UserBase):
    """用户创建模式"""
    password: str
    role_ids: Optional[List[str]] = []
    
    @validator('password')
    def validate_password(cls, v):
        if not v or len(v) < 8:
            raise ValueError('密码长度不能少于8位')
        
        if not re.search(r'[a-zA-Z]', v) or not re.search(r'\d', v):
            raise ValueError('密码必须包含字母和数字')
        
        return v


class UserUpdate(BaseModel):
    """用户更新模式"""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None
    is_active: Optional[bool] = None
    role_ids: Optional[List[str]] = None
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('请输入有效的手机号码')
        return v


class UserResponse(BaseModel):
    """用户响应模式"""
    id: str
    username: str
    email: str
    full_name: str
    phone: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: bool
    is_superuser: bool
    last_login_at: Optional[datetime] = None
    login_count: str
    created_at: datetime
    updated_at: datetime
    roles: List['RoleResponse'] = []
    
    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """用户列表响应模式"""
    users: List[UserResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class RoleBase(BaseModel):
    """角色基础模式"""
    code: str
    name: str
    description: Optional[str] = None
    is_active: bool = True


class RoleCreate(RoleBase):
    """角色创建模式"""
    permission_ids: Optional[List[str]] = []


class RoleUpdate(BaseModel):
    """角色更新模式"""
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    permission_ids: Optional[List[str]] = None


class RoleResponse(BaseModel):
    """角色响应模式"""
    id: str
    code: str
    name: str
    description: Optional[str] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime
    permissions: List['PermissionResponse'] = []
    
    class Config:
        from_attributes = True


class PermissionBase(BaseModel):
    """权限基础模式"""
    code: str
    name: str
    description: Optional[str] = None
    module: str
    is_active: bool = True


class PermissionCreate(PermissionBase):
    """权限创建模式"""
    pass


class PermissionUpdate(BaseModel):
    """权限更新模式"""
    name: Optional[str] = None
    description: Optional[str] = None
    module: Optional[str] = None
    is_active: Optional[bool] = None


class PermissionResponse(BaseModel):
    """权限响应模式"""
    id: str
    code: str
    name: str
    description: Optional[str] = None
    module: str
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserProfileUpdate(BaseModel):
    """用户资料更新模式"""
    full_name: Optional[str] = None
    phone: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None
    avatar_url: Optional[str] = None
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('请输入有效的手机号码')
        return v


class UserStatsResponse(BaseModel):
    """用户统计响应模式"""
    total_users: int
    active_users: int
    inactive_users: int
    new_users_today: int
    new_users_this_week: int
    new_users_this_month: int
    login_stats: dict


# 解决前向引用问题
UserResponse.model_rebuild()
RoleResponse.model_rebuild()
