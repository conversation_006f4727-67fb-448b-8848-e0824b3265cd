"""
文件服务
"""

import os
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, update
from sqlalchemy.orm import selectinload
import logging
from datetime import datetime

from app.models.file_upload import (
    UploadedFile, ExtractedData, DataImportLog, FileProcessingQueue
)
from app.core.config import settings

logger = logging.getLogger(__name__)


class FileService:
    """文件服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_file_record(
        self,
        original_filename: str,
        stored_filename: str,
        file_path: str,
        file_size: int,
        file_type: str,
        file_extension: str,
        description: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> UploadedFile:
        """创建文件记录"""
        try:
            uploaded_file = UploadedFile(
                original_filename=original_filename,
                stored_filename=stored_filename,
                file_path=file_path,
                file_size=file_size,
                file_type=file_type,
                file_extension=file_extension,
                status="uploaded",
                metadata={
                    "description": description,
                    "upload_timestamp": datetime.utcnow().isoformat()
                },
                created_by=user_id,
                updated_by=user_id
            )
            
            self.db.add(uploaded_file)
            await self.db.commit()
            await self.db.refresh(uploaded_file)
            
            return uploaded_file
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建文件记录失败: {str(e)}")
            raise
    
    async def get_file_by_id(self, file_id: str) -> Optional[UploadedFile]:
        """根据ID获取文件"""
        try:
            stmt = select(UploadedFile).where(
                UploadedFile.id == file_id,
                UploadedFile.is_deleted == False
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取文件失败: {str(e)}")
            return None
    
    async def get_upload_history(
        self,
        user_id: Optional[str] = None,
        page: int = 1,
        page_size: int = 20,
        file_type: Optional[str] = None,
        status: Optional[str] = None
    ) -> tuple[List[UploadedFile], int]:
        """获取上传历史"""
        try:
            # 构建查询
            stmt = select(UploadedFile).where(UploadedFile.is_deleted == False)
            
            # 用户过滤
            if user_id:
                stmt = stmt.where(UploadedFile.created_by == user_id)
            
            # 文件类型过滤
            if file_type:
                stmt = stmt.where(UploadedFile.file_type == file_type)
            
            # 状态过滤
            if status:
                stmt = stmt.where(UploadedFile.status == status)
            
            # 获取总数
            count_stmt = select(func.count(UploadedFile.id)).where(
                UploadedFile.is_deleted == False
            )
            if user_id:
                count_stmt = count_stmt.where(UploadedFile.created_by == user_id)
            if file_type:
                count_stmt = count_stmt.where(UploadedFile.file_type == file_type)
            if status:
                count_stmt = count_stmt.where(UploadedFile.status == status)
            
            total_result = await self.db.execute(count_stmt)
            total = total_result.scalar()
            
            # 分页
            offset = (page - 1) * page_size
            stmt = stmt.offset(offset).limit(page_size).order_by(
                UploadedFile.created_at.desc()
            )
            
            result = await self.db.execute(stmt)
            files = result.scalars().all()
            
            return list(files), total
            
        except Exception as e:
            logger.error(f"获取上传历史失败: {str(e)}")
            return [], 0
    
    async def get_extracted_data(self, file_id: str) -> List[ExtractedData]:
        """获取文件提取的数据"""
        try:
            stmt = select(ExtractedData).where(
                ExtractedData.file_id == file_id,
                ExtractedData.is_deleted == False
            ).order_by(ExtractedData.created_at)
            
            result = await self.db.execute(stmt)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"获取提取数据失败: {str(e)}")
            return []
    
    async def add_to_processing_queue(
        self,
        file_id: str,
        processing_type: str,
        processing_params: Optional[Dict[str, Any]] = None,
        priority: int = 5
    ) -> FileProcessingQueue:
        """添加到处理队列"""
        try:
            queue_item = FileProcessingQueue(
                file_id=file_id,
                processing_type=processing_type,
                priority=priority,
                status="pending",
                processing_params=processing_params or {},
                scheduled_at=datetime.utcnow()
            )
            
            self.db.add(queue_item)
            await self.db.commit()
            await self.db.refresh(queue_item)
            
            return queue_item
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"添加到处理队列失败: {str(e)}")
            raise
    
    async def update_file_status(
        self,
        file_id: str,
        status: str,
        processing_result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """更新文件状态"""
        try:
            update_data = {
                "status": status,
                "updated_at": datetime.utcnow()
            }
            
            if processing_result:
                update_data["processing_result"] = processing_result
            
            if error_message:
                update_data["error_message"] = error_message
            
            if status in ["processed", "failed"]:
                update_data["processed_at"] = datetime.utcnow()
            
            stmt = update(UploadedFile).where(
                UploadedFile.id == file_id
            ).values(**update_data)
            
            await self.db.execute(stmt)
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新文件状态失败: {str(e)}")
            return False
    
    async def create_extracted_data(
        self,
        file_id: str,
        data_type: str,
        raw_data: Dict[str, Any],
        table_name: Optional[str] = None,
        sheet_name: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> ExtractedData:
        """创建提取数据记录"""
        try:
            # 计算统计信息
            row_count = 0
            column_count = 0
            
            if isinstance(raw_data, dict):
                if 'data' in raw_data and isinstance(raw_data['data'], list):
                    row_count = len(raw_data['data'])
                    if row_count > 0 and isinstance(raw_data['data'][0], (list, dict)):
                        if isinstance(raw_data['data'][0], list):
                            column_count = len(raw_data['data'][0])
                        else:
                            column_count = len(raw_data['data'][0].keys())
            
            extracted_data = ExtractedData(
                file_id=file_id,
                data_type=data_type,
                table_name=table_name,
                sheet_name=sheet_name,
                raw_data=raw_data,
                row_count=row_count,
                column_count=column_count,
                import_status="pending",
                created_by=user_id,
                updated_by=user_id
            )
            
            self.db.add(extracted_data)
            await self.db.commit()
            await self.db.refresh(extracted_data)
            
            return extracted_data
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建提取数据记录失败: {str(e)}")
            raise
    
    async def delete_file(self, file_id: str) -> bool:
        """删除文件（软删除）"""
        try:
            # 获取文件信息
            file_info = await self.get_file_by_id(file_id)
            if not file_info:
                return False
            
            # 软删除文件记录
            file_info.soft_delete()
            
            # 删除物理文件
            try:
                if os.path.exists(file_info.file_path):
                    os.remove(file_info.file_path)
            except Exception as e:
                logger.warning(f"删除物理文件失败: {str(e)}")
            
            await self.db.commit()
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除文件失败: {str(e)}")
            return False
    
    async def get_file_statistics(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """获取文件统计信息"""
        try:
            base_query = select(UploadedFile).where(UploadedFile.is_deleted == False)
            
            if user_id:
                base_query = base_query.where(UploadedFile.created_by == user_id)
            
            # 总文件数
            total_result = await self.db.execute(
                select(func.count(UploadedFile.id)).where(UploadedFile.is_deleted == False)
            )
            total_files = total_result.scalar()
            
            # 今日上传
            today = datetime.utcnow().date()
            today_result = await self.db.execute(
                select(func.count(UploadedFile.id)).where(
                    UploadedFile.is_deleted == False,
                    func.date(UploadedFile.created_at) == today
                )
            )
            uploaded_today = today_result.scalar()
            
            # 各状态统计
            status_stats = {}
            for status in ["uploaded", "processing", "processed", "failed"]:
                status_result = await self.db.execute(
                    select(func.count(UploadedFile.id)).where(
                        UploadedFile.is_deleted == False,
                        UploadedFile.status == status
                    )
                )
                status_stats[status] = status_result.scalar()
            
            # 总大小
            size_result = await self.db.execute(
                select(func.sum(UploadedFile.file_size)).where(
                    UploadedFile.is_deleted == False
                )
            )
            total_size = size_result.scalar() or 0
            
            # 文件类型统计
            type_result = await self.db.execute(
                select(
                    UploadedFile.file_type,
                    func.count(UploadedFile.id)
                ).where(
                    UploadedFile.is_deleted == False
                ).group_by(UploadedFile.file_type)
            )
            file_types = {row[0]: row[1] for row in type_result.fetchall()}
            
            return {
                "total_files": total_files,
                "uploaded_today": uploaded_today,
                "processing_files": status_stats.get("processing", 0),
                "processed_files": status_stats.get("processed", 0),
                "failed_files": status_stats.get("failed", 0),
                "total_size": total_size,
                "file_types": file_types
            }
            
        except Exception as e:
            logger.error(f"获取文件统计失败: {str(e)}")
            return {
                "total_files": 0,
                "uploaded_today": 0,
                "processing_files": 0,
                "processed_files": 0,
                "failed_files": 0,
                "total_size": 0,
                "file_types": {}
            }
