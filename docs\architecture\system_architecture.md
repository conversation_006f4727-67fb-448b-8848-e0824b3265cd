# 🏗️ Smart Planning 系统架构总览

## 📋 架构概述

Smart Planning（智能生产管理系统）采用现代化的分层架构设计，基于Industry 4.0标准，为智能制造工厂提供全面的生产管理解决方案。

## 🎯 设计原则

### 核心设计原则
1. **统一架构** - 避免功能重复和架构分散
2. **模块化设计** - 高内聚、低耦合的模块设计
3. **可扩展性** - 支持功能扩展和插件开发
4. **用户体验** - 简洁直观的用户界面

### 架构优势
- ✅ **避免功能重复** - 统一服务层管理所有功能
- ✅ **避免架构分散** - 14页面标准架构，统一管理
- ✅ **避免集成困难** - 标准化接口，无缝集成
- ✅ **避免用户体验差** - 智能导航，增强可视化

## 🏛️ 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层 (Streamlit)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 智能导航     │ │ 响应式布局   │ │ 增强图表     │ │ UI主题      │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      页面层 (15个核心页面)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 综合仪表板   │ │ 数据上传     │ │ 生产规划     │ │ 设备管理     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 计划监控     │ │ 数据分析     │ │ 智能助手     │ │ PCI管理     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 供应链协同   │ │ 能耗优化     │ │ 算法中心     │ │ 数据中心     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐                                                │
│  │ 系统管理     │                                                │
│  └─────────────┘                                                │
├─────────────────────────────────────────────────────────────┤
│                    统一服务层 (4个核心服务)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 统一AI服务   │ │ 统一算法服务 │ │ 统一数据服务 │ │ 统一系统服务 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      支持服务层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 数据处理     │ │ 文件管理     │ │ 认证授权     │ │ 配置管理     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      数据存储层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 文件存储     │ │ 数据缓存     │ │ 配置存储     │ │ 日志存储     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📱 页面层架构 (15个核心页面)

### 核心功能页面 (7个)
1. **01_综合仪表板** - 系统总览和实时监控
2. **02_数据上传** - 数据输入和文件管理
3. **03_生产规划** - 生产计划制定和优化
4. **04_设备管理** - 设备状态监控和管理
5. **05_计划监控** - 生产计划执行监控
6. **06_数据分析** - 数据分析和报表生成
7. **08_PCI管理** - PCI专业数据管理

### 智能功能页面 (3个)
8. **07_智能助手** - AI功能统一入口
9. **11_算法中心** - 算法功能统一入口
10. **12_数据中心** - 数据管理统一入口

### 高级功能页面 (2个) - Phase 2
11. **09_供应链协同** - 供应链管理和协同
12. **10_能耗优化** - 能源管理和优化

### 系统管理页面 (3个)
13. **13_系统管理** - 系统配置和用户管理
14. **14_数据库配置** - 数据库连接配置管理
15. **15_邮件配置** - 邮件服务器配置管理

## ⚙️ 统一服务层架构

### 1. 统一AI服务 (unified_ai_service.py)
**职责**: 整合所有AI功能
- 🤖 LLM对话服务
- 🔮 预测分析引擎
- 🔍 异常检测引擎
- ⚡ 智能优化引擎
- 🧠 强化学习服务
- 📚 学习引擎
- 🎨 UI增强服务

### 2. 统一算法服务 (unified_algorithm_service.py)
**职责**: 整合所有算法功能
- 🧮 遗传算法
- 🔥 模拟退火
- 🎯 贪心算法
- 🤖 强化学习
- 📊 机器学习
- 🔄 混合算法

### 3. 统一数据服务 (unified_data_service.py)
**职责**: 整合所有数据管理功能
- 📁 文件上传处理
- 📧 邮件数据导入
- 🗄️ 数据库集成
- 🔌 API接口
- 📡 设备传感器数据
- 🔬 PCI系统数据
- 🏭 ERP/MES系统数据
- ✅ 数据验证和清洗

### 4. 统一系统服务 (unified_system_service.py)
**职责**: 整合所有系统管理功能
- 👥 用户管理
- ⚙️ 系统配置
- 🔐 认证配置
- 🌐 多语言配置
- 🔧 扩展配置
- 🛡️ 安全管理
- 📊 监控管理
- 💾 备份管理

## 🔧 技术栈

### 前端技术
- **Streamlit** - 主要前端框架
- **Plotly** - 数据可视化
- **Pandas** - 数据处理
- **NumPy** - 数值计算

### 后端技术
- **FastAPI** - API服务框架
- **Python** - 主要开发语言
- **SQLAlchemy** - 数据库ORM
- **Pydantic** - 数据验证

### AI/ML技术
- **Ollama** - 本地LLM服务
- **Azure OpenAI** - 云端LLM服务
- **Scikit-learn** - 机器学习
- **Optuna** - 超参数优化

### 数据存储
- **SQLite/PostgreSQL** - 关系数据库
- **Redis** - 缓存存储
- **文件系统** - 文件存储

## 🚀 部署架构

### 开发环境
```
┌─────────────────┐
│   开发环境        │
│  ┌─────────────┐ │
│  │ Streamlit   │ │
│  │ (前端)      │ │
│  └─────────────┘ │
│  ┌─────────────┐ │
│  │ FastAPI     │ │
│  │ (后端)      │ │
│  └─────────────┘ │
│  ┌─────────────┐ │
│  │ SQLite      │ │
│  │ (数据库)    │ │
│  └─────────────┘ │
└─────────────────┘
```

### 生产环境
```
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│   前端服务        │ │   后端服务        │ │   数据服务        │
│  ┌─────────────┐ │ │  ┌─────────────┐ │ │  ┌─────────────┐ │
│  │ Streamlit   │ │ │  │ FastAPI     │ │ │  │ PostgreSQL  │ │
│  │ (容器化)    │ │ │  │ (容器化)    │ │ │  │ (主数据库)  │ │
│  └─────────────┘ │ │  └─────────────┘ │ │  └─────────────┘ │
│                  │ │                  │ │  ┌─────────────┐ │
│                  │ │                  │ │  │ Redis       │ │
│                  │ │                  │ │  │ (缓存)      │ │
│                  │ │                  │ │  └─────────────┘ │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

## 📊 数据流架构

### 数据处理流程
```
数据输入 → 数据验证 → 数据清洗 → 数据存储 → 数据分析 → 结果输出
    ↓         ↓         ↓         ↓         ↓         ↓
  文件上传   格式检查   标准化    数据库     AI分析    可视化
  邮件导入   完整性     去重      缓存      算法      报表
  API接口    一致性     转换      索引      预测      导出
```

### 服务调用流程
```
页面层 → 统一服务层 → 支持服务层 → 数据存储层
  ↓         ↓           ↓           ↓
用户交互   业务逻辑     数据处理     持久化
界面展示   算法执行     文件管理     缓存
数据可视化  AI推理      认证授权     配置
```

## 🔒 安全架构

### 安全层级
1. **前端安全** - 用户认证、会话管理
2. **API安全** - 接口鉴权、请求验证
3. **数据安全** - 数据加密、访问控制
4. **系统安全** - 日志审计、安全监控

### 认证授权
- **用户类型**: 管理员、计划员、主要用户、PCI用户、一般用户
- **权限控制**: 基于角色的访问控制(RBAC)
- **会话管理**: 安全会话、超时控制
- **审计日志**: 操作记录、安全审计

## 📈 性能架构

### 性能优化策略
1. **缓存策略** - 多层缓存、智能缓存
2. **异步处理** - 异步任务、后台处理
3. **数据优化** - 数据分页、懒加载
4. **资源优化** - 静态资源、CDN加速

### 监控指标
- **系统性能** - CPU、内存、磁盘使用率
- **应用性能** - 响应时间、吞吐量、错误率
- **用户体验** - 页面加载时间、交互响应
- **业务指标** - 用户活跃度、功能使用率

---

**Smart APS系统架构设计遵循现代软件工程最佳实践，确保系统的可靠性、可扩展性和可维护性。**
