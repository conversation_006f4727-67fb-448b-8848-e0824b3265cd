"""
供应链协同服务 - 上下游集成与协同优化
"""

import asyncio
import aiohttp
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import logging
import json

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_

logger = logging.getLogger(__name__)


class SupplierStatus(Enum):
    """供应商状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    EVALUATION = "evaluation"


class OrderStatus(Enum):
    """采购订单状态"""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    IN_TRANSIT = "in_transit"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"


@dataclass
class SupplierInfo:
    """供应商信息"""
    supplier_id: str
    name: str
    contact_info: Dict[str, str]
    capabilities: List[str]
    lead_time_days: int
    quality_rating: float
    cost_rating: float
    delivery_rating: float
    status: SupplierStatus
    api_endpoint: Optional[str] = None
    api_key: Optional[str] = None


@dataclass
class PurchaseOrder:
    """采购订单"""
    order_id: str
    supplier_id: str
    material_code: str
    quantity: float
    unit_price: float
    total_amount: float
    order_date: datetime
    required_date: datetime
    status: OrderStatus
    tracking_info: Optional[Dict[str, Any]] = None


@dataclass
class InventoryItem:
    """库存项目"""
    material_code: str
    current_stock: float
    reserved_stock: float
    available_stock: float
    reorder_point: float
    max_stock: float
    unit_cost: float
    last_updated: datetime


@dataclass
class DemandForecast:
    """需求预测"""
    material_code: str
    forecast_date: datetime
    predicted_demand: float
    confidence_level: float
    trend: str


class SupplyChainService:
    """供应链协同服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.suppliers: Dict[str, SupplierInfo] = {}
        self.purchase_orders: Dict[str, PurchaseOrder] = {}
        self.inventory: Dict[str, InventoryItem] = {}
        self.demand_forecasts: Dict[str, List[DemandForecast]] = {}
        
        # 协同配置
        self.sync_interval = 300  # 5分钟同步一次
        self.is_running = False
        self.sync_tasks = {}
        
        # HTTP会话
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def initialize(self):
        """初始化供应链服务"""
        try:
            # 加载供应商信息
            await self._load_suppliers()
            
            # 加载采购订单
            await self._load_purchase_orders()
            
            # 加载库存信息
            await self._load_inventory()
            
            # 启动协同服务
            await self.start_collaboration()
            
            logger.info("供应链协同服务初始化完成")
            return {"status": "success", "suppliers": len(self.suppliers)}
            
        except Exception as e:
            logger.error(f"供应链服务初始化失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def start_collaboration(self):
        """启动供应链协同"""
        if self.is_running:
            logger.warning("供应链协同已在运行")
            return
        
        self.is_running = True
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        
        # 启动各种协同任务
        self.sync_tasks = {
            'supplier_sync': asyncio.create_task(self._sync_suppliers()),
            'order_tracking': asyncio.create_task(self._track_orders()),
            'inventory_sync': asyncio.create_task(self._sync_inventory()),
            'demand_sharing': asyncio.create_task(self._share_demand_forecast())
        }
        
        logger.info("供应链协同服务已启动")
    
    async def stop_collaboration(self):
        """停止供应链协同"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 取消所有同步任务
        for task_name, task in self.sync_tasks.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                logger.info(f"已取消{task_name}协同任务")
        
        # 关闭HTTP会话
        if self.session:
            await self.session.close()
        
        logger.info("供应链协同服务已停止")
    
    async def _load_suppliers(self):
        """加载供应商信息"""
        # 模拟供应商数据
        suppliers_data = [
            {
                "supplier_id": "SUP001",
                "name": "优质材料供应商",
                "contact_info": {"phone": "021-12345678", "email": "<EMAIL>"},
                "capabilities": ["钢材", "铝材", "塑料"],
                "lead_time_days": 7,
                "quality_rating": 4.5,
                "cost_rating": 4.2,
                "delivery_rating": 4.8,
                "status": SupplierStatus.ACTIVE,
                "api_endpoint": "https://api.supplier1.com/v1"
            },
            {
                "supplier_id": "SUP002", 
                "name": "快速配送供应商",
                "contact_info": {"phone": "021-87654321", "email": "<EMAIL>"},
                "capabilities": ["电子元件", "传感器", "控制器"],
                "lead_time_days": 3,
                "quality_rating": 4.3,
                "cost_rating": 3.8,
                "delivery_rating": 4.9,
                "status": SupplierStatus.ACTIVE,
                "api_endpoint": "https://api.supplier2.com/v1"
            }
        ]
        
        for data in suppliers_data:
            supplier = SupplierInfo(**data)
            self.suppliers[supplier.supplier_id] = supplier
    
    async def _load_purchase_orders(self):
        """加载采购订单"""
        # 模拟采购订单数据
        orders_data = [
            {
                "order_id": "PO001",
                "supplier_id": "SUP001",
                "material_code": "MAT001",
                "quantity": 1000.0,
                "unit_price": 15.5,
                "total_amount": 15500.0,
                "order_date": datetime.now() - timedelta(days=5),
                "required_date": datetime.now() + timedelta(days=2),
                "status": OrderStatus.IN_TRANSIT,
                "tracking_info": {"tracking_number": "TRK123456", "carrier": "顺丰快递"}
            }
        ]
        
        for data in orders_data:
            order = PurchaseOrder(**data)
            self.purchase_orders[order.order_id] = order
    
    async def _load_inventory(self):
        """加载库存信息"""
        # 模拟库存数据
        inventory_data = [
            {
                "material_code": "MAT001",
                "current_stock": 500.0,
                "reserved_stock": 200.0,
                "available_stock": 300.0,
                "reorder_point": 100.0,
                "max_stock": 2000.0,
                "unit_cost": 15.5,
                "last_updated": datetime.now()
            },
            {
                "material_code": "MAT002",
                "current_stock": 80.0,
                "reserved_stock": 50.0,
                "available_stock": 30.0,
                "reorder_point": 50.0,
                "max_stock": 500.0,
                "unit_cost": 25.8,
                "last_updated": datetime.now()
            }
        ]
        
        for data in inventory_data:
            item = InventoryItem(**data)
            self.inventory[item.material_code] = item
    
    async def _sync_suppliers(self):
        """同步供应商信息"""
        while self.is_running:
            try:
                for supplier in self.suppliers.values():
                    if supplier.api_endpoint and supplier.status == SupplierStatus.ACTIVE:
                        await self._sync_supplier_data(supplier)
                
                await asyncio.sleep(self.sync_interval)
                
            except Exception as e:
                logger.error(f"同步供应商信息失败: {e}")
                await asyncio.sleep(self.sync_interval)
    
    async def _sync_supplier_data(self, supplier: SupplierInfo):
        """同步单个供应商数据"""
        try:
            if not self.session:
                return
            
            # 获取供应商库存信息
            url = f"{supplier.api_endpoint}/inventory"
            headers = {"Authorization": f"Bearer {supplier.api_key}"} if supplier.api_key else {}
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    await self._process_supplier_inventory(supplier.supplier_id, data)
                    
        except Exception as e:
            logger.error(f"同步供应商 {supplier.supplier_id} 数据失败: {e}")
    
    async def _process_supplier_inventory(self, supplier_id: str, inventory_data: List[Dict]):
        """处理供应商库存数据"""
        # 更新供应商库存信息
        for item in inventory_data:
            material_code = item.get("material_code")
            available_qty = item.get("available_quantity", 0)
            
            # 这里可以更新本地库存或触发补货建议
            logger.debug(f"供应商 {supplier_id} 材料 {material_code} 可用库存: {available_qty}")
    
    async def _track_orders(self):
        """跟踪采购订单"""
        while self.is_running:
            try:
                for order in self.purchase_orders.values():
                    if order.status in [OrderStatus.CONFIRMED, OrderStatus.IN_TRANSIT]:
                        await self._update_order_status(order)
                
                await asyncio.sleep(self.sync_interval)
                
            except Exception as e:
                logger.error(f"跟踪采购订单失败: {e}")
                await asyncio.sleep(self.sync_interval)
    
    async def _update_order_status(self, order: PurchaseOrder):
        """更新订单状态"""
        try:
            supplier = self.suppliers.get(order.supplier_id)
            if not supplier or not supplier.api_endpoint:
                return
            
            if not self.session:
                return
            
            # 查询订单状态
            url = f"{supplier.api_endpoint}/orders/{order.order_id}/status"
            headers = {"Authorization": f"Bearer {supplier.api_key}"} if supplier.api_key else {}
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    new_status = data.get("status")
                    
                    if new_status and new_status != order.status.value:
                        order.status = OrderStatus(new_status)
                        order.tracking_info = data.get("tracking_info", order.tracking_info)
                        logger.info(f"订单 {order.order_id} 状态更新为: {new_status}")
                        
        except Exception as e:
            logger.error(f"更新订单 {order.order_id} 状态失败: {e}")
    
    async def _sync_inventory(self):
        """同步库存信息"""
        while self.is_running:
            try:
                # 检查库存水平，生成补货建议
                reorder_suggestions = await self._generate_reorder_suggestions()
                
                if reorder_suggestions:
                    await self._process_reorder_suggestions(reorder_suggestions)
                
                await asyncio.sleep(self.sync_interval)
                
            except Exception as e:
                logger.error(f"同步库存信息失败: {e}")
                await asyncio.sleep(self.sync_interval)
    
    async def _generate_reorder_suggestions(self) -> List[Dict[str, Any]]:
        """生成补货建议"""
        suggestions = []
        
        for material_code, item in self.inventory.items():
            if item.available_stock <= item.reorder_point:
                # 计算建议订购量
                suggested_qty = item.max_stock - item.current_stock
                
                # 选择最佳供应商
                best_supplier = await self._select_best_supplier(material_code)
                
                if best_supplier:
                    suggestions.append({
                        "material_code": material_code,
                        "current_stock": item.current_stock,
                        "reorder_point": item.reorder_point,
                        "suggested_quantity": suggested_qty,
                        "recommended_supplier": best_supplier.supplier_id,
                        "estimated_cost": suggested_qty * item.unit_cost,
                        "urgency": "HIGH" if item.available_stock < item.reorder_point * 0.5 else "MEDIUM"
                    })
        
        return suggestions
    
    async def _select_best_supplier(self, material_code: str) -> Optional[SupplierInfo]:
        """选择最佳供应商"""
        suitable_suppliers = [
            supplier for supplier in self.suppliers.values()
            if material_code in supplier.capabilities and supplier.status == SupplierStatus.ACTIVE
        ]
        
        if not suitable_suppliers:
            return None
        
        # 综合评分选择最佳供应商
        best_supplier = max(suitable_suppliers, key=lambda s: (
            s.quality_rating * 0.4 + 
            s.delivery_rating * 0.4 + 
            s.cost_rating * 0.2
        ))
        
        return best_supplier
    
    async def _process_reorder_suggestions(self, suggestions: List[Dict[str, Any]]):
        """处理补货建议"""
        for suggestion in suggestions:
            logger.info(f"补货建议: {suggestion['material_code']} - "
                       f"建议数量: {suggestion['suggested_quantity']} - "
                       f"推荐供应商: {suggestion['recommended_supplier']}")
            
            # 这里可以自动创建采购订单或发送通知
    
    async def _share_demand_forecast(self):
        """共享需求预测"""
        while self.is_running:
            try:
                # 与供应商共享需求预测信息
                for supplier in self.suppliers.values():
                    if supplier.api_endpoint and supplier.status == SupplierStatus.ACTIVE:
                        await self._send_demand_forecast(supplier)
                
                await asyncio.sleep(self.sync_interval * 4)  # 每20分钟共享一次
                
            except Exception as e:
                logger.error(f"共享需求预测失败: {e}")
                await asyncio.sleep(self.sync_interval)
    
    async def _send_demand_forecast(self, supplier: SupplierInfo):
        """向供应商发送需求预测"""
        try:
            if not self.session:
                return
            
            # 准备相关材料的需求预测
            relevant_forecasts = []
            for material_code in supplier.capabilities:
                if material_code in self.demand_forecasts:
                    relevant_forecasts.extend(self.demand_forecasts[material_code])
            
            if not relevant_forecasts:
                return
            
            # 发送预测数据
            url = f"{supplier.api_endpoint}/demand-forecast"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {supplier.api_key}"
            } if supplier.api_key else {"Content-Type": "application/json"}
            
            forecast_data = [
                {
                    "material_code": f.material_code,
                    "forecast_date": f.forecast_date.isoformat(),
                    "predicted_demand": f.predicted_demand,
                    "confidence_level": f.confidence_level
                }
                for f in relevant_forecasts
            ]
            
            async with self.session.post(url, headers=headers, json=forecast_data) as response:
                if response.status == 200:
                    logger.debug(f"需求预测已发送给供应商 {supplier.supplier_id}")
                    
        except Exception as e:
            logger.error(f"向供应商 {supplier.supplier_id} 发送需求预测失败: {e}")
    
    async def get_supply_chain_status(self) -> Dict[str, Any]:
        """获取供应链状态"""
        try:
            # 统计供应商状态
            supplier_stats = {
                "total": len(self.suppliers),
                "active": len([s for s in self.suppliers.values() if s.status == SupplierStatus.ACTIVE]),
                "average_quality": sum(s.quality_rating for s in self.suppliers.values()) / len(self.suppliers) if self.suppliers else 0
            }
            
            # 统计采购订单状态
            order_stats = {
                "total": len(self.purchase_orders),
                "pending": len([o for o in self.purchase_orders.values() if o.status == OrderStatus.PENDING]),
                "in_transit": len([o for o in self.purchase_orders.values() if o.status == OrderStatus.IN_TRANSIT]),
                "total_value": sum(o.total_amount for o in self.purchase_orders.values())
            }
            
            # 统计库存状态
            inventory_stats = {
                "total_items": len(self.inventory),
                "low_stock_items": len([i for i in self.inventory.values() if i.available_stock <= i.reorder_point]),
                "total_value": sum(i.current_stock * i.unit_cost for i in self.inventory.values())
            }
            
            return {
                "status": "running" if self.is_running else "stopped",
                "suppliers": supplier_stats,
                "orders": order_stats,
                "inventory": inventory_stats,
                "last_sync": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取供应链状态失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def create_purchase_order(self, material_code: str, quantity: float, 
                                  supplier_id: Optional[str] = None) -> Dict[str, Any]:
        """创建采购订单"""
        try:
            # 选择供应商
            if not supplier_id:
                supplier = await self._select_best_supplier(material_code)
                if not supplier:
                    return {"success": False, "error": "未找到合适的供应商"}
                supplier_id = supplier.supplier_id
            
            supplier = self.suppliers.get(supplier_id)
            if not supplier:
                return {"success": False, "error": "供应商不存在"}
            
            # 获取材料成本
            inventory_item = self.inventory.get(material_code)
            unit_price = inventory_item.unit_cost if inventory_item else 10.0
            
            # 创建采购订单
            order_id = f"PO{datetime.now().strftime('%Y%m%d%H%M%S')}"
            order = PurchaseOrder(
                order_id=order_id,
                supplier_id=supplier_id,
                material_code=material_code,
                quantity=quantity,
                unit_price=unit_price,
                total_amount=quantity * unit_price,
                order_date=datetime.now(),
                required_date=datetime.now() + timedelta(days=supplier.lead_time_days),
                status=OrderStatus.PENDING
            )
            
            self.purchase_orders[order_id] = order
            
            # 发送订单给供应商
            if supplier.api_endpoint:
                await self._send_purchase_order(supplier, order)
            
            return {
                "success": True,
                "order_id": order_id,
                "supplier": supplier.name,
                "total_amount": order.total_amount,
                "expected_delivery": order.required_date.isoformat()
            }
            
        except Exception as e:
            logger.error(f"创建采购订单失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _send_purchase_order(self, supplier: SupplierInfo, order: PurchaseOrder):
        """向供应商发送采购订单"""
        try:
            if not self.session:
                return
            
            url = f"{supplier.api_endpoint}/orders"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {supplier.api_key}"
            } if supplier.api_key else {"Content-Type": "application/json"}
            
            order_data = {
                "order_id": order.order_id,
                "material_code": order.material_code,
                "quantity": order.quantity,
                "unit_price": order.unit_price,
                "required_date": order.required_date.isoformat()
            }
            
            async with self.session.post(url, headers=headers, json=order_data) as response:
                if response.status == 200:
                    order.status = OrderStatus.CONFIRMED
                    logger.info(f"采购订单 {order.order_id} 已发送给供应商 {supplier.supplier_id}")
                    
        except Exception as e:
            logger.error(f"发送采购订单给供应商失败: {e}")


# 全局供应链服务实例
supply_chain_service = None

async def get_supply_chain_service(db: AsyncSession) -> SupplyChainService:
    """获取供应链服务实例"""
    global supply_chain_service
    if supply_chain_service is None:
        supply_chain_service = SupplyChainService(db)
        await supply_chain_service.initialize()
    return supply_chain_service
