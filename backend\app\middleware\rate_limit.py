"""
限流中间件
"""

import time
import logging
from typing import Callable, Dict
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings
from app.core.redis import redis_manager

logger = logging.getLogger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""
    
    def __init__(self, app, requests_per_minute: int = None, window_seconds: int = None):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute or settings.RATE_LIMIT_REQUESTS
        self.window_seconds = window_seconds or settings.RATE_LIMIT_WINDOW
        self.local_cache: Dict[str, Dict] = {}  # 本地缓存作为备用
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 获取客户端标识
        client_id = self._get_client_id(request)
        
        # 检查限流
        if await self._is_rate_limited(client_id):
            logger.warning(
                f"🚫 限流触发",
                extra={
                    "client_id": client_id,
                    "url": str(request.url),
                    "method": request.method
                }
            )
            
            raise HTTPException(
                status_code=429,
                detail="请求过于频繁，请稍后再试",
                headers={"Retry-After": str(self.window_seconds)}
            )
        
        # 记录请求
        await self._record_request(client_id)
        
        # 处理请求
        response = await call_next(request)
        
        # 添加限流信息到响应头
        remaining = await self._get_remaining_requests(client_id)
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + self.window_seconds)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用认证用户ID
        if hasattr(request.state, "user_id"):
            return f"user:{request.state.user_id}"
        
        # 使用IP地址
        client_ip = request.headers.get("x-forwarded-for", "").split(",")[0].strip()
        if not client_ip:
            client_ip = request.headers.get("x-real-ip", "")
        if not client_ip:
            client_ip = request.client.host if request.client else "unknown"
        
        return f"ip:{client_ip}"
    
    async def _is_rate_limited(self, client_id: str) -> bool:
        """检查是否被限流"""
        try:
            # 尝试使用Redis
            if redis_manager._connected:
                return await self._redis_rate_limit_check(client_id)
            else:
                return self._local_rate_limit_check(client_id)
        except Exception as e:
            logger.error(f"限流检查失败: {str(e)}")
            # 出错时使用本地缓存
            return self._local_rate_limit_check(client_id)
    
    async def _redis_rate_limit_check(self, client_id: str) -> bool:
        """Redis限流检查"""
        key = f"rate_limit:{client_id}"
        current_time = int(time.time())
        window_start = current_time - self.window_seconds
        
        # 使用Redis的有序集合实现滑动窗口
        pipe = redis_manager.redis_client.pipeline()
        
        # 移除过期的请求记录
        pipe.zremrangebyscore(key, 0, window_start)
        
        # 获取当前窗口内的请求数
        pipe.zcard(key)
        
        # 执行管道
        results = await pipe.execute()
        current_requests = results[1]
        
        return current_requests >= self.requests_per_minute
    
    def _local_rate_limit_check(self, client_id: str) -> bool:
        """本地缓存限流检查"""
        current_time = time.time()
        
        if client_id not in self.local_cache:
            self.local_cache[client_id] = {"requests": [], "last_cleanup": current_time}
        
        client_data = self.local_cache[client_id]
        
        # 清理过期请求（每分钟清理一次）
        if current_time - client_data["last_cleanup"] > 60:
            window_start = current_time - self.window_seconds
            client_data["requests"] = [
                req_time for req_time in client_data["requests"] 
                if req_time > window_start
            ]
            client_data["last_cleanup"] = current_time
        
        return len(client_data["requests"]) >= self.requests_per_minute
    
    async def _record_request(self, client_id: str):
        """记录请求"""
        try:
            if redis_manager._connected:
                await self._redis_record_request(client_id)
            else:
                self._local_record_request(client_id)
        except Exception as e:
            logger.error(f"记录请求失败: {str(e)}")
            # 出错时使用本地缓存
            self._local_record_request(client_id)
    
    async def _redis_record_request(self, client_id: str):
        """Redis记录请求"""
        key = f"rate_limit:{client_id}"
        current_time = time.time()
        
        # 添加当前请求时间戳
        await redis_manager.redis_client.zadd(key, {str(current_time): current_time})
        
        # 设置过期时间
        await redis_manager.redis_client.expire(key, self.window_seconds * 2)
    
    def _local_record_request(self, client_id: str):
        """本地缓存记录请求"""
        current_time = time.time()
        
        if client_id not in self.local_cache:
            self.local_cache[client_id] = {"requests": [], "last_cleanup": current_time}
        
        self.local_cache[client_id]["requests"].append(current_time)
    
    async def _get_remaining_requests(self, client_id: str) -> int:
        """获取剩余请求数"""
        try:
            if redis_manager._connected:
                key = f"rate_limit:{client_id}"
                current_requests = await redis_manager.redis_client.zcard(key)
            else:
                if client_id in self.local_cache:
                    current_requests = len(self.local_cache[client_id]["requests"])
                else:
                    current_requests = 0
            
            return max(0, self.requests_per_minute - current_requests)
            
        except Exception:
            return self.requests_per_minute
