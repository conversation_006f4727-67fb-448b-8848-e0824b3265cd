"""
能耗优化服务 - 绿色制造支持
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import logging
import json
import numpy as np

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_

logger = logging.getLogger(__name__)


class EnergyType(Enum):
    """能源类型"""
    ELECTRICITY = "electricity"
    NATURAL_GAS = "natural_gas"
    STEAM = "steam"
    COMPRESSED_AIR = "compressed_air"
    COOLING_WATER = "cooling_water"


class OptimizationStrategy(Enum):
    """优化策略"""
    PEAK_SHAVING = "peak_shaving"  # 削峰填谷
    LOAD_BALANCING = "load_balancing"  # 负载均衡
    RENEWABLE_PRIORITY = "renewable_priority"  # 可再生能源优先
    COST_MINIMIZATION = "cost_minimization"  # 成本最小化
    CARBON_REDUCTION = "carbon_reduction"  # 碳减排


@dataclass
class EnergyConsumption:
    """能耗数据"""
    equipment_id: str
    energy_type: EnergyType
    consumption: float  # 消耗量
    unit: str  # 单位
    timestamp: datetime
    cost: float  # 成本
    carbon_emission: float  # 碳排放


@dataclass
class EnergyTariff:
    """电价信息"""
    tariff_type: str  # 峰谷平
    start_time: str
    end_time: str
    price_per_kwh: float
    season: str  # 季节


@dataclass
class RenewableEnergy:
    """可再生能源"""
    source_type: str  # 太阳能、风能等
    capacity: float  # 装机容量
    current_output: float  # 当前发电量
    forecast_output: List[float]  # 预测发电量
    efficiency: float  # 效率


@dataclass
class EnergyOptimizationPlan:
    """能耗优化计划"""
    plan_id: str
    equipment_schedule: Dict[str, List[Dict]]  # 设备排程
    energy_allocation: Dict[str, float]  # 能源分配
    expected_savings: float  # 预期节省
    carbon_reduction: float  # 碳减排量
    optimization_strategy: OptimizationStrategy
    created_at: datetime


class EnergyOptimizationService:
    """能耗优化服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.energy_data: Dict[str, List[EnergyConsumption]] = {}
        self.tariff_schedule: List[EnergyTariff] = []
        self.renewable_sources: Dict[str, RenewableEnergy] = {}
        self.optimization_plans: Dict[str, EnergyOptimizationPlan] = {}
        
        # 优化配置
        self.optimization_config = {
            "peak_hours": ["09:00-12:00", "18:00-22:00"],
            "valley_hours": ["23:00-07:00"],
            "carbon_factor": 0.5968,  # kg CO2/kWh
            "renewable_priority": True,
            "max_demand_limit": 1000.0,  # kW
            "target_efficiency_improvement": 0.15
        }
        
        # 监控状态
        self.is_monitoring = False
        self.monitoring_tasks = {}
    
    async def initialize(self):
        """初始化能耗优化服务"""
        try:
            # 加载历史能耗数据
            await self._load_energy_data()
            
            # 加载电价信息
            await self._load_tariff_schedule()
            
            # 加载可再生能源信息
            await self._load_renewable_sources()
            
            # 启动实时监控
            await self.start_monitoring()
            
            logger.info("能耗优化服务初始化完成")
            return {"status": "success", "equipment_count": len(self.energy_data)}
            
        except Exception as e:
            logger.error(f"能耗优化服务初始化失败: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def _load_energy_data(self):
        """加载能耗数据"""
        # 模拟设备能耗数据
        equipment_list = ["L01", "L02", "L03", "L04", "Tank01", "Tank02", "Tank03"]
        
        for equipment_id in equipment_list:
            self.energy_data[equipment_id] = []
            
            # 生成24小时的模拟数据
            for hour in range(24):
                timestamp = datetime.now().replace(hour=hour, minute=0, second=0, microsecond=0)
                
                # 模拟不同时段的能耗变化
                base_consumption = 50 if equipment_id.startswith("L") else 30
                time_factor = 1.2 if 9 <= hour <= 17 else 0.8  # 工作时间能耗更高
                consumption = base_consumption * time_factor * (1 + np.random.normal(0, 0.1))
                
                energy_data = EnergyConsumption(
                    equipment_id=equipment_id,
                    energy_type=EnergyType.ELECTRICITY,
                    consumption=max(0, consumption),
                    unit="kWh",
                    timestamp=timestamp,
                    cost=consumption * 0.8,  # 假设电价0.8元/kWh
                    carbon_emission=consumption * self.optimization_config["carbon_factor"]
                )
                
                self.energy_data[equipment_id].append(energy_data)
    
    async def _load_tariff_schedule(self):
        """加载电价信息"""
        self.tariff_schedule = [
            EnergyTariff("峰时", "09:00", "12:00", 1.2, "夏季"),
            EnergyTariff("峰时", "18:00", "22:00", 1.2, "夏季"),
            EnergyTariff("平时", "07:00", "09:00", 0.8, "夏季"),
            EnergyTariff("平时", "12:00", "18:00", 0.8, "夏季"),
            EnergyTariff("平时", "22:00", "23:00", 0.8, "夏季"),
            EnergyTariff("谷时", "23:00", "07:00", 0.4, "夏季")
        ]
    
    async def _load_renewable_sources(self):
        """加载可再生能源信息"""
        # 模拟太阳能发电
        solar_forecast = [0] * 6 + [10, 30, 50, 70, 80, 90, 95, 90, 80, 60, 30, 10] + [0] * 6
        
        self.renewable_sources["solar"] = RenewableEnergy(
            source_type="太阳能",
            capacity=100.0,  # 100kW
            current_output=solar_forecast[datetime.now().hour],
            forecast_output=solar_forecast,
            efficiency=0.85
        )
        
        # 模拟风能发电
        wind_forecast = [20 + np.random.normal(0, 5) for _ in range(24)]
        wind_forecast = [max(0, min(50, x)) for x in wind_forecast]
        
        self.renewable_sources["wind"] = RenewableEnergy(
            source_type="风能",
            capacity=50.0,  # 50kW
            current_output=wind_forecast[datetime.now().hour],
            forecast_output=wind_forecast,
            efficiency=0.75
        )
    
    async def start_monitoring(self):
        """启动能耗监控"""
        if self.is_monitoring:
            logger.warning("能耗监控已在运行")
            return
        
        self.is_monitoring = True
        
        # 启动监控任务
        self.monitoring_tasks = {
            'energy_collection': asyncio.create_task(self._collect_energy_data()),
            'optimization_trigger': asyncio.create_task(self._trigger_optimization()),
            'renewable_monitoring': asyncio.create_task(self._monitor_renewable_sources()),
            'alert_monitoring': asyncio.create_task(self._monitor_energy_alerts())
        }
        
        logger.info("能耗监控已启动")
    
    async def stop_monitoring(self):
        """停止能耗监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        # 取消所有监控任务
        for task_name, task in self.monitoring_tasks.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                logger.info(f"已取消{task_name}监控任务")
        
        logger.info("能耗监控已停止")
    
    async def _collect_energy_data(self):
        """收集能耗数据"""
        while self.is_monitoring:
            try:
                current_time = datetime.now()
                
                # 更新各设备的实时能耗
                for equipment_id in self.energy_data.keys():
                    # 模拟实时能耗数据
                    base_consumption = 50 if equipment_id.startswith("L") else 30
                    hour = current_time.hour
                    time_factor = 1.2 if 9 <= hour <= 17 else 0.8
                    consumption = base_consumption * time_factor * (1 + np.random.normal(0, 0.1))
                    
                    # 获取当前电价
                    current_tariff = self._get_current_tariff(current_time)
                    
                    energy_data = EnergyConsumption(
                        equipment_id=equipment_id,
                        energy_type=EnergyType.ELECTRICITY,
                        consumption=max(0, consumption),
                        unit="kWh",
                        timestamp=current_time,
                        cost=consumption * current_tariff.price_per_kwh,
                        carbon_emission=consumption * self.optimization_config["carbon_factor"]
                    )
                    
                    # 保持最近24小时的数据
                    self.energy_data[equipment_id].append(energy_data)
                    self.energy_data[equipment_id] = self.energy_data[equipment_id][-24:]
                
                await asyncio.sleep(300)  # 5分钟收集一次
                
            except Exception as e:
                logger.error(f"收集能耗数据失败: {e}")
                await asyncio.sleep(300)
    
    def _get_current_tariff(self, timestamp: datetime) -> EnergyTariff:
        """获取当前电价"""
        current_time = timestamp.strftime("%H:%M")
        
        for tariff in self.tariff_schedule:
            if self._time_in_range(current_time, tariff.start_time, tariff.end_time):
                return tariff
        
        # 默认返回平时电价
        return EnergyTariff("平时", "00:00", "23:59", 0.8, "夏季")
    
    def _time_in_range(self, current_time: str, start_time: str, end_time: str) -> bool:
        """判断时间是否在范围内"""
        current = datetime.strptime(current_time, "%H:%M").time()
        start = datetime.strptime(start_time, "%H:%M").time()
        end = datetime.strptime(end_time, "%H:%M").time()
        
        if start <= end:
            return start <= current <= end
        else:  # 跨天的情况
            return current >= start or current <= end
    
    async def _trigger_optimization(self):
        """触发优化"""
        while self.is_monitoring:
            try:
                # 每小时检查一次是否需要优化
                current_hour = datetime.now().hour
                
                # 在峰时前1小时触发优化
                if current_hour in [8, 17]:  # 峰时前1小时
                    await self.optimize_energy_consumption(OptimizationStrategy.PEAK_SHAVING)
                
                # 在可再生能源发电高峰时触发优化
                solar_output = self.renewable_sources["solar"].current_output
                if solar_output > 70:  # 太阳能发电量大于70kW时
                    await self.optimize_energy_consumption(OptimizationStrategy.RENEWABLE_PRIORITY)
                
                await asyncio.sleep(3600)  # 1小时检查一次
                
            except Exception as e:
                logger.error(f"触发优化失败: {e}")
                await asyncio.sleep(3600)
    
    async def _monitor_renewable_sources(self):
        """监控可再生能源"""
        while self.is_monitoring:
            try:
                current_hour = datetime.now().hour
                
                # 更新可再生能源发电量
                for source_id, source in self.renewable_sources.items():
                    if source_id == "solar":
                        # 太阳能发电受天气影响
                        base_output = source.forecast_output[current_hour]
                        weather_factor = np.random.uniform(0.7, 1.0)  # 模拟天气影响
                        source.current_output = base_output * weather_factor
                    elif source_id == "wind":
                        # 风能发电
                        base_output = source.forecast_output[current_hour]
                        wind_factor = np.random.uniform(0.8, 1.2)  # 模拟风速变化
                        source.current_output = min(source.capacity, base_output * wind_factor)
                
                await asyncio.sleep(600)  # 10分钟更新一次
                
            except Exception as e:
                logger.error(f"监控可再生能源失败: {e}")
                await asyncio.sleep(600)
    
    async def _monitor_energy_alerts(self):
        """监控能耗告警"""
        while self.is_monitoring:
            try:
                # 检查总能耗是否超限
                total_consumption = sum(
                    data[-1].consumption for data in self.energy_data.values() if data
                )
                
                if total_consumption > self.optimization_config["max_demand_limit"]:
                    logger.warning(f"总能耗超限: {total_consumption:.2f} kW")
                    # 触发紧急优化
                    await self.optimize_energy_consumption(OptimizationStrategy.LOAD_BALANCING)
                
                # 检查设备异常能耗
                for equipment_id, data_list in self.energy_data.items():
                    if data_list:
                        recent_consumption = data_list[-1].consumption
                        avg_consumption = np.mean([d.consumption for d in data_list[-6:]])  # 最近6次的平均值
                        
                        if recent_consumption > avg_consumption * 1.5:
                            logger.warning(f"设备 {equipment_id} 能耗异常: {recent_consumption:.2f} kW")
                
                await asyncio.sleep(300)  # 5分钟检查一次
                
            except Exception as e:
                logger.error(f"监控能耗告警失败: {e}")
                await asyncio.sleep(300)
    
    async def optimize_energy_consumption(self, strategy: OptimizationStrategy) -> Dict[str, Any]:
        """优化能耗"""
        try:
            plan_id = f"OPT_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 根据策略生成优化计划
            if strategy == OptimizationStrategy.PEAK_SHAVING:
                plan = await self._generate_peak_shaving_plan(plan_id)
            elif strategy == OptimizationStrategy.LOAD_BALANCING:
                plan = await self._generate_load_balancing_plan(plan_id)
            elif strategy == OptimizationStrategy.RENEWABLE_PRIORITY:
                plan = await self._generate_renewable_priority_plan(plan_id)
            elif strategy == OptimizationStrategy.COST_MINIMIZATION:
                plan = await self._generate_cost_minimization_plan(plan_id)
            elif strategy == OptimizationStrategy.CARBON_REDUCTION:
                plan = await self._generate_carbon_reduction_plan(plan_id)
            else:
                return {"success": False, "error": "未知的优化策略"}
            
            # 保存优化计划
            self.optimization_plans[plan_id] = plan
            
            logger.info(f"能耗优化计划已生成: {plan_id} - 策略: {strategy.value}")
            
            return {
                "success": True,
                "plan_id": plan_id,
                "strategy": strategy.value,
                "expected_savings": plan.expected_savings,
                "carbon_reduction": plan.carbon_reduction,
                "equipment_adjustments": len(plan.equipment_schedule)
            }
            
        except Exception as e:
            logger.error(f"能耗优化失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _generate_peak_shaving_plan(self, plan_id: str) -> EnergyOptimizationPlan:
        """生成削峰填谷计划"""
        equipment_schedule = {}
        
        # 识别高能耗设备
        high_consumption_equipment = []
        for equipment_id, data_list in self.energy_data.items():
            if data_list and data_list[-1].consumption > 40:
                high_consumption_equipment.append(equipment_id)
        
        # 为高能耗设备安排错峰运行
        for equipment_id in high_consumption_equipment:
            equipment_schedule[equipment_id] = [
                {"action": "reduce_power", "start_time": "09:00", "end_time": "12:00", "power_reduction": 0.2},
                {"action": "reduce_power", "start_time": "18:00", "end_time": "22:00", "power_reduction": 0.2},
                {"action": "increase_power", "start_time": "23:00", "end_time": "07:00", "power_increase": 0.1}
            ]
        
        # 计算预期节省
        expected_savings = len(high_consumption_equipment) * 50 * 0.4  # 简化计算
        carbon_reduction = expected_savings * self.optimization_config["carbon_factor"]
        
        return EnergyOptimizationPlan(
            plan_id=plan_id,
            equipment_schedule=equipment_schedule,
            energy_allocation={},
            expected_savings=expected_savings,
            carbon_reduction=carbon_reduction,
            optimization_strategy=OptimizationStrategy.PEAK_SHAVING,
            created_at=datetime.now()
        )
    
    async def _generate_load_balancing_plan(self, plan_id: str) -> EnergyOptimizationPlan:
        """生成负载均衡计划"""
        equipment_schedule = {}
        
        # 计算当前负载分布
        current_loads = {
            equipment_id: data_list[-1].consumption if data_list else 0
            for equipment_id, data_list in self.energy_data.items()
        }
        
        # 找出负载不均衡的设备
        avg_load = np.mean(list(current_loads.values()))
        
        for equipment_id, load in current_loads.items():
            if load > avg_load * 1.3:  # 负载过高
                equipment_schedule[equipment_id] = [
                    {"action": "reduce_load", "reduction": 0.15, "duration": "2h"}
                ]
            elif load < avg_load * 0.7:  # 负载过低
                equipment_schedule[equipment_id] = [
                    {"action": "increase_load", "increase": 0.1, "duration": "2h"}
                ]
        
        expected_savings = len(equipment_schedule) * 30
        carbon_reduction = expected_savings * self.optimization_config["carbon_factor"]
        
        return EnergyOptimizationPlan(
            plan_id=plan_id,
            equipment_schedule=equipment_schedule,
            energy_allocation={},
            expected_savings=expected_savings,
            carbon_reduction=carbon_reduction,
            optimization_strategy=OptimizationStrategy.LOAD_BALANCING,
            created_at=datetime.now()
        )
    
    async def _generate_renewable_priority_plan(self, plan_id: str) -> EnergyOptimizationPlan:
        """生成可再生能源优先计划"""
        equipment_schedule = {}
        energy_allocation = {}
        
        # 计算可再生能源总发电量
        total_renewable = sum(source.current_output for source in self.renewable_sources.values())
        
        # 优先使用可再生能源
        energy_allocation["renewable"] = total_renewable
        energy_allocation["grid"] = max(0, sum(
            data_list[-1].consumption if data_list else 0
            for data_list in self.energy_data.values()
        ) - total_renewable)
        
        # 调整设备运行时间以匹配可再生能源发电
        for equipment_id in self.energy_data.keys():
            equipment_schedule[equipment_id] = [
                {"action": "schedule_renewable", "priority": "high", "renewable_hours": "10:00-16:00"}
            ]
        
        expected_savings = total_renewable * 0.4  # 可再生能源成本更低
        carbon_reduction = total_renewable * self.optimization_config["carbon_factor"]
        
        return EnergyOptimizationPlan(
            plan_id=plan_id,
            equipment_schedule=equipment_schedule,
            energy_allocation=energy_allocation,
            expected_savings=expected_savings,
            carbon_reduction=carbon_reduction,
            optimization_strategy=OptimizationStrategy.RENEWABLE_PRIORITY,
            created_at=datetime.now()
        )
    
    async def _generate_cost_minimization_plan(self, plan_id: str) -> EnergyOptimizationPlan:
        """生成成本最小化计划"""
        equipment_schedule = {}
        
        # 根据电价安排设备运行
        for equipment_id in self.energy_data.keys():
            equipment_schedule[equipment_id] = [
                {"action": "schedule_valley", "valley_hours": "23:00-07:00", "load_shift": 0.3},
                {"action": "reduce_peak", "peak_hours": "09:00-12:00,18:00-22:00", "reduction": 0.25}
            ]
        
        expected_savings = len(self.energy_data) * 80  # 简化计算
        carbon_reduction = expected_savings * 0.3
        
        return EnergyOptimizationPlan(
            plan_id=plan_id,
            equipment_schedule=equipment_schedule,
            energy_allocation={},
            expected_savings=expected_savings,
            carbon_reduction=carbon_reduction,
            optimization_strategy=OptimizationStrategy.COST_MINIMIZATION,
            created_at=datetime.now()
        )
    
    async def _generate_carbon_reduction_plan(self, plan_id: str) -> EnergyOptimizationPlan:
        """生成碳减排计划"""
        equipment_schedule = {}
        
        # 优先减少高碳排放设备的使用
        for equipment_id, data_list in self.energy_data.items():
            if data_list:
                carbon_emission = data_list[-1].carbon_emission
                if carbon_emission > 30:  # 高碳排放设备
                    equipment_schedule[equipment_id] = [
                        {"action": "efficiency_upgrade", "efficiency_gain": 0.15},
                        {"action": "renewable_substitute", "substitute_ratio": 0.4}
                    ]
        
        expected_savings = len(equipment_schedule) * 60
        carbon_reduction = expected_savings * self.optimization_config["carbon_factor"] * 1.5
        
        return EnergyOptimizationPlan(
            plan_id=plan_id,
            equipment_schedule=equipment_schedule,
            energy_allocation={},
            expected_savings=expected_savings,
            carbon_reduction=carbon_reduction,
            optimization_strategy=OptimizationStrategy.CARBON_REDUCTION,
            created_at=datetime.now()
        )
    
    async def get_energy_status(self) -> Dict[str, Any]:
        """获取能耗状态"""
        try:
            # 计算总能耗
            total_consumption = sum(
                data_list[-1].consumption if data_list else 0
                for data_list in self.energy_data.values()
            )
            
            # 计算总成本
            total_cost = sum(
                data_list[-1].cost if data_list else 0
                for data_list in self.energy_data.values()
            )
            
            # 计算总碳排放
            total_carbon = sum(
                data_list[-1].carbon_emission if data_list else 0
                for data_list in self.energy_data.values()
            )
            
            # 可再生能源状态
            renewable_output = sum(source.current_output for source in self.renewable_sources.values())
            renewable_ratio = renewable_output / total_consumption if total_consumption > 0 else 0
            
            return {
                "status": "monitoring" if self.is_monitoring else "stopped",
                "total_consumption": round(total_consumption, 2),
                "total_cost": round(total_cost, 2),
                "total_carbon_emission": round(total_carbon, 2),
                "renewable_output": round(renewable_output, 2),
                "renewable_ratio": round(renewable_ratio * 100, 1),
                "equipment_count": len(self.energy_data),
                "optimization_plans": len(self.optimization_plans),
                "last_update": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取能耗状态失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """获取优化建议"""
        recommendations = []
        
        try:
            # 分析当前能耗情况
            current_hour = datetime.now().hour
            current_tariff = self._get_current_tariff(datetime.now())
            
            # 峰时建议
            if current_tariff.tariff_type == "峰时":
                recommendations.append({
                    "type": "peak_shaving",
                    "priority": "high",
                    "title": "削峰建议",
                    "description": "当前为峰时电价，建议降低非关键设备功率",
                    "potential_savings": "15-25%",
                    "action": "reduce_non_critical_load"
                })
            
            # 可再生能源建议
            renewable_output = sum(source.current_output for source in self.renewable_sources.values())
            if renewable_output > 50:
                recommendations.append({
                    "type": "renewable_priority",
                    "priority": "medium",
                    "title": "可再生能源利用",
                    "description": f"当前可再生能源发电量较高({renewable_output:.1f}kW)，建议增加生产负荷",
                    "potential_savings": "10-20%",
                    "action": "increase_production_during_renewable_peak"
                })
            
            # 负载均衡建议
            equipment_loads = [
                data_list[-1].consumption if data_list else 0
                for data_list in self.energy_data.values()
            ]
            load_variance = np.var(equipment_loads)
            if load_variance > 100:
                recommendations.append({
                    "type": "load_balancing",
                    "priority": "medium",
                    "title": "负载均衡优化",
                    "description": "设备负载分布不均，建议进行负载均衡",
                    "potential_savings": "8-15%",
                    "action": "balance_equipment_loads"
                })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"获取优化建议失败: {e}")
            return []


# 全局能耗优化服务实例
energy_optimization_service = None

async def get_energy_optimization_service(db: AsyncSession) -> EnergyOptimizationService:
    """获取能耗优化服务实例"""
    global energy_optimization_service
    if energy_optimization_service is None:
        energy_optimization_service = EnergyOptimizationService(db)
        await energy_optimization_service.initialize()
    return energy_optimization_service
