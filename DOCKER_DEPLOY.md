# 🐳 Smart Planning Docker 一键部署指南

## 📋 部署概述

Smart Planning 提供完整的 Docker 一键部署方案，支持 Windows、Linux、macOS 系统，包含以下服务：

- **MySQL 8.0** - 主数据库
- **Redis 7.0** - 缓存和会话管理
- **FastAPI Backend** - 后端API服务
- **Streamlit Frontend** - 前端应用
- **Ollama** - 本地LLM服务（可选）

## 🚀 快速开始

### 1. 环境要求

- **Docker 20.10+**
- **Docker Compose 2.0+**
- **4核CPU, 8GB内存** (推荐配置)
- **20GB 可用磁盘空间**

### 2. 一键部署

#### Windows 系统
```cmd
# 1. 克隆项目
git clone https://github.com/your-org/smart-planning.git
cd smart-planning

# 2. 一键启动
deploy.bat start

# 或者使用 Docker Compose
docker-compose up -d
```

#### Linux/macOS 系统
```bash
# 1. 克隆项目
git clone https://github.com/your-org/smart-planning.git
cd smart-planning

# 2. 设置执行权限
chmod +x deploy.sh

# 3. 一键启动
./deploy.sh start

# 或者使用 Docker Compose
docker-compose up -d
```

### 3. 访问系统

部署完成后，通过以下地址访问：

- **前端应用**: http://localhost:8501
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

**默认账户**:
- 管理员: `admin` / `admin123456`
- 计划员: `planner` / `admin123456`
- 用户: `user` / `admin123456`

## ⚙️ 配置说明

### 环境变量配置

系统会自动从 `.env.example` 复制配置文件，主要配置项：

```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DATABASE=smart_planning
MYSQL_USER=smart_planning
MYSQL_PASSWORD=smart_planning_password

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379

# LLM配置
DEFAULT_LLM_SERVICE=ollama
OLLAMA_BASE_URL=http://ollama:11434
```

### 端口配置

| 服务 | 内部端口 | 外部端口 | 说明 |
|------|---------|---------|------|
| MySQL | 3306 | 3306 | 数据库服务 |
| Redis | 6379 | 6379 | 缓存服务 |
| Backend | 8000 | 8000 | API服务 |
| Frontend | 8501 | 8501 | Web应用 |
| Ollama | 11434 | 11434 | LLM服务 |

## 🛠️ 管理命令

### Windows (deploy.bat)

```cmd
# 启动服务
deploy.bat start

# 停止服务
deploy.bat stop

# 重启服务
deploy.bat restart

# 查看状态
deploy.bat status

# 查看日志
deploy.bat logs

# 备份数据库
deploy.bat backup

# 清理资源
deploy.bat cleanup

# 显示帮助
deploy.bat help
```

### Linux/macOS (deploy.sh)

```bash
# 启动服务
./deploy.sh start

# 停止服务
./deploy.sh stop

# 重启服务
./deploy.sh restart

# 查看状态
./deploy.sh status

# 查看日志
./deploy.sh logs

# 备份数据库
./deploy.sh backup

# 恢复数据库
./deploy.sh restore backups/backup_file.sql

# 更新系统
./deploy.sh update

# 清理资源
./deploy.sh cleanup
```

### Docker Compose 原生命令

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重新构建镜像
docker-compose build

# 拉取最新镜像
docker-compose pull
```

## 📊 服务监控

### 健康检查

所有服务都配置了健康检查：

```bash
# 检查所有服务健康状态
docker-compose ps

# 查看特定服务日志
docker-compose logs backend
docker-compose logs frontend
docker-compose logs mysql
```

### 资源监控

```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
docker system df

# 查看网络状态
docker network ls
```

## 🔧 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :8501
netstat -tulpn | grep :8000
netstat -tulpn | grep :3306

# 修改 docker-compose.yml 中的端口映射
ports:
  - "8502:8501"  # 修改外部端口
```

#### 2. 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose logs mysql

# 重启MySQL服务
docker-compose restart mysql

# 检查数据库连接
docker-compose exec mysql mysql -u smart_planning -p
```

#### 3. 前端无法访问后端
```bash
# 检查网络连接
docker-compose exec frontend ping backend

# 检查环境变量
docker-compose exec frontend env | grep API_BASE_URL
```

#### 4. 内存不足
```bash
# 清理未使用的镜像
docker image prune -f

# 清理未使用的容器
docker container prune -f

# 清理未使用的卷
docker volume prune -f
```

### 日志分析

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend

# 实时跟踪日志
docker-compose logs -f frontend

# 查看最近的日志
docker-compose logs --tail=100 mysql
```

## 💾 数据管理

### 数据备份

```bash
# 自动备份（使用脚本）
./deploy.sh backup

# 手动备份
docker-compose exec mysql mysqldump -u smart_aps -psmart_aps_password smart_aps > backup.sql
```

### 数据恢复

```bash
# 使用脚本恢复
./deploy.sh restore backup.sql

# 手动恢复
docker-compose exec -T mysql mysql -u smart_aps -psmart_aps_password smart_aps < backup.sql
```

### 数据持久化

数据通过 Docker 卷持久化存储：

- `mysql_data` - MySQL数据
- `redis_data` - Redis数据
- `uploads_data` - 上传文件
- `logs_data` - 日志文件
- `ollama_data` - Ollama模型

## 🔄 更新升级

### 系统更新

```bash
# 使用脚本更新（推荐）
./deploy.sh update

# 手动更新
git pull
docker-compose build
docker-compose up -d
```

### 镜像更新

```bash
# 拉取最新镜像
docker-compose pull

# 重新构建自定义镜像
docker-compose build --no-cache

# 重启服务
docker-compose up -d
```

## 🚀 生产环境部署

### 安全配置

1. **修改默认密码**
```bash
# 编辑 .env 文件
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_PASSWORD=your_secure_password
SECRET_KEY=your_super_secret_key
```

2. **启用HTTPS**
```yaml
# 在 docker-compose.yml 中添加反向代理
nginx:
  image: nginx:alpine
  ports:
    - "443:443"
  volumes:
    - ./nginx.conf:/etc/nginx/nginx.conf
    - ./ssl:/etc/nginx/ssl
```

3. **限制网络访问**
```yaml
# 修改端口映射，只绑定本地
ports:
  - "127.0.0.1:8501:8501"
```

### 性能优化

1. **资源限制**
```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 4G
    reservations:
      cpus: '1.0'
      memory: 2G
```

2. **数据库优化**
```yaml
command: >
  --default-authentication-plugin=mysql_native_password
  --innodb-buffer-pool-size=2G
  --max-connections=200
```

## 📞 技术支持

如果遇到部署问题，请：

1. 查看日志: `docker-compose logs`
2. 检查系统资源: `docker stats`
3. 验证配置文件: `.env` 和 `docker-compose.yml`
4. 提交Issue: 包含错误日志和系统信息

---

**🎉 恭喜！Smart Planning 系统已成功部署，开始您的智能生产管理之旅！**
