"""
智能对话增强引擎
提供上下文记忆、专业术语、多轮对话优化等功能
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import re
import os
from collections import defaultdict, deque

@dataclass
class ConversationContext:
    """对话上下文"""
    user_id: str
    conversation_id: str
    session_start: datetime
    last_activity: datetime
    user_preferences: Dict[str, Any]
    conversation_history: List[Dict[str, Any]]
    current_topic: Optional[str] = None
    context_memory: Dict[str, Any] = None

    def __post_init__(self):
        if self.context_memory is None:
            self.context_memory = {}

@dataclass
class ConversationResponse:
    """对话响应"""
    response_text: str
    confidence: float
    context_used: List[str]
    suggestions: List[str]
    follow_up_questions: List[str]
    extracted_entities: Dict[str, Any]
    action_recommendations: List[Dict[str, Any]]

class ManufacturingTerminologyEngine:
    """制造业专业术语引擎"""

    def __init__(self):
        self.terminology_db = self._initialize_terminology()
        self.abbreviations = self._initialize_abbreviations()
        self.context_patterns = self._initialize_context_patterns()

    def _initialize_terminology(self) -> Dict[str, Dict[str, Any]]:
        """初始化专业术语数据库"""
        return {
            # 生产管理术语
            'MES': {
                'full_name': '制造执行系统',
                'english': 'Manufacturing Execution System',
                'category': 'system',
                'description': '位于上层计划管理系统与底层工业控制之间的面向车间层的管理信息系统',
                'related_terms': ['ERP', 'WMS', 'APS'],
                'usage_examples': ['MES系统集成', 'MES数据采集', 'MES报表']
            },
            'APS': {
                'full_name': '高级计划排程',
                'english': 'Advanced Planning and Scheduling',
                'category': 'system',
                'description': '通过优化算法进行生产计划和排程的系统',
                'related_terms': ['MES', 'ERP', '排程优化'],
                'usage_examples': ['APS排程', 'APS优化', 'APS算法']
            },
            'OEE': {
                'full_name': '设备综合效率',
                'english': 'Overall Equipment Effectiveness',
                'category': 'metric',
                'description': '衡量设备利用率的关键指标，包括可用率、性能率和质量率',
                'related_terms': ['可用率', '性能率', '质量率', 'MTBF', 'MTTR'],
                'usage_examples': ['OEE分析', 'OEE提升', 'OEE监控']
            },
            'FIFO': {
                'full_name': '先进先出',
                'english': 'First In First Out',
                'category': 'method',
                'description': '库存管理和生产排程中的重要原则',
                'related_terms': ['LIFO', '库存管理', '排程规则'],
                'usage_examples': ['FIFO原则', 'FIFO排程', 'FIFO库存']
            },
            'JIT': {
                'full_name': '准时制生产',
                'english': 'Just In Time',
                'category': 'method',
                'description': '以需求为导向的生产方式，减少库存和浪费',
                'related_terms': ['精益生产', '看板', '拉动式生产'],
                'usage_examples': ['JIT生产', 'JIT供应', 'JIT管理']
            },
            'TPM': {
                'full_name': '全面生产维护',
                'english': 'Total Productive Maintenance',
                'category': 'method',
                'description': '以提高设备综合效率为目标的生产维护方式',
                'related_terms': ['预防性维护', '设备管理', 'OEE'],
                'usage_examples': ['TPM实施', 'TPM活动', 'TPM改善']
            },
            'SPC': {
                'full_name': '统计过程控制',
                'english': 'Statistical Process Control',
                'category': 'method',
                'description': '运用统计方法监控和控制生产过程的质量管理方法',
                'related_terms': ['控制图', 'Cpk', '过程能力'],
                'usage_examples': ['SPC分析', 'SPC控制图', 'SPC监控']
            }
        }

    def _initialize_abbreviations(self) -> Dict[str, str]:
        """初始化缩写词典"""
        return {
            'ERP': '企业资源计划',
            'WMS': '仓库管理系统',
            'CRM': '客户关系管理',
            'PLM': '产品生命周期管理',
            'CAD': '计算机辅助设计',
            'CAM': '计算机辅助制造',
            'CNC': '数控机床',
            'PLC': '可编程逻辑控制器',
            'SCADA': '数据采集与监视控制系统',
            'HMI': '人机界面',
            'RFID': '射频识别',
            'IoT': '物联网',
            'AI': '人工智能',
            'ML': '机器学习',
            'DL': '深度学习'
        }

    def _initialize_context_patterns(self) -> Dict[str, List[str]]:
        """初始化上下文模式"""
        return {
            'production_planning': [
                '生产计划', '排程', '工单', '订单', '产能', '交期', '优先级'
            ],
            'quality_control': [
                '质量', '检验', '不良率', '合格率', '缺陷', '质量控制', 'SPC'
            ],
            'equipment_management': [
                '设备', '机器', '维护', '保养', '故障', 'OEE', 'TPM'
            ],
            'inventory_management': [
                '库存', '仓库', '物料', '原材料', '成品', 'FIFO', 'JIT'
            ],
            'data_analysis': [
                '数据', '分析', '报表', '统计', '趋势', '预测', '算法'
            ]
        }

    def expand_terminology(self, text: str) -> str:
        """扩展专业术语"""
        expanded_text = text

        # 扩展缩写
        for abbr, full_name in self.abbreviations.items():
            pattern = r'\b' + re.escape(abbr) + r'\b'
            if re.search(pattern, text, re.IGNORECASE):
                expanded_text = re.sub(
                    pattern,
                    f"{abbr}({full_name})",
                    expanded_text,
                    flags=re.IGNORECASE
                )

        return expanded_text

    def get_term_explanation(self, term: str) -> Optional[Dict[str, Any]]:
        """获取术语解释"""
        term_upper = term.upper()

        # 检查专业术语数据库
        if term_upper in self.terminology_db:
            return self.terminology_db[term_upper]

        # 检查缩写词典
        if term_upper in self.abbreviations:
            return {
                'full_name': self.abbreviations[term_upper],
                'category': 'abbreviation',
                'description': f"{term_upper}是{self.abbreviations[term_upper]}的缩写"
            }

        return None

    def detect_context_topic(self, text: str) -> Optional[str]:
        """检测对话主题"""
        text_lower = text.lower()
        topic_scores = defaultdict(int)

        for topic, keywords in self.context_patterns.items():
            for keyword in keywords:
                if keyword in text_lower:
                    topic_scores[topic] += 1

        if topic_scores:
            return max(topic_scores, key=topic_scores.get)

        return None

class ConversationMemoryManager:
    """对话记忆管理器"""

    def __init__(self, max_memory_size: int = 100):
        self.max_memory_size = max_memory_size
        self.user_contexts = {}
        self.global_patterns = defaultdict(int)

    def get_or_create_context(self, user_id: str, conversation_id: str) -> ConversationContext:
        """获取或创建对话上下文"""
        context_key = f"{user_id}_{conversation_id}"

        if context_key not in self.user_contexts:
            self.user_contexts[context_key] = ConversationContext(
                user_id=user_id,
                conversation_id=conversation_id,
                session_start=datetime.now(),
                last_activity=datetime.now(),
                user_preferences={},
                conversation_history=[]
            )

        return self.user_contexts[context_key]

    def update_context(self, context: ConversationContext, user_message: str,
                      ai_response: str, extracted_entities: Dict[str, Any]):
        """更新对话上下文"""
        context.last_activity = datetime.now()

        # 添加到对话历史
        context.conversation_history.append({
            'timestamp': datetime.now().isoformat(),
            'user_message': user_message,
            'ai_response': ai_response,
            'entities': extracted_entities
        })

        # 限制历史记录大小
        if len(context.conversation_history) > self.max_memory_size:
            context.conversation_history = context.conversation_history[-self.max_memory_size:]

        # 更新上下文记忆
        self._update_context_memory(context, user_message, extracted_entities)

        # 更新全局模式
        self._update_global_patterns(user_message, extracted_entities)

    def _update_context_memory(self, context: ConversationContext,
                              message: str, entities: Dict[str, Any]):
        """更新上下文记忆"""
        # 记住用户提到的关键信息
        for entity_type, entity_value in entities.items():
            if entity_type in ['product_id', 'order_id', 'equipment_id']:
                context.context_memory[entity_type] = entity_value

        # 记住用户偏好
        if '喜欢' in message or '偏好' in message:
            # 简单的偏好提取逻辑
            context.user_preferences['last_preference'] = message

    def _update_global_patterns(self, message: str, entities: Dict[str, Any]):
        """更新全局模式"""
        # 记录常见的查询模式
        for entity_type in entities.keys():
            self.global_patterns[entity_type] += 1

    def get_relevant_context(self, context: ConversationContext,
                           current_message: str) -> List[str]:
        """获取相关上下文"""
        relevant_context = []

        # 从最近的对话历史中查找相关信息
        for history_item in context.conversation_history[-5:]:  # 最近5条
            if any(keyword in current_message.lower()
                   for keyword in history_item['user_message'].lower().split()):
                relevant_context.append(history_item['user_message'])

        # 添加上下文记忆中的相关信息
        for key, value in context.context_memory.items():
            if key in current_message.lower():
                relevant_context.append(f"之前提到的{key}: {value}")

        return relevant_context

class EntityExtractor:
    """实体提取器"""

    def __init__(self):
        self.entity_patterns = self._initialize_entity_patterns()

    def _initialize_entity_patterns(self) -> Dict[str, List[str]]:
        """初始化实体识别模式"""
        return {
            'product_id': [
                r'产品[编号|ID|代码][:：]?\s*([A-Z0-9]+)',
                r'商品[编号|ID|代码][:：]?\s*([A-Z0-9]+)',
                r'PROD[-_]?(\d+)',
                r'P\d{3,}'
            ],
            'order_id': [
                r'订单[编号|号|ID][:：]?\s*([A-Z0-9]+)',
                r'工单[编号|号|ID][:：]?\s*([A-Z0-9]+)',
                r'ORDER[-_]?(\d+)',
                r'WO[-_]?(\d+)'
            ],
            'equipment_id': [
                r'设备[编号|号|ID][:：]?\s*([A-Z0-9]+)',
                r'机器[编号|号|ID][:：]?\s*([A-Z0-9]+)',
                r'L\d{2}',  # 生产线编号
                r'Tank\d{2}'  # 罐体编号
            ],
            'date': [
                r'\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?',
                r'\d{1,2}[-/月]\d{1,2}[-/日]',
                r'今天|明天|昨天|本周|下周|本月|下月'
            ],
            'quantity': [
                r'(\d+(?:\.\d+)?)\s*[个|件|台|批|吨|公斤|kg]',
                r'数量[:：]?\s*(\d+(?:\.\d+)?)',
                r'(\d+(?:\.\d+)?)\s*units?'
            ],
            'percentage': [
                r'(\d+(?:\.\d+)?)%',
                r'百分之(\d+(?:\.\d+)?)',
                r'(\d+(?:\.\d+)?)\s*percent'
            ]
        }

    def extract_entities(self, text: str) -> Dict[str, Any]:
        """提取实体"""
        entities = {}

        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    if entity_type not in entities:
                        entities[entity_type] = []
                    entities[entity_type].extend(matches)

        # 去重并转换格式
        for entity_type, values in entities.items():
            entities[entity_type] = list(set(values))

        return entities

class EnhancedConversationEngine:
    """增强对话引擎"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.terminology_engine = ManufacturingTerminologyEngine()
        self.memory_manager = ConversationMemoryManager()
        self.entity_extractor = EntityExtractor()
        self.response_templates = self._initialize_response_templates()

    def _initialize_response_templates(self) -> Dict[str, List[str]]:
        """初始化响应模板"""
        return {
            'greeting': [
                "您好！我是Smart Planning智能助手，专门帮助您处理生产计划和制造管理相关问题。",
                "欢迎使用Smart Planning系统！我可以帮您分析数据、优化生产计划、解答专业问题。",
                "您好！我是您的智能制造顾问，有什么可以帮助您的吗？"
            ],
            'data_analysis': [
                "我来帮您分析这些数据。根据您提供的信息，我发现了以下模式：",
                "基于数据分析，我为您总结了以下关键发现：",
                "让我为您深入分析这些数据，以下是主要洞察："
            ],
            'production_planning': [
                "关于生产计划优化，我建议从以下几个方面考虑：",
                "基于当前的生产状况，我为您制定了以下优化方案：",
                "针对您的生产计划需求，我推荐以下策略："
            ],
            'quality_control': [
                "关于质量控制，我注意到以下几个关键点：",
                "基于质量数据分析，我建议采取以下措施：",
                "为了提升产品质量，我推荐以下改进方案："
            ],
            'equipment_management': [
                "关于设备管理，我为您分析了以下情况：",
                "基于设备运行数据，我发现了以下优化机会：",
                "针对设备效率提升，我建议考虑以下方案："
            ]
        }

    def process_conversation(self, user_id: str, conversation_id: str,
                           message: str, context_data: Dict[str, Any] = None) -> ConversationResponse:
        """处理对话"""
        try:
            # 获取对话上下文
            context = self.memory_manager.get_or_create_context(user_id, conversation_id)

            # 提取实体
            entities = self.entity_extractor.extract_entities(message)

            # 检测主题
            topic = self.terminology_engine.detect_context_topic(message)
            if topic:
                context.current_topic = topic

            # 获取相关上下文
            relevant_context = self.memory_manager.get_relevant_context(context, message)

            # 扩展专业术语
            expanded_message = self.terminology_engine.expand_terminology(message)

            # 生成响应
            response_text = self._generate_response(message, context, entities, topic, context_data)

            # 生成建议和后续问题
            suggestions = self._generate_suggestions(topic, entities, context)
            follow_up_questions = self._generate_follow_up_questions(topic, entities)

            # 生成行动建议
            action_recommendations = self._generate_action_recommendations(topic, entities, context_data)

            # 计算置信度
            confidence = self._calculate_confidence(message, entities, topic)

            # 创建响应
            response = ConversationResponse(
                response_text=response_text,
                confidence=confidence,
                context_used=relevant_context,
                suggestions=suggestions,
                follow_up_questions=follow_up_questions,
                extracted_entities=entities,
                action_recommendations=action_recommendations
            )

            # 更新上下文
            self.memory_manager.update_context(context, message, response_text, entities)

            return response

        except Exception as e:
            self.logger.error(f"对话处理失败: {str(e)}")
            return ConversationResponse(
                response_text="抱歉，我在处理您的问题时遇到了困难。请您重新描述一下问题，我会尽力帮助您。",
                confidence=0.0,
                context_used=[],
                suggestions=["请重新描述问题", "检查输入格式", "联系技术支持"],
                follow_up_questions=[],
                extracted_entities={},
                action_recommendations=[]
            )

    def _generate_response(self, message: str, context: ConversationContext,
                          entities: Dict[str, Any], topic: Optional[str],
                          context_data: Dict[str, Any] = None) -> str:
        """生成响应文本"""
        # 检查是否是问候语
        if any(greeting in message.lower() for greeting in ['你好', 'hello', '您好', '嗨']):
            return self.response_templates['greeting'][0]

        # 根据主题选择响应模板
        if topic and topic in self.response_templates:
            base_response = self.response_templates[topic][0]
        else:
            base_response = "我理解您的问题，让我为您分析一下："

        # 添加具体的分析内容
        analysis_content = self._generate_analysis_content(message, entities, topic, context_data)

        return f"{base_response}\n\n{analysis_content}"

    def _generate_analysis_content(self, message: str, entities: Dict[str, Any],
                                  topic: Optional[str], context_data: Dict[str, Any] = None) -> str:
        """生成分析内容"""
        content_parts = []

        # 基于实体生成内容
        if 'product_id' in entities:
            content_parts.append(f"• 关于产品 {', '.join(entities['product_id'])}，我建议关注其生产计划和质量指标。")

        if 'order_id' in entities:
            content_parts.append(f"• 订单 {', '.join(entities['order_id'])} 的处理需要考虑交期和优先级。")

        if 'equipment_id' in entities:
            content_parts.append(f"• 设备 {', '.join(entities['equipment_id'])} 的运行状态和效率是关键因素。")

        if 'percentage' in entities:
            content_parts.append(f"• 您提到的 {', '.join(entities['percentage'])}% 指标需要进一步分析趋势。")

        # 基于主题生成内容
        if topic == 'production_planning':
            content_parts.append("• 建议优化生产排程，平衡产能利用率和交期要求。")
        elif topic == 'quality_control':
            content_parts.append("• 建议加强过程控制，实施SPC统计过程控制。")
        elif topic == 'equipment_management':
            content_parts.append("• 建议实施TPM全面生产维护，提升设备OEE。")

        # 如果没有具体内容，提供通用建议
        if not content_parts:
            content_parts.append("• 建议您提供更多具体信息，以便我为您提供更精准的分析和建议。")

        return "\n".join(content_parts)

    def _generate_suggestions(self, topic: Optional[str], entities: Dict[str, Any],
                            context: ConversationContext) -> List[str]:
        """生成建议"""
        suggestions = []

        if topic == 'production_planning':
            suggestions.extend([
                "查看生产计划优化报告",
                "分析设备利用率",
                "检查订单交期状况"
            ])
        elif topic == 'quality_control':
            suggestions.extend([
                "查看质量控制图表",
                "分析不良率趋势",
                "检查工艺参数"
            ])
        elif topic == 'equipment_management':
            suggestions.extend([
                "查看设备OEE报告",
                "分析故障模式",
                "制定维护计划"
            ])
        else:
            suggestions.extend([
                "上传相关数据文件",
                "查看系统报告",
                "获取专业建议"
            ])

        return suggestions[:3]  # 限制建议数量

    def _generate_follow_up_questions(self, topic: Optional[str], entities: Dict[str, Any]) -> List[str]:
        """生成后续问题"""
        questions = []

        if 'product_id' in entities:
            questions.append("您想了解这个产品的哪些具体信息？")

        if 'order_id' in entities:
            questions.append("这个订单的当前状态如何？")

        if topic == 'production_planning':
            questions.append("您希望优化哪个时间段的生产计划？")
        elif topic == 'quality_control':
            questions.append("您关注的质量指标是什么？")

        if not questions:
            questions.append("还有其他需要我帮助分析的问题吗？")

        return questions[:2]  # 限制问题数量

    def _generate_action_recommendations(self, topic: Optional[str], entities: Dict[str, Any],
                                       context_data: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """生成行动建议"""
        recommendations = []

        if topic == 'production_planning':
            recommendations.append({
                'action': 'optimize_schedule',
                'title': '优化生产排程',
                'description': '使用APS算法优化当前生产计划',
                'priority': 'high'
            })

        if topic == 'quality_control':
            recommendations.append({
                'action': 'quality_analysis',
                'title': '质量数据分析',
                'description': '分析质量趋势并识别改进机会',
                'priority': 'medium'
            })

        if 'equipment_id' in entities:
            recommendations.append({
                'action': 'equipment_check',
                'title': '设备状态检查',
                'description': '检查设备运行状态和维护需求',
                'priority': 'medium'
            })

        return recommendations

    def _calculate_confidence(self, message: str, entities: Dict[str, Any],
                            topic: Optional[str]) -> float:
        """计算响应置信度"""
        confidence = 0.5  # 基础置信度

        # 实体识别增加置信度
        if entities:
            confidence += 0.2 * min(len(entities), 3) / 3

        # 主题识别增加置信度
        if topic:
            confidence += 0.2

        # 消息长度和复杂度
        if len(message.split()) > 5:
            confidence += 0.1

        return min(confidence, 1.0)

    def get_conversation_summary(self, user_id: str, conversation_id: str) -> Dict[str, Any]:
        """获取对话摘要"""
        context_key = f"{user_id}_{conversation_id}"

        if context_key not in self.memory_manager.user_contexts:
            return {"error": "对话不存在"}

        context = self.memory_manager.user_contexts[context_key]

        return {
            "session_duration": str(datetime.now() - context.session_start),
            "message_count": len(context.conversation_history),
            "current_topic": context.current_topic,
            "user_preferences": context.user_preferences,
            "key_entities": list(context.context_memory.keys())
        }
