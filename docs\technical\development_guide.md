# Smart Planning - 开发实施指南

## 开发团队配置

### 团队组成建议（小规模团队优化）

| 角色 | 人数 | 主要技能要求 | 职责范围 |
|------|-----|-------------|---------|
| 项目经理/技术负责人 | 1 | 项目管理、全栈开发、架构设计 | 项目规划、技术决策、团队协调 |
| 后端开发工程师 | 1-2 | Python、FastAPI、数据库、算法 | API开发、业务逻辑、算法实现 |
| 前端开发工程师 | 1 | Python、Streamlit、数据可视化 | UI开发、数据可视化、用户体验 |
| 测试/运维工程师 | 1 | 测试、Docker、部署 | 质量保证、部署运维、系统监控 |

**总团队规模：4-5人**（适合小规模项目和预算）

### 技能矩阵

#### 必备技能
- **Python 3.9+**：后端开发、算法实现、前端开发
- **FastAPI**：API开发框架
- **Streamlit**：快速前端开发
- **MySQL/Redis**：数据库管理
- **Git**：版本控制
- **Docker**：容器化部署

#### 加分技能
- **优化算法**：MILP、启发式算法
- **机器学习**：scikit-learn、强化学习
- **LLM应用**：Ollama、Azure OpenAI、提示词工程
- **数据可视化**：Plotly、Altair、交互式图表
- **文件处理**：邮件解析、Excel处理、数据提取

## 开发环境搭建

### 本地开发环境

#### 1. 基础环境要求
```bash
# 操作系统要求
- Windows 10+ / macOS 11+ / Ubuntu 20.04+
- Python 3.9+
- Node.js 16+
- Git 2.30+
- Docker 20.10+

# 推荐IDE
- VS Code + Python扩展
- PyCharm Professional
- WebStorm (前端开发)
```

#### 2. Python环境配置
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 安装开发工具
pip install black flake8 pytest pytest-cov pre-commit
```

#### 3. 数据库环境
```bash
# 使用Docker快速搭建
docker-compose up -d mysql redis

# 或手动安装
# MySQL 8.0+
# Redis 7.0+

# 初始化数据库
python scripts/init_database.py
```

#### 4. Streamlit前端环境
```bash
# 安装Streamlit和相关依赖
pip install streamlit plotly altair streamlit-aggrid

# 安装文件处理库
pip install python-multipart email-parser openpyxl

# 安装Ollama客户端（可选）
pip install ollama

# 启动Streamlit应用
streamlit run frontend/main.py

# 访问地址：http://localhost:8501
```

### 开发工具配置

#### 1. VS Code配置
```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "88"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

#### 2. Git Hooks配置
```bash
# 安装pre-commit
pre-commit install

# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3.9
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.15.0
    hooks:
      - id: eslint
        files: \.(js|jsx|ts|tsx)$
```

## 开发流程与规范

### 1. 分支管理策略

#### Git Flow模型
```
main (生产分支)
├── develop (开发分支)
│   ├── feature/user-management (功能分支)
│   ├── feature/planning-engine (功能分支)
│   └── feature/llm-integration (功能分支)
├── release/v1.0.0 (发布分支)
└── hotfix/critical-bug-fix (热修复分支)
```

#### 分支命名规范
- **功能分支**：`feature/功能名称`
- **修复分支**：`bugfix/问题描述`
- **热修复分支**：`hotfix/紧急修复`
- **发布分支**：`release/版本号`

### 2. 代码提交规范

#### Commit Message格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- **feat**：新功能
- **fix**：修复bug
- **docs**：文档更新
- **style**：代码格式调整
- **refactor**：代码重构
- **test**：测试相关
- **chore**：构建过程或辅助工具变动

#### 示例
```
feat(planning): 添加MILP优化算法

- 实现基于PuLP的线性规划求解器
- 支持多目标优化配置
- 添加约束条件验证机制

Closes #123
```

### 3. 代码审查流程

#### Pull Request模板
```markdown
## 变更描述
简要描述本次变更的内容和目的

## 变更类型
- [ ] 新功能
- [ ] Bug修复
- [ ] 文档更新
- [ ] 性能优化
- [ ] 代码重构

## 测试情况
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成

## 检查清单
- [ ] 代码符合规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 无安全风险

## 相关Issue
Closes #issue_number
```

#### 审查要点
1. **代码质量**：可读性、可维护性、性能
2. **安全性**：输入验证、权限检查、数据保护
3. **测试覆盖**：单元测试、集成测试
4. **文档完整性**：API文档、代码注释
5. **向后兼容性**：API变更影响

## 功能开发优先级

### 第一阶段：核心功能 (8周)

#### 里程碑1：基础架构 (2周)
**目标**：搭建项目基础架构和开发环境

**任务清单**：
- [ ] 项目结构搭建
- [ ] 数据库设计与实现
- [ ] API框架搭建
- [ ] 认证授权模块
- [ ] 基础UI框架
- [ ] CI/CD流水线

**验收标准**：
- 开发环境可正常运行
- 基础API接口可访问
- 用户登录功能正常
- 自动化测试流水线运行

#### 里程碑2：数据集成 (2周)
**目标**：实现多数据源集成能力

**任务清单**：
- [ ] 数据源适配器框架
- [ ] SQL数据源适配器
- [ ] Excel数据源适配器
- [ ] 数据验证框架
- [ ] ETL工作流引擎

**验收标准**：
- 成功连接至少2种数据源
- 数据验证规则正常工作
- 数据转换流程无误
- 错误处理机制完善

#### 里程碑3：规划引擎 (3周)
**目标**：实现核心生产规划功能

**任务清单**：
- [ ] 需求计算引擎
- [ ] 产能计算引擎
- [ ] MILP优化算法
- [ ] 约束条件管理
- [ ] 计划生成接口

**验收标准**：
- 能生成基础生产计划
- 优化算法运行正常
- 约束条件正确应用
- 计划结果可视化展示

#### 里程碑4：基础UI (1周)
**目标**：实现核心用户界面

**任务清单**：
- [ ] 生产计划看板
- [ ] 甘特图组件
- [ ] 数据表格组件
- [ ] 基础交互功能

**验收标准**：
- 计划数据正确显示
- 用户交互响应正常
- 界面布局合理
- 移动端基本适配

### 第二阶段：智能化功能 (6周)

#### 里程碑5：LLM集成 (2周)
**任务清单**：
- [ ] LLM服务连接器
- [ ] 提示词模板管理
- [ ] 自然语言查询接口
- [ ] 结果验证机制

#### 里程碑6：学习机制 (2周)
**任务清单**：
- [ ] 反馈收集系统
- [ ] 参数自动优化
- [ ] 效果评估工具
- [ ] 学习报告生成

#### 里程碑7：高级可视化 (1周)
**任务清单**：
- [ ] 高级图表组件
- [ ] 数据钻取功能
- [ ] 自定义仪表盘
- [ ] 数据导出功能

#### 里程碑8：插件系统 (1周)
**任务清单**：
- [ ] 插件框架实现
- [ ] 插件管理界面
- [ ] 示例插件开发
- [ ] 插件文档编写

### 第三阶段：优化完善 (4周)

#### 里程碑9：性能优化 (2周)
**任务清单**：
- [ ] 数据库查询优化
- [ ] 缓存策略实施
- [ ] 前端性能优化
- [ ] 算法性能调优

#### 里程碑10：系统完善 (2周)
**任务清单**：
- [ ] 安全加固
- [ ] 监控告警
- [ ] 文档完善
- [ ] 用户培训材料

## 质量保证策略

### 1. 测试策略

#### 测试金字塔
```
        E2E Tests (10%)
      ┌─────────────────┐
     Integration Tests (20%)
   ┌─────────────────────────┐
  Unit Tests (70%)
┌─────────────────────────────┐
```

#### 单元测试
```python
# 测试示例
import pytest
from app.services.planning import PlanningEngine

class TestPlanningEngine:
    def setup_method(self):
        self.engine = PlanningEngine()

    def test_calculate_demand(self):
        """测试需求计算"""
        orders = [{"product_id": 1, "quantity": 100}]
        inventory = {"product_id": 1, "quantity": 20}

        demand = self.engine.calculate_demand(orders, inventory)

        assert demand["product_id"] == 1
        assert demand["net_demand"] == 80

    @pytest.mark.asyncio
    async def test_generate_plan(self):
        """测试计划生成"""
        config = {
            "year": 2024,
            "month": 2,
            "optimization_objectives": {"minimize_makespan": 1.0}
        }

        result = await self.engine.generate_plan(config)

        assert result.status == "success"
        assert result.plan_id is not None
        assert result.objective_value > 0
```

#### 集成测试
```python
# API集成测试示例
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_create_production_plan():
    async with AsyncClient(app=app, base_url="http://test") as client:
        # 登录获取token
        login_response = await client.post("/api/v1/auth/login", json={
            "username": "test_user",
            "password": "test_password"
        })
        token = login_response.json()["data"]["access_token"]

        # 创建生产计划
        headers = {"Authorization": f"Bearer {token}"}
        plan_data = {
            "year": 2024,
            "month": 2,
            "planning_horizon_days": 30
        }

        response = await client.post(
            "/api/v1/production-plans/generate",
            json=plan_data,
            headers=headers
        )

        assert response.status_code == 200
        assert response.json()["success"] is True
```

### 2. 代码质量控制

#### 静态代码分析
```bash
# 代码格式化
black --line-length 88 app/

# 代码检查
flake8 app/ --max-line-length=88

# 类型检查
mypy app/

# 安全检查
bandit -r app/

# 复杂度检查
radon cc app/ -a
```

#### 测试覆盖率
```bash
# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html --cov-report=term

# 覆盖率要求
# 总体覆盖率 >= 80%
# 核心业务逻辑 >= 90%
# 新增代码 >= 85%
```

### 3. 性能基准测试

#### API性能测试
```python
# 使用locust进行负载测试
from locust import HttpUser, task, between

class APSUser(HttpUser):
    wait_time = between(1, 3)

    def on_start(self):
        # 登录获取token
        response = self.client.post("/api/v1/auth/login", json={
            "username": "test_user",
            "password": "test_password"
        })
        self.token = response.json()["data"]["access_token"]
        self.headers = {"Authorization": f"Bearer {self.token}"}

    @task(3)
    def get_production_plans(self):
        self.client.get("/api/v1/production-plans", headers=self.headers)

    @task(1)
    def generate_plan(self):
        plan_data = {
            "year": 2024,
            "month": 2,
            "planning_horizon_days": 30
        }
        self.client.post(
            "/api/v1/production-plans/generate",
            json=plan_data,
            headers=self.headers
        )
```

#### 性能基准
| 指标 | 目标值 | 测试方法 |
|------|-------|---------|
| API响应时间 | < 1秒 | 负载测试 |
| 计划生成时间 | < 30分钟 | 算法性能测试 |
| 并发用户数 | 30用户 | 压力测试 |
| 数据库查询 | < 100ms | 查询性能测试 |
| 内存使用 | < 2GB | 资源监控 |

## 部署与运维

### 1. 容器化部署

#### Docker配置
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://user:password@mysql:3306/smart_planning
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: smart_planning
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"

  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 2. CI/CD流水线

#### GitHub Actions配置
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: testpassword
          MYSQL_DATABASE: test_db
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:7.0
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Run linting
      run: |
        flake8 app/
        black --check app/

    - name: Run tests
      run: |
        pytest --cov=app --cov-report=xml

    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        # 部署脚本
        echo "Deploying to production..."
```

这个开发实施指南涵盖了团队配置、环境搭建、开发流程、质量保证和部署运维等关键方面。
