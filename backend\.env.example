# Smart Planning Environment Configuration

# 基础配置
PROJECT_NAME=Smart Planning
VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# API配置
API_V1_STR=/api/v1
ALLOWED_HOSTS=["*"]

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=smart_planning
DB_PASSWORD=smart_planning_password
DB_NAME=smart_planning
# 或者直接使用完整的数据库URL
# DATABASE_URL=mysql+aiomysql://smart_planning:smart_planning_password@localhost:3306/smart_planning?charset=utf8mb4

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
# 或者直接使用完整的Redis URL
# REDIS_URL=redis://localhost:6379/0

# JWT配置
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# 文件上传配置
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=209715200  # 200MB in bytes

# LLM配置
# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2
OLLAMA_TIMEOUT=60

# Azure OpenAI配置
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-api-key
AZURE_OPENAI_API_VERSION=2023-12-01-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4

# 默认使用的LLM服务 (ollama 或 azure)
DEFAULT_LLM_SERVICE=ollama

# 缓存配置
CACHE_TTL=300  # 5分钟
CACHE_MAX_SIZE=1000

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/smart_planning.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# 安全配置
ENCRYPTION_KEY=  # 留空将自动生成
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 算法配置
MILP_SOLVER_TIMEOUT=1800  # 30分钟
MILP_GAP_TOLERANCE=0.01
MAX_CONCURRENT_CALCULATIONS=2

# LDAP配置
LDAP_ENABLED=false
LDAP_SERVER_URI=ldap://localhost:389
LDAP_SERVER_PORT=389
LDAP_USE_SSL=false
LDAP_USE_TLS=false
LDAP_BIND_DN=cn=admin,dc=example,dc=com
LDAP_BIND_PASSWORD=
LDAP_AUTH_METHOD=SIMPLE
LDAP_BASE_DN=dc=example,dc=com
LDAP_USER_SEARCH_BASE=ou=users,dc=example,dc=com
LDAP_GROUP_SEARCH_BASE=ou=groups,dc=example,dc=com
LDAP_USER_FILTER=(uid={username})
LDAP_USERNAME_ATTR=uid
LDAP_EMAIL_ATTR=mail
LDAP_FIRST_NAME_ATTR=givenName
LDAP_LAST_NAME_ATTR=sn
LDAP_DISPLAY_NAME_ATTR=displayName
LDAP_GROUP_FILTER=(member={user_dn})
LDAP_GROUP_NAME_ATTR=cn
LDAP_ADMIN_GROUPS=["administrators", "admin"]
LDAP_USER_GROUPS=["users"]
LDAP_AUTO_CREATE_USERS=true
LDAP_AUTO_UPDATE_USERS=true
LDAP_SYNC_GROUPS=true

# SSO配置
SSO_ENABLED=false
SSO_TYPE=saml  # saml, oauth, oidc

# SAML配置
SAML_IDP_URL=
SAML_IDP_CERT=
SAML_SP_ENTITY_ID=smart-planning
SAML_ACS_URL=http://localhost:8000/api/v1/auth/saml/acs
SAML_SLS_URL=http://localhost:8000/api/v1/auth/saml/sls

# OAuth/OIDC配置
OAUTH_CLIENT_ID=
OAUTH_CLIENT_SECRET=
OAUTH_AUTH_URL=
OAUTH_TOKEN_URL=
OAUTH_USERINFO_URL=
OAUTH_REDIRECT_URI=http://localhost:8000/api/v1/auth/oauth/callback
OAUTH_SCOPE=openid profile email

# SSO用户属性映射
SSO_USERNAME_CLAIM=sub
SSO_EMAIL_CLAIM=email
SSO_NAME_CLAIM=name
SSO_GROUPS_CLAIM=groups
SSO_ADMIN_GROUPS=["administrators", "admin"]
SSO_USER_GROUPS=["users"]
SSO_AUTO_CREATE_USERS=true
SSO_AUTO_UPDATE_USERS=true
