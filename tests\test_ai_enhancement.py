"""
AI能力增强功能测试
测试预测分析、异常检测、智能优化等功能
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'frontend'))

from services.ai_enhancement_service import (
    AIEnhancementService,
    PredictiveAnalyticsEngine,
    AnomalyDetectionEngine,
    IntelligentOptimizationEngine,
    PredictionType,
    AnomalyType,
    OptimizationType
)

class TestPredictiveAnalytics:
    """预测分析测试"""
    
    @pytest.fixture
    def analytics_engine(self):
        """创建预测分析引擎实例"""
        return PredictiveAnalyticsEngine()
    
    @pytest.fixture
    def sample_data(self):
        """创建样本数据"""
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), 
                             end=datetime.now(), freq='D')
        data = []
        for date in dates:
            data.append({
                "date": date,
                "demand": np.random.normal(100, 20),
                "production": np.random.normal(95, 15),
                "efficiency": np.random.uniform(0.7, 0.95)
            })
        return pd.DataFrame(data)
    
    @pytest.mark.asyncio
    async def test_demand_prediction(self, analytics_engine):
        """测试需求预测"""
        input_data = {
            "base_demand": 100,
            "historical_data": "30天历史数据"
        }
        
        result = await analytics_engine.predict_demand(input_data, forecast_horizon=7)
        
        assert result.prediction_type == PredictionType.DEMAND_FORECAST
        assert len(result.predictions) == 7
        assert len(result.confidence_intervals) == 7
        assert all(pred > 0 for pred in result.predictions)
        assert result.timestamp is not None
        assert result.model_version is not None
    
    @pytest.mark.asyncio
    async def test_equipment_failure_prediction(self, analytics_engine):
        """测试设备故障预测"""
        equipment_data = {
            "equipment_ids": ["L01", "L02", "L03", "L04"]
        }
        
        result = await analytics_engine.predict_equipment_failure(equipment_data)
        
        assert result.prediction_type == PredictionType.EQUIPMENT_FAILURE
        assert len(result.predictions) == 4
        assert all(0 <= prob <= 1 for prob in result.predictions)
        assert len(result.confidence_intervals) == 4
        assert "运行时间" in result.feature_importance
    
    @pytest.mark.asyncio
    async def test_model_training(self, analytics_engine, sample_data):
        """测试模型训练"""
        result = await analytics_engine.train_demand_forecast_model(sample_data)
        
        assert result["success"] is True
        assert "model_version" in result
        assert "performance" in result
        assert "mae" in result["performance"]
        assert "rmse" in result["performance"]

class TestAnomalyDetection:
    """异常检测测试"""
    
    @pytest.fixture
    def detection_engine(self):
        """创建异常检测引擎实例"""
        return AnomalyDetectionEngine()
    
    @pytest.fixture
    def normal_data(self):
        """创建正常数据"""
        np.random.seed(42)
        data = []
        for i in range(100):
            data.append({
                "timestamp": datetime.now() - timedelta(hours=100-i),
                "value": np.random.normal(100, 10),
                "temperature": np.random.normal(25, 3),
                "pressure": np.random.normal(1.0, 0.1)
            })
        return pd.DataFrame(data)
    
    @pytest.fixture
    def anomaly_data(self):
        """创建包含异常的数据"""
        np.random.seed(42)
        data = []
        for i in range(100):
            value = np.random.normal(100, 10)
            # 注入异常
            if i in [20, 50, 80]:
                value = np.random.choice([150, 50])
            
            data.append({
                "timestamp": datetime.now() - timedelta(hours=100-i),
                "value": value,
                "temperature": np.random.normal(25, 3),
                "pressure": np.random.normal(1.0, 0.1)
            })
        return pd.DataFrame(data)
    
    @pytest.mark.asyncio
    async def test_setup_detectors(self, detection_engine, normal_data):
        """测试异常检测器设置"""
        result = await detection_engine.setup_anomaly_detectors(normal_data)
        
        assert result["success"] is True
        assert "detectors_setup" in result
        assert len(result["detectors_setup"]) > 0
    
    @pytest.mark.asyncio
    async def test_statistical_anomaly_detection(self, detection_engine, anomaly_data):
        """测试统计异常检测"""
        # 先设置检测器
        await detection_engine.setup_anomaly_detectors(anomaly_data)
        
        result = await detection_engine.detect_anomalies(anomaly_data, AnomalyType.STATISTICAL)
        
        assert result.anomaly_type == AnomalyType.STATISTICAL
        assert isinstance(result.anomalies, list)
        assert isinstance(result.anomaly_scores, list)
        assert result.threshold > 0
        assert 0 <= result.detection_rate <= 1
    
    @pytest.mark.asyncio
    async def test_ensemble_anomaly_detection(self, detection_engine, anomaly_data):
        """测试集成异常检测"""
        await detection_engine.setup_anomaly_detectors(anomaly_data)
        
        result = await detection_engine.detect_anomalies(anomaly_data, AnomalyType.ENSEMBLE)
        
        assert result.anomaly_type == AnomalyType.ENSEMBLE
        assert len(result.anomalies) >= 0
        assert result.timestamp is not None

class TestIntelligentOptimization:
    """智能优化测试"""
    
    @pytest.fixture
    def optimization_engine(self):
        """创建智能优化引擎实例"""
        return IntelligentOptimizationEngine()
    
    @pytest.fixture
    def production_data(self):
        """创建生产数据"""
        return {
            "orders": [
                {"order_id": "O001", "processing_time": 120, "priority": 1},
                {"order_id": "O002", "processing_time": 90, "priority": 2},
                {"order_id": "O003", "processing_time": 150, "priority": 1},
                {"order_id": "O004", "processing_time": 80, "priority": 3}
            ],
            "equipment": {
                "L01": {"status": "available", "efficiency": 0.9},
                "L02": {"status": "available", "efficiency": 0.85},
                "L03": {"status": "maintenance", "efficiency": 0.0},
                "L04": {"status": "available", "efficiency": 0.95}
            }
        }
    
    @pytest.fixture
    def resource_data(self):
        """创建资源数据"""
        return {
            "resources": {
                "R001": {"type": "material", "capacity": 1000, "unit_cost": 10, "efficiency": 0.9},
                "R002": {"type": "material", "capacity": 800, "unit_cost": 12, "efficiency": 0.95},
                "R003": {"type": "labor", "capacity": 40, "unit_cost": 50, "efficiency": 0.85}
            },
            "demands": [
                {"demand_id": "D001", "type": "material", "amount": 500, "baseline_cost": 5500},
                {"demand_id": "D002", "type": "material", "amount": 300, "baseline_cost": 3300},
                {"demand_id": "D003", "type": "labor", "amount": 20, "baseline_cost": 1100}
            ]
        }
    
    @pytest.mark.asyncio
    async def test_production_schedule_optimization(self, optimization_engine, production_data):
        """测试生产排程优化"""
        result = await optimization_engine.optimize(
            OptimizationType.PRODUCTION_SCHEDULE, 
            production_data
        )
        
        assert result.optimization_type == OptimizationType.PRODUCTION_SCHEDULE
        assert result.optimal_solution is not None
        assert "schedule" in result.optimal_solution
        assert result.objective_value > 0
        assert result.improvement_percentage >= 0
        assert result.constraints_satisfied is True
        assert result.computation_time >= 0
    
    @pytest.mark.asyncio
    async def test_resource_allocation_optimization(self, optimization_engine, resource_data):
        """测试资源分配优化"""
        result = await optimization_engine.optimize(
            OptimizationType.RESOURCE_ALLOCATION,
            resource_data
        )
        
        assert result.optimization_type == OptimizationType.RESOURCE_ALLOCATION
        assert "allocation" in result.optimal_solution
        assert result.objective_value > 0
        assert result.improvement_percentage >= 0
    
    @pytest.mark.asyncio
    async def test_energy_optimization(self, optimization_engine, production_data):
        """测试能耗优化"""
        energy_data = {
            "equipment": {
                "L01": {"power_rating": 50},
                "L02": {"power_rating": 45},
                "L04": {"power_rating": 55}
            },
            "schedule": production_data["orders"]
        }
        
        result = await optimization_engine.optimize(
            OptimizationType.ENERGY_OPTIMIZATION,
            energy_data
        )
        
        assert result.optimization_type == OptimizationType.ENERGY_OPTIMIZATION
        assert "energy_plan" in result.optimal_solution
        assert result.improvement_percentage >= 0

class TestAIEnhancementService:
    """AI增强服务集成测试"""
    
    @pytest.fixture
    def ai_service(self):
        """创建AI增强服务实例"""
        return AIEnhancementService()
    
    @pytest.mark.asyncio
    async def test_service_initialization(self, ai_service):
        """测试服务初始化"""
        result = await ai_service.initialize_ai_services()
        
        assert result["success"] is True
        assert "services_initialized" in result
        assert len(result["services_initialized"]) > 0
    
    @pytest.mark.asyncio
    async def test_comprehensive_analysis(self, ai_service):
        """测试综合AI分析"""
        input_data = {
            "orders": [
                {"order_id": "O001", "processing_time": 120},
                {"order_id": "O002", "processing_time": 90}
            ],
            "equipment": {
                "L01": {"status": "available", "efficiency": 0.9},
                "L02": {"status": "available", "efficiency": 0.85}
            },
            "monitoring_data": [
                {"timestamp": datetime.now(), "value": 100, "equipment": "L01"},
                {"timestamp": datetime.now(), "value": 95, "equipment": "L02"}
            ]
        }
        
        result = await ai_service.get_comprehensive_ai_analysis(input_data)
        
        assert result["success"] is True
        assert "results" in result
        assert "analysis_timestamp" in result

class TestPerformanceAndReliability:
    """性能和可靠性测试"""
    
    @pytest.fixture
    def ai_service(self):
        return AIEnhancementService()
    
    @pytest.mark.asyncio
    async def test_concurrent_predictions(self, ai_service):
        """测试并发预测"""
        input_data = {"base_demand": 100}
        
        # 创建多个并发预测任务
        tasks = []
        for _ in range(5):
            task = ai_service.predictive_analytics.predict_demand(input_data, 7)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 5
        assert all(result.prediction_type == PredictionType.DEMAND_FORECAST for result in results)
    
    @pytest.mark.asyncio
    async def test_large_dataset_processing(self, ai_service):
        """测试大数据集处理"""
        # 创建大数据集
        large_data = []
        for i in range(1000):
            large_data.append({
                "timestamp": datetime.now() - timedelta(hours=i),
                "value": np.random.normal(100, 10),
                "equipment": f"L{i%4+1:02d}"
            })
        
        df = pd.DataFrame(large_data)
        
        result = await ai_service.anomaly_detection.detect_anomalies(df)
        
        assert result.timestamp is not None
        assert isinstance(result.anomalies, list)
    
    @pytest.mark.asyncio
    async def test_error_handling(self, ai_service):
        """测试错误处理"""
        # 测试空数据
        empty_data = {}
        
        result = await ai_service.predictive_analytics.predict_demand(empty_data, 7)
        
        # 应该返回模拟结果而不是抛出异常
        assert result.prediction_type == PredictionType.DEMAND_FORECAST
        assert len(result.predictions) == 7

# 运行测试的辅助函数
def run_tests():
    """运行所有测试"""
    pytest.main([__file__, "-v"])

if __name__ == "__main__":
    run_tests()
