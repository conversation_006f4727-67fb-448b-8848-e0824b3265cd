"""
高级数据过滤服务
支持字段级别的精细化数据筛选和自定义SQL查询
"""

import json
import sqlite3
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
import logging

logger = logging.getLogger(__name__)


class AdvancedDataFilterService:
    """高级数据过滤服务类"""

    def __init__(self):
        self.filter_db_path = "data_filters.db"
        self._init_filter_database()

    def _init_filter_database(self):
        """初始化过滤配置数据库"""
        conn = sqlite3.connect(self.filter_db_path)
        cursor = conn.cursor()

        # 数据源过滤配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_source_filters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_name TEXT NOT NULL,
                filter_name TEXT NOT NULL,
                filter_config TEXT NOT NULL,
                enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(source_name, filter_name)
            )
        ''')

        # 自定义SQL查询配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS custom_sql_queries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query_name TEXT UNIQUE NOT NULL,
                source_name TEXT NOT NULL,
                sql_query TEXT NOT NULL,
                parameters TEXT,
                description TEXT,
                enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 字段映射配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS field_mappings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_name TEXT NOT NULL,
                original_field TEXT NOT NULL,
                mapped_field TEXT NOT NULL,
                data_type TEXT,
                transformation TEXT,
                enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

    def add_data_source_filter(self, source_name: str, filter_name: str, 
                              filter_config: Dict[str, Any]) -> bool:
        """添加数据源过滤配置"""
        try:
            conn = sqlite3.connect(self.filter_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO data_source_filters 
                (source_name, filter_name, filter_config, updated_at)
                VALUES (?, ?, ?, ?)
            ''', (
                source_name,
                filter_name,
                json.dumps(filter_config),
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"添加数据源过滤配置失败: {e}")
            return False

    def add_custom_sql_query(self, query_name: str, source_name: str, 
                           sql_query: str, parameters: Dict[str, Any] = None,
                           description: str = "") -> bool:
        """添加自定义SQL查询"""
        try:
            conn = sqlite3.connect(self.filter_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO custom_sql_queries 
                (query_name, source_name, sql_query, parameters, description, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                query_name,
                source_name,
                sql_query,
                json.dumps(parameters) if parameters else None,
                description,
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"添加自定义SQL查询失败: {e}")
            return False

    def get_filtered_pci_data(self, filter_config: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """获取过滤后的PCI数据"""
        # 默认过滤配置
        default_config = {
            "container_types": None,  # 箱子类型过滤
            "min_days_old": None,     # 最小库龄
            "max_days_old": None,     # 最大库龄
            "priority_threshold": None, # 优先级阈值
            "quantity_range": None,   # 数量范围
            "specific_fs_ids": None,  # 特定FS ID
            "exclude_empty": True,    # 排除空箱
            "fields": None           # 指定返回字段
        }

        if filter_config:
            default_config.update(filter_config)

        # 模拟PCI数据（实际应该从PCI服务获取）
        all_pci_data = [
            {
                "fs_id": "FS001",
                "container_type": "箱子",
                "material_code": "MAT001",
                "quantity": 150,
                "days_old": 45,
                "consumption_priority": 75,
                "location": "A区-01",
                "batch_number": "B20240101"
            },
            {
                "fs_id": "FS002", 
                "container_type": "托盘",
                "material_code": "MAT002",
                "quantity": 200,
                "days_old": 190,
                "consumption_priority": 95,
                "location": "B区-02",
                "batch_number": "B20230615"
            },
            {
                "fs_id": "FS003",
                "container_type": "箱子",
                "material_code": "MAT003", 
                "quantity": 0,
                "days_old": 30,
                "consumption_priority": 20,
                "location": "C区-03",
                "batch_number": "B20240115"
            }
        ]

        # 应用过滤条件
        filtered_data = []
        for item in all_pci_data:
            # 箱子类型过滤
            if (default_config["container_types"] and 
                item["container_type"] not in default_config["container_types"]):
                continue

            # 库龄过滤
            if (default_config["min_days_old"] and 
                item["days_old"] < default_config["min_days_old"]):
                continue

            if (default_config["max_days_old"] and 
                item["days_old"] > default_config["max_days_old"]):
                continue

            # 优先级过滤
            if (default_config["priority_threshold"] and 
                item["consumption_priority"] < default_config["priority_threshold"]):
                continue

            # 数量范围过滤
            if default_config["quantity_range"]:
                min_qty, max_qty = default_config["quantity_range"]
                if item["quantity"] < min_qty or item["quantity"] > max_qty:
                    continue

            # 特定FS ID过滤
            if (default_config["specific_fs_ids"] and 
                item["fs_id"] not in default_config["specific_fs_ids"]):
                continue

            # 排除空箱
            if default_config["exclude_empty"] and item["quantity"] == 0:
                continue

            # 字段选择
            if default_config["fields"]:
                filtered_item = {field: item.get(field) for field in default_config["fields"]}
            else:
                filtered_item = item

            filtered_data.append(filtered_item)

        return filtered_data

    def get_filtered_equipment_data(self, filter_config: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """获取过滤后的设备数据"""
        default_config = {
            "equipment_ids": None,    # 特定设备ID
            "equipment_types": None,  # 设备类型
            "status_filter": None,    # 状态过滤
            "workshop_filter": None,  # 车间过滤
            "capacity_range": None,   # 产能范围
            "utilization_range": None, # 利用率范围
            "fields": None           # 指定返回字段
        }

        if filter_config:
            default_config.update(filter_config)

        # 模拟设备数据
        all_equipment_data = [
            {
                "equipment_id": "L01",
                "equipment_name": "生产线01",
                "equipment_type": "生产线",
                "status": "运行中",
                "workshop": "A车间",
                "capacity": 50,
                "current_utilization": 85.2,
                "maintenance_status": "正常"
            },
            {
                "equipment_id": "L02",
                "equipment_name": "生产线02", 
                "equipment_type": "生产线",
                "status": "停机",
                "workshop": "A车间",
                "capacity": 45,
                "current_utilization": 0,
                "maintenance_status": "维修中"
            },
            {
                "equipment_id": "Tank01",
                "equipment_name": "储罐01",
                "equipment_type": "储罐",
                "status": "运行中",
                "workshop": "B车间",
                "capacity": 1000,
                "current_utilization": 76.3,
                "maintenance_status": "正常"
            }
        ]

        # 应用过滤条件
        filtered_data = []
        for item in all_equipment_data:
            # 特定设备ID过滤
            if (default_config["equipment_ids"] and 
                item["equipment_id"] not in default_config["equipment_ids"]):
                continue

            # 设备类型过滤
            if (default_config["equipment_types"] and 
                item["equipment_type"] not in default_config["equipment_types"]):
                continue

            # 状态过滤
            if (default_config["status_filter"] and 
                item["status"] not in default_config["status_filter"]):
                continue

            # 车间过滤
            if (default_config["workshop_filter"] and 
                item["workshop"] not in default_config["workshop_filter"]):
                continue

            # 产能范围过滤
            if default_config["capacity_range"]:
                min_cap, max_cap = default_config["capacity_range"]
                if item["capacity"] < min_cap or item["capacity"] > max_cap:
                    continue

            # 利用率范围过滤
            if default_config["utilization_range"]:
                min_util, max_util = default_config["utilization_range"]
                if (item["current_utilization"] < min_util or 
                    item["current_utilization"] > max_util):
                    continue

            # 字段选择
            if default_config["fields"]:
                filtered_item = {field: item.get(field) for field in default_config["fields"]}
            else:
                filtered_item = item

            filtered_data.append(filtered_item)

        return filtered_data

    def execute_custom_sql_query(self, query_name: str, 
                                parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """执行自定义SQL查询"""
        try:
            conn = sqlite3.connect(self.filter_db_path)
            cursor = conn.cursor()

            # 获取查询配置
            cursor.execute('''
                SELECT source_name, sql_query, parameters 
                FROM custom_sql_queries 
                WHERE query_name = ? AND enabled = 1
            ''', (query_name,))

            result = cursor.fetchone()
            if not result:
                logger.error(f"未找到查询配置: {query_name}")
                return []

            source_name, sql_query, stored_params = result

            # 合并参数
            query_params = {}
            if stored_params:
                query_params.update(json.loads(stored_params))
            if parameters:
                query_params.update(parameters)

            conn.close()

            # 根据数据源类型执行查询
            if source_name == "mes_database":
                return self._execute_mes_sql_query(sql_query, query_params)
            elif source_name == "erp_database":
                return self._execute_erp_sql_query(sql_query, query_params)
            elif source_name == "pci_database":
                return self._execute_pci_sql_query(sql_query, query_params)
            else:
                logger.error(f"不支持的数据源: {source_name}")
                return []

        except Exception as e:
            logger.error(f"执行自定义SQL查询失败: {e}")
            return []

    def _execute_mes_sql_query(self, sql_query: str, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行MES数据库SQL查询"""
        # 这里应该连接实际的MES数据库
        # 现在返回模拟数据
        logger.info(f"执行MES SQL查询: {sql_query}")
        logger.info(f"查询参数: {parameters}")

        # 模拟MES查询结果
        return [
            {
                "order_id": "MO001",
                "product_code": "PROD001",
                "quantity": 100,
                "start_time": "2024-01-15 08:00:00",
                "end_time": "2024-01-15 16:00:00",
                "status": "completed"
            },
            {
                "order_id": "MO002", 
                "product_code": "PROD002",
                "quantity": 150,
                "start_time": "2024-01-15 16:00:00",
                "end_time": "2024-01-16 08:00:00",
                "status": "in_progress"
            }
        ]

    def _execute_erp_sql_query(self, sql_query: str, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行ERP数据库SQL查询"""
        logger.info(f"执行ERP SQL查询: {sql_query}")
        logger.info(f"查询参数: {parameters}")

        # 模拟ERP查询结果
        return [
            {
                "material_code": "MAT001",
                "material_name": "原料A",
                "current_stock": 500,
                "safety_stock": 100,
                "unit_cost": 25.50
            },
            {
                "material_code": "MAT002",
                "material_name": "原料B", 
                "current_stock": 300,
                "safety_stock": 50,
                "unit_cost": 18.75
            }
        ]

    def _execute_pci_sql_query(self, sql_query: str, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行PCI数据库SQL查询"""
        logger.info(f"执行PCI SQL查询: {sql_query}")
        logger.info(f"查询参数: {parameters}")

        # 模拟PCI查询结果
        return [
            {
                "fs_id": "FS001",
                "material_code": "MAT001",
                "quantity": 150,
                "location": "A区-01",
                "receive_date": "2023-12-01",
                "days_old": 45
            }
        ]

    def get_available_filters(self, source_name: str) -> List[Dict[str, Any]]:
        """获取可用的过滤器"""
        conn = sqlite3.connect(self.filter_db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT filter_name, filter_config, enabled
            FROM data_source_filters
            WHERE source_name = ?
        ''', (source_name,))

        filters = []
        for row in cursor.fetchall():
            filters.append({
                "name": row[0],
                "config": json.loads(row[1]),
                "enabled": bool(row[2])
            })

        conn.close()
        return filters

    def get_available_sql_queries(self, source_name: str = None) -> List[Dict[str, Any]]:
        """获取可用的SQL查询"""
        conn = sqlite3.connect(self.filter_db_path)
        cursor = conn.cursor()

        if source_name:
            cursor.execute('''
                SELECT query_name, source_name, description, enabled
                FROM custom_sql_queries
                WHERE source_name = ?
            ''', (source_name,))
        else:
            cursor.execute('''
                SELECT query_name, source_name, description, enabled
                FROM custom_sql_queries
            ''')

        queries = []
        for row in cursor.fetchall():
            queries.append({
                "name": row[0],
                "source": row[1],
                "description": row[2],
                "enabled": bool(row[3])
            })

        conn.close()
        return queries


# 全局高级数据过滤服务实例
advanced_data_filter_service = AdvancedDataFilterService()
