"""
PCI服务模块 - FS数据管理和SQL查询服务
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging


class PCIService:
    """PCI数据服务类"""
    
    def __init__(self, db_path: str = "pci_data.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化PCI数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建FS库存表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fs_inventory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                fs_id TEXT UNIQUE NOT NULL,
                product_name TEXT NOT NULL,
                production_date DATE NOT NULL,
                quantity INTEGER NOT NULL,
                location TEXT NOT NULL,
                yield_rate REAL NOT NULL,
                status TEXT NOT NULL DEFAULT 'available',
                gu_factor REAL NOT NULL DEFAULT 1.0,
                urgency_level TEXT NOT NULL DEFAULT 'medium',
                batch_number TEXT,
                supplier TEXT,
                material_grade TEXT,
                storage_condition TEXT,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建消耗记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fs_consumption (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                fs_id TEXT NOT NULL,
                consumption_date DATE NOT NULL,
                consumed_quantity INTEGER NOT NULL,
                remaining_quantity INTEGER NOT NULL,
                consumption_reason TEXT,
                operator TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (fs_id) REFERENCES fs_inventory (fs_id)
            )
        ''')
        
        # 创建SQL查询历史表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS query_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query_name TEXT,
                sql_query TEXT NOT NULL,
                execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                result_count INTEGER,
                execution_duration REAL,
                user_id TEXT,
                status TEXT DEFAULT 'success'
            )
        ''')
        
        # 创建消耗策略配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS consumption_strategy (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_name TEXT NOT NULL,
                fifo_threshold_days INTEGER DEFAULT 180,
                weight_age REAL DEFAULT 0.4,
                weight_yield REAL DEFAULT 0.3,
                weight_urgency REAL DEFAULT 0.2,
                weight_gu REAL DEFAULT 0.1,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # 初始化示例数据
        self._init_sample_data()
    
    def _init_sample_data(self):
        """初始化示例FS数据"""
        sample_fs_data = [
            {
                'fs_id': 'FS001',
                'product_name': '产品A',
                'production_date': '2023-06-15',
                'quantity': 1500,
                'location': 'A区-01',
                'yield_rate': 92.5,
                'status': 'available',
                'gu_factor': 1.2,
                'urgency_level': 'high',
                'batch_number': 'B230615001',
                'supplier': '供应商A',
                'material_grade': 'Grade1',
                'storage_condition': '常温干燥'
            },
            {
                'fs_id': 'FS002',
                'product_name': '产品B',
                'production_date': '2023-08-20',
                'quantity': 800,
                'location': 'B区-03',
                'yield_rate': 88.3,
                'status': 'available',
                'gu_factor': 1.0,
                'urgency_level': 'medium',
                'batch_number': 'B230820002',
                'supplier': '供应商B',
                'material_grade': 'Grade2',
                'storage_condition': '低温保存'
            },
            {
                'fs_id': 'FS003',
                'product_name': '产品A',
                'production_date': '2023-05-10',
                'quantity': 2000,
                'location': 'A区-05',
                'yield_rate': 85.7,
                'status': 'available',
                'gu_factor': 1.5,
                'urgency_level': 'high',
                'batch_number': 'B230510003',
                'supplier': '供应商A',
                'material_grade': 'Grade1',
                'storage_condition': '常温干燥'
            },
            {
                'fs_id': 'FS004',
                'product_name': '产品C',
                'production_date': '2023-10-01',
                'quantity': 1200,
                'location': 'C区-02',
                'yield_rate': 94.1,
                'status': 'available',
                'gu_factor': 0.8,
                'urgency_level': 'low',
                'batch_number': 'B231001004',
                'supplier': '供应商C',
                'material_grade': 'Grade3',
                'storage_condition': '防潮保存'
            },
            {
                'fs_id': 'FS005',
                'product_name': '产品D',
                'production_date': '2023-04-25',
                'quantity': 900,
                'location': 'D区-01',
                'yield_rate': 89.2,
                'status': 'available',
                'gu_factor': 1.3,
                'urgency_level': 'medium',
                'batch_number': 'B230425005',
                'supplier': '供应商D',
                'material_grade': 'Grade2',
                'storage_condition': '常温干燥'
            }
        ]
        
        # 检查是否已有数据
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM fs_inventory")
        count = cursor.fetchone()[0]
        
        if count == 0:
            # 插入示例数据
            for fs_data in sample_fs_data:
                self.add_fs_inventory(fs_data)
        
        conn.close()
    
    def execute_sql_query(self, sql_query: str, user_id: str = None) -> Dict[str, Any]:
        """执行SQL查询"""
        start_time = datetime.now()
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 执行查询
            df = pd.read_sql_query(sql_query, conn)
            
            # 计算执行时间
            execution_duration = (datetime.now() - start_time).total_seconds()
            
            # 记录查询历史
            self._log_query_history(
                sql_query=sql_query,
                result_count=len(df),
                execution_duration=execution_duration,
                user_id=user_id,
                status='success'
            )
            
            conn.close()
            
            return {
                'success': True,
                'data': df,
                'result_count': len(df),
                'execution_duration': execution_duration
            }
            
        except Exception as e:
            # 记录错误
            execution_duration = (datetime.now() - start_time).total_seconds()
            self._log_query_history(
                sql_query=sql_query,
                result_count=0,
                execution_duration=execution_duration,
                user_id=user_id,
                status='error'
            )
            
            return {
                'success': False,
                'error': str(e),
                'execution_duration': execution_duration
            }
    
    def get_fs_data_with_fifo(self, fifo_threshold_days: int = 180, 
                             filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """获取带FIFO逻辑的FS数据"""
        
        # 构建基础查询
        sql_query = """
        SELECT 
            fs_id,
            product_name,
            production_date,
            quantity,
            location,
            yield_rate as yield,
            status,
            gu_factor,
            urgency_level,
            batch_number,
            supplier,
            material_grade,
            storage_condition,
            JULIANDAY('now') - JULIANDAY(production_date) as days_old
        FROM fs_inventory 
        WHERE status = 'available'
        """
        
        # 应用过滤条件
        if filters:
            conditions = []
            if filters.get('product') and filters['product'] != '全部':
                conditions.append(f"product_name = '{filters['product']}'")
            if filters.get('location') and filters['location'] != '全部':
                conditions.append(f"location LIKE '{filters['location']}%'")
            if filters.get('status') and filters['status'] != '全部':
                conditions.append(f"status = '{filters['status']}'")
            
            if conditions:
                sql_query += " AND " + " AND ".join(conditions)
        
        # FIFO排序
        sql_query += " ORDER BY production_date ASC"
        
        result = self.execute_sql_query(sql_query)
        
        if result['success']:
            fs_data = result['data'].to_dict('records')
            
            # 计算消耗优先级
            for fs in fs_data:
                fs['consumption_priority'] = self._calculate_consumption_priority(
                    fs, fifo_threshold_days
                )
            
            return fs_data
        else:
            return []
    
    def _calculate_consumption_priority(self, fs: Dict[str, Any], 
                                      fifo_threshold_days: int) -> float:
        """计算消耗优先级"""
        # 默认权重
        weights = {
            'age': 0.4,
            'yield': 0.3,
            'urgency': 0.2,
            'gu': 0.1
        }
        
        # 库龄得分
        age_score = min(100, (fs['days_old'] / fifo_threshold_days) * 100)
        
        # Yield得分 (Yield越低得分越高)
        yield_score = max(0, 100 - fs['yield'])
        
        # 紧急程度得分
        urgency_scores = {'high': 100, 'medium': 60, 'low': 20}
        urgency_score = urgency_scores.get(fs['urgency_level'], 20)
        
        # GU得分
        gu_score = min(100, fs['gu_factor'] * 50)
        
        # 加权计算
        total_priority = (
            age_score * weights['age'] +
            yield_score * weights['yield'] +
            urgency_score * weights['urgency'] +
            gu_score * weights['gu']
        )
        
        return round(total_priority, 1)
    
    def add_fs_inventory(self, fs_data: Dict[str, Any]) -> bool:
        """添加FS库存记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO fs_inventory (
                    fs_id, product_name, production_date, quantity, location,
                    yield_rate, status, gu_factor, urgency_level, batch_number,
                    supplier, material_grade, storage_condition
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                fs_data['fs_id'],
                fs_data['product_name'],
                fs_data['production_date'],
                fs_data['quantity'],
                fs_data['location'],
                fs_data['yield_rate'],
                fs_data.get('status', 'available'),
                fs_data.get('gu_factor', 1.0),
                fs_data.get('urgency_level', 'medium'),
                fs_data.get('batch_number', ''),
                fs_data.get('supplier', ''),
                fs_data.get('material_grade', ''),
                fs_data.get('storage_condition', '')
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logging.error(f"添加FS库存失败: {e}")
            return False
    
    def update_fs_consumption(self, fs_id: str, consumed_quantity: int, 
                            reason: str = "", operator: str = "") -> bool:
        """更新FS消耗记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取当前库存
            cursor.execute("SELECT quantity FROM fs_inventory WHERE fs_id = ?", (fs_id,))
            result = cursor.fetchone()
            
            if not result:
                return False
            
            current_quantity = result[0]
            remaining_quantity = current_quantity - consumed_quantity
            
            if remaining_quantity < 0:
                return False
            
            # 更新库存
            cursor.execute(
                "UPDATE fs_inventory SET quantity = ? WHERE fs_id = ?",
                (remaining_quantity, fs_id)
            )
            
            # 记录消耗
            cursor.execute('''
                INSERT INTO fs_consumption (
                    fs_id, consumption_date, consumed_quantity, 
                    remaining_quantity, consumption_reason, operator
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                fs_id,
                datetime.now().date(),
                consumed_quantity,
                remaining_quantity,
                reason,
                operator
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logging.error(f"更新FS消耗失败: {e}")
            return False
    
    def get_query_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取查询历史"""
        sql_query = f"""
        SELECT 
            query_name,
            sql_query,
            execution_time,
            result_count,
            execution_duration,
            status
        FROM query_history 
        ORDER BY execution_time DESC 
        LIMIT {limit}
        """
        
        result = self.execute_sql_query(sql_query)
        
        if result['success']:
            return result['data'].to_dict('records')
        else:
            return []
    
    def _log_query_history(self, sql_query: str, result_count: int, 
                          execution_duration: float, user_id: str = None, 
                          status: str = 'success'):
        """记录查询历史"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO query_history (
                    sql_query, result_count, execution_duration, user_id, status
                ) VALUES (?, ?, ?, ?, ?)
            ''', (sql_query, result_count, execution_duration, user_id, status))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logging.error(f"记录查询历史失败: {e}")


# 全局PCI服务实例
pci_service = PCIService()
