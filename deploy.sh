#!/bin/bash

# Smart Planning Docker 一键部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|logs|status]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="smart-planning"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi

    log_success "系统要求检查通过"
}

# 检查环境配置
check_environment() {
    log_info "检查环境配置..."

    if [ ! -f "$ENV_FILE" ]; then
        if [ -f ".env.example" ]; then
            log_warning ".env 文件不存在，从 .env.example 复制..."
            cp .env.example .env
            log_warning "请编辑 .env 文件配置数据库密码等参数"
        else
            log_error ".env 和 .env.example 文件都不存在"
            exit 1
        fi
    fi

    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "docker-compose.yml 文件不存在"
        exit 1
    fi

    log_success "环境配置检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."

    directories=(
        "uploads"
        "logs"
        "exports"
        "backups"
        "scripts/mysql"
    )

    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done

    log_success "目录创建完成"
}

# 启动服务
start_services() {
    log_info "启动 Smart Planning 服务..."

    # 拉取最新镜像
    log_info "拉取Docker镜像..."
    docker-compose pull

    # 构建自定义镜像
    log_info "构建应用镜像..."
    docker-compose build

    # 启动服务
    log_info "启动所有服务..."
    docker-compose up -d

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10

    # 检查服务状态
    check_services_health

    log_success "Smart Planning 服务启动完成！"
    show_access_info
}

# 停止服务
stop_services() {
    log_info "停止 Smart Planning 服务..."
    docker-compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启 Smart Planning 服务..."
    stop_services
    sleep 5
    start_services
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态..."

    services=("mysql" "redis" "backend" "frontend")

    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "${PROJECT_NAME}_${service}.*Up"; then
            log_success "$service 服务运行正常"
        else
            log_warning "$service 服务可能存在问题"
        fi
    done
}

# 显示服务状态
show_status() {
    log_info "Smart Planning 服务状态:"
    docker-compose ps

    echo ""
    log_info "容器资源使用情况:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# 显示日志
show_logs() {
    if [ -n "$2" ]; then
        log_info "显示 $2 服务日志..."
        docker-compose logs -f "$2"
    else
        log_info "显示所有服务日志..."
        docker-compose logs -f
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    log_success "🎉 Smart Planning 部署完成！"
    echo ""
    echo "📱 访问地址:"
    echo "  前端应用: http://localhost:8501"
    echo "  后端API:  http://localhost:8000"
    echo "  API文档:  http://localhost:8000/docs"
    echo ""
    echo "🔑 默认账户:"
    echo "  管理员: admin / admin123456"
    echo "  计划员: planner / admin123456"
    echo "  用户:   user / admin123456"
    echo ""
    echo "🐳 Docker管理:"
    echo "  查看状态: ./deploy.sh status"
    echo "  查看日志: ./deploy.sh logs"
    echo "  停止服务: ./deploy.sh stop"
    echo "  重启服务: ./deploy.sh restart"
    echo ""
}

# 数据库备份
backup_database() {
    log_info "备份数据库..."

    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_file="backups/smart_planning_backup_${timestamp}.sql"

    docker-compose exec mysql mysqldump -u smart_planning -psmart_planning_password smart_planning > "$backup_file"

    if [ $? -eq 0 ]; then
        log_success "数据库备份完成: $backup_file"
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 数据库恢复
restore_database() {
    if [ -z "$2" ]; then
        log_error "请指定备份文件: ./deploy.sh restore <backup_file>"
        exit 1
    fi

    backup_file="$2"

    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        exit 1
    fi

    log_info "恢复数据库从: $backup_file"
    docker-compose exec -T mysql mysql -u smart_planning -psmart_planning_password smart_planning < "$backup_file"

    if [ $? -eq 0 ]; then
        log_success "数据库恢复完成"
    else
        log_error "数据库恢复失败"
        exit 1
    fi
}

# 清理系统
cleanup() {
    log_info "清理Docker资源..."

    # 停止服务
    docker-compose down

    # 删除未使用的镜像
    docker image prune -f

    # 删除未使用的卷
    docker volume prune -f

    # 删除未使用的网络
    docker network prune -f

    log_success "清理完成"
}

# 更新系统
update_system() {
    log_info "更新 Smart Planning 系统..."

    # 备份数据库
    backup_database

    # 拉取最新代码（如果是git仓库）
    if [ -d ".git" ]; then
        log_info "拉取最新代码..."
        git pull
    fi

    # 重新构建和启动
    restart_services

    log_success "系统更新完成"
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            check_requirements
            check_environment
            create_directories
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$@"
            ;;
        "backup")
            backup_database
            ;;
        "restore")
            restore_database "$@"
            ;;
        "cleanup")
            cleanup
            ;;
        "update")
            update_system
            ;;
        "help"|"-h"|"--help")
            echo "Smart Planning Docker 部署脚本"
            echo ""
            echo "使用方法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  start     启动所有服务 (默认)"
            echo "  stop      停止所有服务"
            echo "  restart   重启所有服务"
            echo "  status    显示服务状态"
            echo "  logs      显示服务日志"
            echo "  backup    备份数据库"
            echo "  restore   恢复数据库"
            echo "  cleanup   清理Docker资源"
            echo "  update    更新系统"
            echo "  help      显示帮助信息"
            echo ""
            ;;
        *)
            log_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
