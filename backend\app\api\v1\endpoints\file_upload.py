"""
文件上传API端点
"""

import os
import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
import aiofiles
import logging

from app.core.database import get_db
from app.core.config import settings
from app.core.security import security_utils
from app.core.logging_config import business_logger
from app.services.file_service import FileService
from app.schemas.file_upload import (
    FileUploadResponse, 
    UploadHistoryResponse, 
    ExtractedDataResponse,
    FileProcessingRequest
)
from app.api.dependencies import get_current_user

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/file", response_model=FileUploadResponse, summary="上传文件")
async def upload_file(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    auto_process: bool = Form(True),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    上传文件
    
    - **file**: 上传的文件
    - **description**: 文件描述
    - **auto_process**: 是否自动处理文件
    """
    try:
        # 验证文件类型
        if not security_utils.validate_file_type(file.filename, file.content_type):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件类型: {file.content_type}"
            )
        
        # 验证文件大小
        if file.size > settings.MAX_UPLOAD_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"文件大小超过限制: {settings.MAX_UPLOAD_SIZE / 1024 / 1024}MB"
            )
        
        # 生成存储文件名
        file_extension = os.path.splitext(file.filename)[1]
        stored_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(settings.UPLOAD_DIR, stored_filename)
        
        # 确保上传目录存在
        os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
        
        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        # 创建文件记录
        file_service = FileService(db)
        uploaded_file = await file_service.create_file_record(
            original_filename=file.filename,
            stored_filename=stored_filename,
            file_path=file_path,
            file_size=file.size,
            file_type=file.content_type,
            file_extension=file_extension.lstrip('.'),
            description=description,
            user_id=current_user["id"]
        )
        
        # 记录业务日志
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="file_upload",
            details={
                "filename": file.filename,
                "file_size": file.size,
                "file_type": file.content_type
            }
        )
        
        # 如果启用自动处理，添加到处理队列
        if auto_process:
            await file_service.add_to_processing_queue(
                file_id=uploaded_file.id,
                processing_type="auto_parse"
            )
        
        return {
            "success": True,
            "message": "文件上传成功",
            "data": {
                "file_id": uploaded_file.id,
                "filename": uploaded_file.original_filename,
                "file_size": uploaded_file.file_size,
                "file_type": uploaded_file.file_type,
                "status": uploaded_file.status,
                "auto_process": auto_process
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        business_logger.log_error(
            error_type="file_upload_error",
            error_message=str(e),
            details={"filename": file.filename if file else "unknown"}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="文件上传失败"
        )


@router.get("/history", response_model=UploadHistoryResponse, summary="获取上传历史")
async def get_upload_history(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    file_type: Optional[str] = Query(None, description="文件类型过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取上传历史
    
    - **page**: 页码
    - **page_size**: 每页数量
    - **file_type**: 文件类型过滤
    - **status**: 状态过滤
    """
    try:
        file_service = FileService(db)
        files, total = await file_service.get_upload_history(
            user_id=current_user["id"],
            page=page,
            page_size=page_size,
            file_type=file_type,
            status=status
        )
        
        total_pages = (total + page_size - 1) // page_size
        
        return {
            "success": True,
            "data": {
                "files": [
                    {
                        "id": f.id,
                        "original_filename": f.original_filename,
                        "file_size": f.file_size,
                        "file_type": f.file_type,
                        "status": f.status,
                        "created_at": f.created_at.isoformat(),
                        "processed_at": f.processed_at.isoformat() if f.processed_at else None
                    }
                    for f in files
                ],
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "total_pages": total_pages
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取上传历史失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取上传历史失败"
        )


@router.get("/{file_id}/status", summary="获取文件处理状态")
async def get_file_status(
    file_id: str,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取文件处理状态"""
    try:
        file_service = FileService(db)
        file_info = await file_service.get_file_by_id(file_id)
        
        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )
        
        # 检查权限（只能查看自己上传的文件）
        if file_info.created_by != current_user["id"] and not current_user.get("is_superuser"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此文件"
            )
        
        return {
            "success": True,
            "data": {
                "file_id": file_info.id,
                "filename": file_info.original_filename,
                "status": file_info.status,
                "processing_result": file_info.processing_result,
                "error_message": file_info.error_message,
                "created_at": file_info.created_at.isoformat(),
                "processed_at": file_info.processed_at.isoformat() if file_info.processed_at else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取文件状态失败"
        )


@router.get("/{file_id}/extracted-data", response_model=ExtractedDataResponse, summary="获取提取的数据")
async def get_extracted_data(
    file_id: str,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取文件提取的数据"""
    try:
        file_service = FileService(db)
        
        # 验证文件权限
        file_info = await file_service.get_file_by_id(file_id)
        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )
        
        if file_info.created_by != current_user["id"] and not current_user.get("is_superuser"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此文件"
            )
        
        # 获取提取的数据
        extracted_data = await file_service.get_extracted_data(file_id)
        
        return {
            "success": True,
            "data": {
                "file_id": file_id,
                "extracted_data": [
                    {
                        "id": data.id,
                        "data_type": data.data_type,
                        "table_name": data.table_name,
                        "sheet_name": data.sheet_name,
                        "row_count": data.row_count,
                        "column_count": data.column_count,
                        "raw_data": data.raw_data,
                        "processed_data": data.processed_data,
                        "field_mapping": data.field_mapping,
                        "validation_result": data.validation_result,
                        "import_status": data.import_status
                    }
                    for data in extracted_data
                ]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取提取数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取提取数据失败"
        )


@router.post("/{file_id}/process", summary="手动处理文件")
async def process_file(
    file_id: str,
    request: FileProcessingRequest,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """手动处理文件"""
    try:
        file_service = FileService(db)
        
        # 验证文件权限
        file_info = await file_service.get_file_by_id(file_id)
        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )
        
        if file_info.created_by != current_user["id"] and not current_user.get("is_superuser"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权处理此文件"
            )
        
        # 添加到处理队列
        await file_service.add_to_processing_queue(
            file_id=file_id,
            processing_type=request.processing_type,
            processing_params=request.processing_params,
            priority=request.priority
        )
        
        business_logger.log_user_action(
            user_id=current_user["id"],
            action="file_process_request",
            details={
                "file_id": file_id,
                "processing_type": request.processing_type
            }
        )
        
        return {
            "success": True,
            "message": "文件已添加到处理队列",
            "data": {
                "file_id": file_id,
                "processing_type": request.processing_type
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="处理文件失败"
        )


@router.delete("/{file_id}", summary="删除文件")
async def delete_file(
    file_id: str,
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除文件"""
    try:
        file_service = FileService(db)
        
        # 验证文件权限
        file_info = await file_service.get_file_by_id(file_id)
        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )
        
        if file_info.created_by != current_user["id"] and not current_user.get("is_superuser"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此文件"
            )
        
        # 删除文件
        success = await file_service.delete_file(file_id)
        
        if success:
            business_logger.log_user_action(
                user_id=current_user["id"],
                action="file_delete",
                details={"file_id": file_id, "filename": file_info.original_filename}
            )
            
            return {
                "success": True,
                "message": "文件删除成功"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="文件删除失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除文件失败"
        )
