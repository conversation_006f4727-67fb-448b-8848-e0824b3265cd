-- Smart Planning MySQL 初始化脚本
-- 创建数据库和基础配置

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 使用smart_planning数据库
USE smart_planning;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    role ENUM('管理员', '计划员', '主要用户', 'PCI用户', '一般用户') DEFAULT '一般用户',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建文件上传表
CREATE TABLE IF NOT EXISTS file_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    upload_status ENUM('uploading', 'uploaded', 'processing', 'processed', 'failed') DEFAULT 'uploaded',
    user_id INT NOT NULL,
    description TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_upload_status (upload_status),
    INDEX idx_file_type (file_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建生产计划表
CREATE TABLE IF NOT EXISTS production_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    plan_name VARCHAR(100) NOT NULL,
    plan_type ENUM('daily', 'weekly', 'monthly') DEFAULT 'daily',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('draft', 'active', 'completed', 'cancelled') DEFAULT 'draft',
    created_by INT NOT NULL,
    algorithm_used VARCHAR(50),
    optimization_params JSON,
    results JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_plan_type (plan_type),
    INDEX idx_status (status),
    INDEX idx_start_date (start_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建设备管理表
CREATE TABLE IF NOT EXISTS equipment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    equipment_code VARCHAR(50) UNIQUE NOT NULL,
    equipment_name VARCHAR(100) NOT NULL,
    equipment_type ENUM('production_line', 'tank', 'machine', 'other') DEFAULT 'machine',
    status ENUM('running', 'stopped', 'maintenance', 'error') DEFAULT 'stopped',
    capacity DECIMAL(10,2),
    efficiency DECIMAL(5,4) DEFAULT 1.0000,
    location VARCHAR(100),
    specifications JSON,
    maintenance_schedule JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_equipment_code (equipment_code),
    INDEX idx_equipment_type (equipment_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建PCI数据表
CREATE TABLE IF NOT EXISTS pci_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    item_code VARCHAR(100) NOT NULL,
    item_name VARCHAR(200),
    quantity DECIMAL(15,4) NOT NULL,
    unit VARCHAR(20),
    age_days INT DEFAULT 0,
    yield_rate DECIMAL(5,4) DEFAULT 1.0000,
    shipping_urgency ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    gu_factor DECIMAL(8,4) DEFAULT 1.0000,
    fifo_priority INT DEFAULT 0,
    status ENUM('available', 'consumed', 'expired', 'quarantine') DEFAULT 'available',
    location VARCHAR(100),
    batch_number VARCHAR(100),
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_item_code (item_code),
    INDEX idx_age_days (age_days),
    INDEX idx_status (status),
    INDEX idx_fifo_priority (fifo_priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建算法执行记录表
CREATE TABLE IF NOT EXISTS algorithm_executions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    algorithm_name VARCHAR(100) NOT NULL,
    algorithm_type VARCHAR(50) NOT NULL,
    input_data JSON,
    parameters JSON,
    execution_status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
    start_time TIMESTAMP NULL,
    end_time TIMESTAMP NULL,
    execution_time_seconds DECIMAL(10,3),
    results JSON,
    error_message TEXT,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_algorithm_name (algorithm_name),
    INDEX idx_execution_status (execution_status),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认管理员用户
-- 密码: admin123456 (已哈希)
INSERT IGNORE INTO users (username, email, password_hash, full_name, role, is_active) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Qe2', '系统管理员', '管理员', TRUE),
('planner', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Qe2', '生产计划员', '计划员', TRUE),
('user', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5/Qe2', '普通用户', '一般用户', TRUE);

-- 插入示例设备数据
INSERT IGNORE INTO equipment (equipment_code, equipment_name, equipment_type, status, capacity, efficiency, location) VALUES
('L01', '生产线1', 'production_line', 'running', 1000.00, 0.9200, '车间A'),
('L02', '生产线2', 'production_line', 'running', 1200.00, 0.8800, '车间A'),
('L03', '生产线3', 'production_line', 'stopped', 800.00, 0.9500, '车间B'),
('L04', '生产线4', 'production_line', 'maintenance', 1100.00, 0.9000, '车间B'),
('Tank01', '储罐1', 'tank', 'running', 5000.00, 1.0000, '储存区'),
('Tank02', '储罐2', 'tank', 'running', 5000.00, 1.0000, '储存区'),
('Tank03', '储罐3', 'tank', 'stopped', 3000.00, 1.0000, '储存区'),
('Tank04', '储罐4', 'tank', 'running', 4000.00, 1.0000, '储存区'),
('Tank05', '储罐5', 'tank', 'maintenance', 3500.00, 1.0000, '储存区');

-- 插入示例PCI数据
INSERT IGNORE INTO pci_data (item_code, item_name, quantity, unit, age_days, yield_rate, shipping_urgency, gu_factor, status, location) VALUES
('RAW001', '原料A', 1500.0000, 'kg', 185, 0.9800, 'high', 1.2000, 'available', '仓库1'),
('RAW002', '原料B', 2300.0000, 'kg', 95, 0.9500, 'medium', 1.0000, 'available', '仓库1'),
('RAW003', '原料C', 800.0000, 'kg', 220, 0.9200, 'urgent', 1.5000, 'available', '仓库2'),
('SEMI001', '半成品1', 500.0000, 'pcs', 45, 0.9900, 'low', 0.8000, 'available', '中转区'),
('SEMI002', '半成品2', 750.0000, 'pcs', 120, 0.9600, 'medium', 1.1000, 'available', '中转区');

-- 插入系统配置
INSERT IGNORE INTO system_config (config_key, config_value, config_type, description, is_public) VALUES
('system_name', 'Smart Planning', 'string', '系统名称', TRUE),
('system_version', '1.0.0', 'string', '系统版本', TRUE),
('default_language', 'zh', 'string', '默认语言', TRUE),
('max_upload_size', '209715200', 'number', '最大上传文件大小(字节)', FALSE),
('llm_service', 'ollama', 'string', '默认LLM服务', FALSE),
('algorithm_timeout', '1800', 'number', '算法执行超时时间(秒)', FALSE);

-- 创建视图：设备状态统计
CREATE OR REPLACE VIEW equipment_status_summary AS
SELECT
    equipment_type,
    status,
    COUNT(*) as count,
    AVG(efficiency) as avg_efficiency,
    SUM(capacity) as total_capacity
FROM equipment
GROUP BY equipment_type, status;

-- 创建视图：PCI数据统计
CREATE OR REPLACE VIEW pci_data_summary AS
SELECT
    status,
    COUNT(*) as item_count,
    SUM(quantity) as total_quantity,
    AVG(age_days) as avg_age_days,
    COUNT(CASE WHEN age_days > 180 THEN 1 END) as items_over_180_days
FROM pci_data
GROUP BY status;

-- 创建存储过程：更新FIFO优先级
DELIMITER //
CREATE PROCEDURE UpdateFIFOPriority()
BEGIN
    UPDATE pci_data
    SET fifo_priority = CASE
        WHEN age_days > 180 THEN 1
        WHEN age_days > 120 THEN 2
        WHEN age_days > 60 THEN 3
        ELSE 4
    END
    WHERE status = 'available';
END //
DELIMITER ;

-- 设置数据库完成标志
INSERT IGNORE INTO system_config (config_key, config_value, config_type, description, is_public) VALUES
('database_initialized', 'true', 'boolean', '数据库初始化完成标志', FALSE);

-- 提交事务
COMMIT;
