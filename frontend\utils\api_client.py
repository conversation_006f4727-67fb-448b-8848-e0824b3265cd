"""
API客户端
"""

import requests
import streamlit as st
from typing import Dict, Any, Optional, List
import json
import logging

from config.settings import AppConfig

logger = logging.getLogger(__name__)


class APIClient:
    """API客户端类"""

    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.timeout = AppConfig.API_TIMEOUT

    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        # 添加认证令牌
        if 'access_token' in st.session_state:
            headers["Authorization"] = f"Bearer {st.session_state.access_token}"

        return headers

    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """处理响应"""
        try:
            if response.status_code == 401:
                # 令牌过期，清除会话
                self._clear_session()
                st.error("登录已过期，请重新登录")
                st.rerun()

            response.raise_for_status()
            return response.json()

        except requests.exceptions.HTTPError as e:
            if response.status_code == 422:
                # 验证错误
                error_detail = response.json().get('detail', '数据验证失败')
                return {"success": False, "message": error_detail}
            else:
                return {"success": False, "message": f"HTTP错误: {response.status_code}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {str(e)}")
            return {"success": False, "message": "网络连接错误"}

        except json.JSONDecodeError:
            return {"success": False, "message": "响应数据格式错误"}

    def _clear_session(self):
        """清除会话状态"""
        keys_to_clear = ['access_token', 'refresh_token', 'user_info', 'authenticated']
        for key in keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]

    # 认证相关API
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """用户登录"""
        url = f"{self.base_url}/api/v1/auth/login"
        data = {
            "username": username,
            "password": password
        }

        try:
            response = self.session.post(url, data=data)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"登录请求失败: {str(e)}")
            return {"success": False, "message": "登录请求失败"}

    def logout(self) -> Dict[str, Any]:
        """用户登出"""
        url = f"{self.base_url}/api/v1/auth/logout"
        headers = self._get_headers()

        try:
            response = self.session.post(url, headers=headers)
            result = self._handle_response(response)

            # 清除本地会话
            self._clear_session()

            return result
        except Exception as e:
            logger.error(f"登出请求失败: {str(e)}")
            return {"success": False, "message": "登出请求失败"}

    def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """刷新令牌"""
        url = f"{self.base_url}/api/v1/auth/refresh"
        data = {"refresh_token": refresh_token}
        headers = {"Content-Type": "application/json"}

        try:
            response = self.session.post(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"刷新令牌失败: {str(e)}")
            return {"success": False, "message": "刷新令牌失败"}

    # 文件上传API
    def upload_file(self, file_data: bytes, filename: str, file_type: str) -> Dict[str, Any]:
        """上传文件"""
        url = f"{self.base_url}/api/v1/upload/file"

        files = {
            'file': (filename, file_data, file_type)
        }

        headers = {}
        if 'access_token' in st.session_state:
            headers["Authorization"] = f"Bearer {st.session_state.access_token}"

        try:
            response = self.session.post(url, files=files, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            return {"success": False, "message": "文件上传失败"}

    def get_upload_history(self, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取上传历史"""
        url = f"{self.base_url}/api/v1/upload/history"
        params = {"page": page, "page_size": page_size}
        headers = self._get_headers()

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取上传历史失败: {str(e)}")
            return {"success": False, "message": "获取上传历史失败"}

    # 生产计划API
    def get_production_plans(self, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取生产计划列表"""
        url = f"{self.base_url}/api/v1/production-plans"
        params = {"page": page, "page_size": page_size}
        headers = self._get_headers()

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取生产计划失败: {str(e)}")
            return {"success": False, "message": "获取生产计划失败"}

    def create_production_plan(self, plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建生产计划"""
        url = f"{self.base_url}/api/v1/production-plans"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=plan_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"创建生产计划失败: {str(e)}")
            return {"success": False, "message": "创建生产计划失败"}

    def execute_planning_algorithm(self, plan_id: str, algorithm_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行规划算法"""
        url = f"{self.base_url}/api/v1/production-plans/{plan_id}/execute"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=algorithm_config, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"执行规划算法失败: {str(e)}")
            return {"success": False, "message": "执行规划算法失败"}

    # 设备管理API
    def get_equipment_list(self, page: int = 1, page_size: int = 20,
                          equipment_type: Optional[str] = None,
                          status_filter: Optional[str] = None,
                          workshop: Optional[str] = None,
                          search: Optional[str] = None) -> Dict[str, Any]:
        """获取设备列表"""
        url = f"{self.base_url}/api/v1/equipment"
        headers = self._get_headers()

        params = {"page": page, "page_size": page_size}
        if equipment_type:
            params["equipment_type"] = equipment_type
        if status_filter:
            params["status_filter"] = status_filter
        if workshop:
            params["workshop"] = workshop
        if search:
            params["search"] = search

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取设备列表失败: {str(e)}")
            return {"success": False, "message": "获取设备列表失败"}

    def get_equipment_detail(self, equipment_id: str) -> Dict[str, Any]:
        """获取设备详情"""
        url = f"{self.base_url}/api/v1/equipment/{equipment_id}"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取设备详情失败: {str(e)}")
            return {"success": False, "message": "获取设备详情失败"}

    def create_equipment(self, equipment_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建设备"""
        url = f"{self.base_url}/api/v1/equipment"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=equipment_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"创建设备失败: {str(e)}")
            return {"success": False, "message": "创建设备失败"}

    def update_equipment(self, equipment_id: str, equipment_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新设备"""
        url = f"{self.base_url}/api/v1/equipment/{equipment_id}"
        headers = self._get_headers()

        try:
            response = self.session.put(url, json=equipment_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"更新设备失败: {str(e)}")
            return {"success": False, "message": "更新设备失败"}

    def delete_equipment(self, equipment_id: str) -> Dict[str, Any]:
        """删除设备"""
        url = f"{self.base_url}/api/v1/equipment/{equipment_id}"
        headers = self._get_headers()

        try:
            response = self.session.delete(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"删除设备失败: {str(e)}")
            return {"success": False, "message": "删除设备失败"}

    def get_equipment_status_overview(self) -> Dict[str, Any]:
        """获取设备状态概览"""
        url = f"{self.base_url}/api/v1/equipment/status/overview"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取设备状态概览失败: {str(e)}")
            return {"success": False, "message": "获取设备状态概览失败"}

    def get_equipment_utilization_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取设备利用率统计"""
        url = f"{self.base_url}/api/v1/equipment/utilization/statistics"
        headers = self._get_headers()
        params = {"days": days}

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取设备利用率统计失败: {str(e)}")
            return {"success": False, "message": "获取设备利用率统计失败"}

    def schedule_equipment_maintenance(self, equipment_id: str, maintenance_data: Dict[str, Any]) -> Dict[str, Any]:
        """安排设备维护"""
        url = f"{self.base_url}/api/v1/equipment/{equipment_id}/maintenance"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=maintenance_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"安排设备维护失败: {str(e)}")
            return {"success": False, "message": "安排设备维护失败"}

    # LLM聊天API
    def chat_with_llm(self, message: str, context: Optional[Dict[str, Any]] = None,
                     model: Optional[str] = None, session_id: Optional[str] = None,
                     temperature: Optional[float] = None, max_tokens: Optional[int] = None) -> Dict[str, Any]:
        """与LLM聊天"""
        url = f"{self.base_url}/api/v1/llm/chat"
        headers = self._get_headers()

        data = {
            "message": message,
            "context": context or {}
        }

        if model:
            data["model"] = model
        if session_id:
            data["session_id"] = session_id
        if temperature is not None:
            data["temperature"] = temperature
        if max_tokens is not None:
            data["max_tokens"] = max_tokens

        try:
            response = self.session.post(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"LLM聊天失败: {str(e)}")
            return {"success": False, "message": "LLM聊天失败"}

    def get_chat_history(self, page: int = 1, page_size: int = 20,
                        session_id: Optional[str] = None) -> Dict[str, Any]:
        """获取聊天历史"""
        url = f"{self.base_url}/api/v1/llm/history"
        headers = self._get_headers()

        params = {"page": page, "page_size": page_size}
        if session_id:
            params["session_id"] = session_id

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取聊天历史失败: {str(e)}")
            return {"success": False, "message": "获取聊天历史失败"}

    def delete_chat_session(self, session_id: str) -> Dict[str, Any]:
        """删除聊天会话"""
        url = f"{self.base_url}/api/v1/llm/history/{session_id}"
        headers = self._get_headers()

        try:
            response = self.session.delete(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"删除聊天会话失败: {str(e)}")
            return {"success": False, "message": "删除聊天会话失败"}

    def get_llm_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        url = f"{self.base_url}/api/v1/llm/config"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取LLM配置失败: {str(e)}")
            return {"success": False, "message": "获取LLM配置失败"}

    def update_llm_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新LLM配置"""
        url = f"{self.base_url}/api/v1/llm/config"
        headers = self._get_headers()

        try:
            response = self.session.put(url, json=config_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"更新LLM配置失败: {str(e)}")
            return {"success": False, "message": "更新LLM配置失败"}

    def get_available_models(self) -> Dict[str, Any]:
        """获取可用模型列表"""
        url = f"{self.base_url}/api/v1/llm/models"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取模型列表失败: {str(e)}")
            return {"success": False, "message": "获取模型列表失败"}

    def analyze_data_with_llm(self, data: Dict[str, Any], analysis_type: str,
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """使用LLM进行数据分析"""
        url = f"{self.base_url}/api/v1/llm/analyze"
        headers = self._get_headers()

        request_data = {
            "data": data,
            "analysis_type": analysis_type,
            "context": context or {}
        }

        try:
            response = self.session.post(url, json=request_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"LLM数据分析失败: {str(e)}")
            return {"success": False, "message": "LLM数据分析失败"}

    def get_optimization_suggestions(self, plan_data: Dict[str, Any],
                                   constraints: Optional[Dict[str, Any]] = None,
                                   objectives: Optional[List[str]] = None,
                                   context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """获取优化建议"""
        url = f"{self.base_url}/api/v1/llm/suggest"
        headers = self._get_headers()

        request_data = {
            "plan_data": plan_data,
            "constraints": constraints or {},
            "objectives": objectives or [],
            "context": context or {}
        }

        try:
            response = self.session.post(url, json=request_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取优化建议失败: {str(e)}")
            return {"success": False, "message": "获取优化建议失败"}

    def get_llm_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取LLM使用统计"""
        url = f"{self.base_url}/api/v1/llm/statistics"
        headers = self._get_headers()
        params = {"days": days}

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取LLM统计失败: {str(e)}")
            return {"success": False, "message": "获取LLM统计失败"}

    # 计划监控API
    def get_production_monitoring_overview(self) -> Dict[str, Any]:
        """获取生产监控概览"""
        url = f"{self.base_url}/api/v1/monitoring/overview"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取生产监控概览失败: {str(e)}")
            return {"success": False, "message": "获取生产监控概览失败"}

    def get_real_time_metrics(self, metrics: Optional[List[str]] = None) -> Dict[str, Any]:
        """获取实时指标数据"""
        url = f"{self.base_url}/api/v1/monitoring/metrics"
        headers = self._get_headers()

        params = {}
        if metrics:
            params["metrics"] = metrics

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取实时指标失败: {str(e)}")
            return {"success": False, "message": "获取实时指标失败"}

    def get_production_alerts(self, level: Optional[str] = None,
                            status: Optional[str] = None,
                            page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取生产告警"""
        url = f"{self.base_url}/api/v1/monitoring/alerts"
        headers = self._get_headers()

        params = {"page": page, "page_size": page_size}
        if level:
            params["level"] = level
        if status:
            params["status"] = status

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取生产告警失败: {str(e)}")
            return {"success": False, "message": "获取生产告警失败"}

    def update_alert_status(self, alert_id: str, status: str,
                          comment: Optional[str] = None) -> Dict[str, Any]:
        """更新告警状态"""
        url = f"{self.base_url}/api/v1/monitoring/alerts/{alert_id}"
        headers = self._get_headers()

        data = {"status": status}
        if comment:
            data["comment"] = comment

        try:
            response = self.session.put(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"更新告警状态失败: {str(e)}")
            return {"success": False, "message": "更新告警状态失败"}

    def get_production_plans_status(self, date_range: Optional[str] = None) -> Dict[str, Any]:
        """获取生产计划状态"""
        url = f"{self.base_url}/api/v1/monitoring/plans"
        headers = self._get_headers()

        params = {}
        if date_range:
            params["date_range"] = date_range

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取生产计划状态失败: {str(e)}")
            return {"success": False, "message": "获取生产计划状态失败"}

    def adjust_production_plan(self, plan_id: str, adjustments: Dict[str, Any]) -> Dict[str, Any]:
        """调整生产计划"""
        url = f"{self.base_url}/api/v1/monitoring/plans/{plan_id}/adjust"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=adjustments, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"调整生产计划失败: {str(e)}")
            return {"success": False, "message": "调整生产计划失败"}

    def get_resource_utilization(self, resource_type: Optional[str] = None,
                               time_range: str = "today") -> Dict[str, Any]:
        """获取资源利用率"""
        url = f"{self.base_url}/api/v1/monitoring/resources"
        headers = self._get_headers()

        params = {"time_range": time_range}
        if resource_type:
            params["resource_type"] = resource_type

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取资源利用率失败: {str(e)}")
            return {"success": False, "message": "获取资源利用率失败"}

    def get_performance_trends(self, metrics: List[str],
                             time_range: str = "7days") -> Dict[str, Any]:
        """获取性能趋势数据"""
        url = f"{self.base_url}/api/v1/monitoring/trends"
        headers = self._get_headers()

        params = {
            "metrics": metrics,
            "time_range": time_range
        }

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取性能趋势失败: {str(e)}")
            return {"success": False, "message": "获取性能趋势失败"}

    def create_manual_alert(self, alert_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建手动告警"""
        url = f"{self.base_url}/api/v1/monitoring/alerts"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=alert_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"创建手动告警失败: {str(e)}")
            return {"success": False, "message": "创建手动告警失败"}

    # 数据分析API
    def get_production_analytics_overview(self) -> Dict[str, Any]:
        """获取生产分析概览"""
        url = f"{self.base_url}/api/v1/analytics/overview"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取生产分析概览失败: {str(e)}")
            return {"success": False, "message": "获取生产分析概览失败"}

    def get_efficiency_analysis(self, time_range: str = "30days",
                              dimensions: Optional[List[str]] = None) -> Dict[str, Any]:
        """获取效率分析数据"""
        url = f"{self.base_url}/api/v1/analytics/efficiency"
        headers = self._get_headers()

        params = {"time_range": time_range}
        if dimensions:
            params["dimensions"] = dimensions

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取效率分析失败: {str(e)}")
            return {"success": False, "message": "获取效率分析失败"}

    def get_quality_analysis(self, time_range: str = "30days",
                           product_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """获取质量分析数据"""
        url = f"{self.base_url}/api/v1/analytics/quality"
        headers = self._get_headers()

        params = {"time_range": time_range}
        if product_types:
            params["product_types"] = product_types

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取质量分析失败: {str(e)}")
            return {"success": False, "message": "获取质量分析失败"}

    def get_cost_analysis(self, time_range: str = "30days",
                         cost_categories: Optional[List[str]] = None) -> Dict[str, Any]:
        """获取成本分析数据"""
        url = f"{self.base_url}/api/v1/analytics/cost"
        headers = self._get_headers()

        params = {"time_range": time_range}
        if cost_categories:
            params["cost_categories"] = cost_categories

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取成本分析失败: {str(e)}")
            return {"success": False, "message": "获取成本分析失败"}

    def get_equipment_performance_analysis(self, time_range: str = "30days",
                                         equipment_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """获取设备性能分析"""
        url = f"{self.base_url}/api/v1/analytics/equipment"
        headers = self._get_headers()

        params = {"time_range": time_range}
        if equipment_types:
            params["equipment_types"] = equipment_types

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取设备性能分析失败: {str(e)}")
            return {"success": False, "message": "获取设备性能分析失败"}

    def get_predictive_analysis(self, prediction_type: str,
                              time_horizon: str = "30days",
                              parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """获取预测分析"""
        url = f"{self.base_url}/api/v1/analytics/predict"
        headers = self._get_headers()

        data = {
            "prediction_type": prediction_type,
            "time_horizon": time_horizon,
            "parameters": parameters or {}
        }

        try:
            response = self.session.post(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取预测分析失败: {str(e)}")
            return {"success": False, "message": "获取预测分析失败"}

    def get_comparative_analysis(self, analysis_type: str,
                               compare_periods: List[str],
                               metrics: List[str]) -> Dict[str, Any]:
        """获取对比分析"""
        url = f"{self.base_url}/api/v1/analytics/compare"
        headers = self._get_headers()

        data = {
            "analysis_type": analysis_type,
            "compare_periods": compare_periods,
            "metrics": metrics
        }

        try:
            response = self.session.post(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取对比分析失败: {str(e)}")
            return {"success": False, "message": "获取对比分析失败"}

    def generate_analytics_report(self, report_config: Dict[str, Any]) -> Dict[str, Any]:
        """生成分析报告"""
        url = f"{self.base_url}/api/v1/analytics/report"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=report_config, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"生成分析报告失败: {str(e)}")
            return {"success": False, "message": "生成分析报告失败"}

    def export_analytics_data(self, export_config: Dict[str, Any]) -> Dict[str, Any]:
        """导出分析数据"""
        url = f"{self.base_url}/api/v1/analytics/export"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=export_config, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"导出分析数据失败: {str(e)}")
            return {"success": False, "message": "导出分析数据失败"}

    def get_anomaly_detection(self, detection_config: Dict[str, Any]) -> Dict[str, Any]:
        """获取异常检测结果"""
        url = f"{self.base_url}/api/v1/analytics/anomaly"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=detection_config, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取异常检测失败: {str(e)}")
            return {"success": False, "message": "获取异常检测失败"}

    # 用户管理API
    def get_users_list(self, page: int = 1, page_size: int = 20,
                      role_filter: Optional[str] = None,
                      status_filter: Optional[str] = None,
                      search_query: Optional[str] = None) -> Dict[str, Any]:
        """获取用户列表"""
        url = f"{self.base_url}/api/v1/users"
        headers = self._get_headers()

        params = {"page": page, "page_size": page_size}
        if role_filter:
            params["role"] = role_filter
        if status_filter:
            params["status"] = status_filter
        if search_query:
            params["search"] = search_query

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取用户列表失败: {str(e)}")
            return {"success": False, "message": "获取用户列表失败"}

    def get_user_detail(self, user_id: str) -> Dict[str, Any]:
        """获取用户详情"""
        url = f"{self.base_url}/api/v1/users/{user_id}"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取用户详情失败: {str(e)}")
            return {"success": False, "message": "获取用户详情失败"}

    def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建用户"""
        url = f"{self.base_url}/api/v1/users"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=user_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"创建用户失败: {str(e)}")
            return {"success": False, "message": "创建用户失败"}

    def update_user(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户信息"""
        url = f"{self.base_url}/api/v1/users/{user_id}"
        headers = self._get_headers()

        try:
            response = self.session.put(url, json=user_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"更新用户失败: {str(e)}")
            return {"success": False, "message": "更新用户失败"}

    def delete_user(self, user_id: str) -> Dict[str, Any]:
        """删除用户"""
        url = f"{self.base_url}/api/v1/users/{user_id}"
        headers = self._get_headers()

        try:
            response = self.session.delete(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"删除用户失败: {str(e)}")
            return {"success": False, "message": "删除用户失败"}

    def reset_user_password(self, user_id: str, new_password: Optional[str] = None) -> Dict[str, Any]:
        """重置用户密码"""
        url = f"{self.base_url}/api/v1/users/{user_id}/reset-password"
        headers = self._get_headers()

        data = {}
        if new_password:
            data["new_password"] = new_password

        try:
            response = self.session.post(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"重置密码失败: {str(e)}")
            return {"success": False, "message": "重置密码失败"}

    def update_user_status(self, user_id: str, status: str) -> Dict[str, Any]:
        """更新用户状态"""
        url = f"{self.base_url}/api/v1/users/{user_id}/status"
        headers = self._get_headers()

        data = {"status": status}

        try:
            response = self.session.put(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"更新用户状态失败: {str(e)}")
            return {"success": False, "message": "更新用户状态失败"}

    def get_user_permissions(self, user_id: str) -> Dict[str, Any]:
        """获取用户权限"""
        url = f"{self.base_url}/api/v1/users/{user_id}/permissions"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取用户权限失败: {str(e)}")
            return {"success": False, "message": "获取用户权限失败"}

    def update_user_permissions(self, user_id: str, permissions: List[str]) -> Dict[str, Any]:
        """更新用户权限"""
        url = f"{self.base_url}/api/v1/users/{user_id}/permissions"
        headers = self._get_headers()

        data = {"permissions": permissions}

        try:
            response = self.session.put(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"更新用户权限失败: {str(e)}")
            return {"success": False, "message": "更新用户权限失败"}

    def get_user_activity_log(self, user_id: str, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取用户活动日志"""
        url = f"{self.base_url}/api/v1/users/{user_id}/activity"
        headers = self._get_headers()

        params = {"page": page, "page_size": page_size}

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取用户活动日志失败: {str(e)}")
            return {"success": False, "message": "获取用户活动日志失败"}

    def get_users_statistics(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        url = f"{self.base_url}/api/v1/users/statistics"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取用户统计失败: {str(e)}")
            return {"success": False, "message": "获取用户统计失败"}

    # 系统配置API
    def get_system_config(self, config_type: Optional[str] = None) -> Dict[str, Any]:
        """获取系统配置"""
        url = f"{self.base_url}/api/v1/config/system"
        headers = self._get_headers()

        params = {}
        if config_type:
            params["type"] = config_type

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取系统配置失败: {str(e)}")
            return {"success": False, "message": "获取系统配置失败"}

    def update_system_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新系统配置"""
        url = f"{self.base_url}/api/v1/config/system"
        headers = self._get_headers()

        try:
            response = self.session.put(url, json=config_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"更新系统配置失败: {str(e)}")
            return {"success": False, "message": "更新系统配置失败"}

    def get_business_config(self, config_type: Optional[str] = None) -> Dict[str, Any]:
        """获取业务配置"""
        url = f"{self.base_url}/api/v1/config/business"
        headers = self._get_headers()

        params = {}
        if config_type:
            params["type"] = config_type

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取业务配置失败: {str(e)}")
            return {"success": False, "message": "获取业务配置失败"}

    def update_business_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新业务配置"""
        url = f"{self.base_url}/api/v1/config/business"
        headers = self._get_headers()

        try:
            response = self.session.put(url, json=config_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"更新业务配置失败: {str(e)}")
            return {"success": False, "message": "更新业务配置失败"}

    def get_integration_config(self, integration_type: Optional[str] = None) -> Dict[str, Any]:
        """获取集成配置"""
        url = f"{self.base_url}/api/v1/config/integration"
        headers = self._get_headers()

        params = {}
        if integration_type:
            params["type"] = integration_type

        try:
            response = self.session.get(url, params=params, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取集成配置失败: {str(e)}")
            return {"success": False, "message": "获取集成配置失败"}

    def update_integration_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新集成配置"""
        url = f"{self.base_url}/api/v1/config/integration"
        headers = self._get_headers()

        try:
            response = self.session.put(url, json=config_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"更新集成配置失败: {str(e)}")
            return {"success": False, "message": "更新集成配置失败"}

    def test_integration_connection(self, integration_type: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """测试集成连接"""
        url = f"{self.base_url}/api/v1/config/integration/test"
        headers = self._get_headers()

        data = {
            "type": integration_type,
            "config": config_data
        }

        try:
            response = self.session.post(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"测试集成连接失败: {str(e)}")
            return {"success": False, "message": "测试集成连接失败"}

    def get_config_backup_list(self) -> Dict[str, Any]:
        """获取配置备份列表"""
        url = f"{self.base_url}/api/v1/config/backup"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取配置备份列表失败: {str(e)}")
            return {"success": False, "message": "获取配置备份列表失败"}

    def create_config_backup(self, backup_name: str, description: Optional[str] = None) -> Dict[str, Any]:
        """创建配置备份"""
        url = f"{self.base_url}/api/v1/config/backup"
        headers = self._get_headers()

        data = {"name": backup_name}
        if description:
            data["description"] = description

        try:
            response = self.session.post(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"创建配置备份失败: {str(e)}")
            return {"success": False, "message": "创建配置备份失败"}

    def restore_config_backup(self, backup_id: str) -> Dict[str, Any]:
        """恢复配置备份"""
        url = f"{self.base_url}/api/v1/config/backup/{backup_id}/restore"
        headers = self._get_headers()

        try:
            response = self.session.post(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"恢复配置备份失败: {str(e)}")
            return {"success": False, "message": "恢复配置备份失败"}

    def export_config(self, config_types: List[str]) -> Dict[str, Any]:
        """导出配置"""
        url = f"{self.base_url}/api/v1/config/export"
        headers = self._get_headers()

        data = {"types": config_types}

        try:
            response = self.session.post(url, json=data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"导出配置失败: {str(e)}")
            return {"success": False, "message": "导出配置失败"}

    def import_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """导入配置"""
        url = f"{self.base_url}/api/v1/config/import"
        headers = self._get_headers()

        try:
            response = self.session.post(url, json=config_data, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"导入配置失败: {str(e)}")
            return {"success": False, "message": "导入配置失败"}

    # 系统管理API
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        url = f"{self.base_url}/api/v1/system/status"
        headers = self._get_headers()

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"获取系统状态失败: {str(e)}")
            return {"success": False, "message": "获取系统状态失败"}

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        url = f"{self.base_url}/health"

        try:
            response = self.session.get(url, timeout=5)
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return {"success": False, "message": "健康检查失败"}
