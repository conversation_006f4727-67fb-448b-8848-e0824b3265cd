"""
可扩展数据源服务
支持动态添加新数据源类型、自定义字段配置和多种数据库类型
"""

import json
import sqlite3
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
import logging
import importlib

logger = logging.getLogger(__name__)


class ExtensibleDataSourceService:
    """可扩展数据源服务类"""

    def __init__(self):
        self.config_db_path = "extensible_data_config.db"
        self.supported_db_types = {
            "mysql": "MySQL数据库",
            "postgresql": "PostgreSQL数据库", 
            "sqlserver": "Microsoft SQL Server",
            "oracle": "Oracle数据库",
            "sqlite": "SQLite数据库",
            "ssas": "SQL Server Analysis Services",
            "mongodb": "MongoDB数据库",
            "redis": "Redis缓存数据库",
            "elasticsearch": "Elasticsearch搜索引擎",
            "clickhouse": "ClickHouse分析数据库"
        }
        self._init_config_database()

    def _init_config_database(self):
        """初始化配置数据库"""
        conn = sqlite3.connect(self.config_db_path)
        cursor = conn.cursor()

        # 数据源类型定义表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_source_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type_name TEXT UNIQUE NOT NULL,
                display_name TEXT NOT NULL,
                description TEXT,
                connection_template TEXT,
                field_schema TEXT,
                enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 自定义字段定义表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS custom_field_definitions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source_type TEXT NOT NULL,
                field_name TEXT NOT NULL,
                field_type TEXT NOT NULL,
                display_name TEXT,
                description TEXT,
                validation_rules TEXT,
                default_value TEXT,
                is_required INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(source_type, field_name)
            )
        ''')

        # 数据源实例配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_source_instances (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                instance_name TEXT UNIQUE NOT NULL,
                source_type TEXT NOT NULL,
                connection_config TEXT NOT NULL,
                field_mapping TEXT,
                enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 预定义数据源类型
        self._init_predefined_data_sources(cursor)
        
        conn.commit()
        conn.close()

    def _init_predefined_data_sources(self, cursor):
        """初始化预定义数据源类型"""
        predefined_sources = [
            {
                "type_name": "pci_data",
                "display_name": "PCI数据",
                "description": "生产控制信息系统数据",
                "connection_template": json.dumps({
                    "database_type": "mysql",
                    "host": "localhost",
                    "port": 3306,
                    "database": "pci_system",
                    "table": "fs_inventory"
                }),
                "field_schema": json.dumps([
                    {"name": "fs_id", "type": "string", "display": "FS编号", "required": True},
                    {"name": "container_type", "type": "string", "display": "容器类型", "required": True},
                    {"name": "material_code", "type": "string", "display": "物料编码", "required": True},
                    {"name": "quantity", "type": "integer", "display": "数量", "required": True},
                    {"name": "days_old", "type": "integer", "display": "库龄(天)", "required": False},
                    {"name": "location", "type": "string", "display": "位置", "required": False},
                    {"name": "batch_number", "type": "string", "display": "批次号", "required": False}
                ])
            },
            {
                "type_name": "equipment_data",
                "display_name": "设备数据",
                "description": "生产设备状态和性能数据",
                "connection_template": json.dumps({
                    "database_type": "sqlserver",
                    "host": "localhost",
                    "port": 1433,
                    "database": "equipment_system",
                    "table": "equipment_status"
                }),
                "field_schema": json.dumps([
                    {"name": "equipment_id", "type": "string", "display": "设备编号", "required": True},
                    {"name": "equipment_name", "type": "string", "display": "设备名称", "required": True},
                    {"name": "equipment_type", "type": "string", "display": "设备类型", "required": True},
                    {"name": "status", "type": "string", "display": "设备状态", "required": True},
                    {"name": "workshop", "type": "string", "display": "车间", "required": False},
                    {"name": "capacity", "type": "float", "display": "产能", "required": False},
                    {"name": "current_utilization", "type": "float", "display": "当前利用率", "required": False}
                ])
            },
            {
                "type_name": "ppd_data",
                "display_name": "PPD数据",
                "description": "生产计划数据(Production Planning Data)",
                "connection_template": json.dumps({
                    "database_type": "ssas",
                    "server": "localhost",
                    "database": "ProductionCube",
                    "cube": "ProductionPlan"
                }),
                "field_schema": json.dumps([
                    {"name": "plan_id", "type": "string", "display": "计划编号", "required": True},
                    {"name": "product_code", "type": "string", "display": "产品编码", "required": True},
                    {"name": "planned_quantity", "type": "integer", "display": "计划数量", "required": True},
                    {"name": "start_date", "type": "date", "display": "开始日期", "required": True},
                    {"name": "end_date", "type": "date", "display": "结束日期", "required": True},
                    {"name": "priority", "type": "integer", "display": "优先级", "required": False},
                    {"name": "department", "type": "string", "display": "部门", "required": False}
                ])
            }
        ]

        for source in predefined_sources:
            cursor.execute('''
                INSERT OR IGNORE INTO data_source_types 
                (type_name, display_name, description, connection_template, field_schema)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                source["type_name"],
                source["display_name"], 
                source["description"],
                source["connection_template"],
                source["field_schema"]
            ))

    def add_custom_data_source_type(self, type_name: str, display_name: str, 
                                   description: str, connection_template: Dict[str, Any],
                                   field_schema: List[Dict[str, Any]]) -> bool:
        """添加自定义数据源类型"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO data_source_types 
                (type_name, display_name, description, connection_template, field_schema)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                type_name,
                display_name,
                description,
                json.dumps(connection_template),
                json.dumps(field_schema)
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"添加自定义数据源类型失败: {e}")
            return False

    def add_custom_field_to_source(self, source_type: str, field_name: str, 
                                  field_type: str, display_name: str = None,
                                  description: str = None, validation_rules: Dict[str, Any] = None,
                                  default_value: str = None, is_required: bool = False) -> bool:
        """为数据源添加自定义字段"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO custom_field_definitions 
                (source_type, field_name, field_type, display_name, description, 
                 validation_rules, default_value, is_required)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                source_type,
                field_name,
                field_type,
                display_name or field_name,
                description,
                json.dumps(validation_rules) if validation_rules else None,
                default_value,
                int(is_required)
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"添加自定义字段失败: {e}")
            return False

    def create_data_source_instance(self, instance_name: str, source_type: str,
                                   connection_config: Dict[str, Any],
                                   field_mapping: Dict[str, str] = None) -> bool:
        """创建数据源实例"""
        try:
            conn = sqlite3.connect(self.config_db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO data_source_instances 
                (instance_name, source_type, connection_config, field_mapping, updated_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                instance_name,
                source_type,
                json.dumps(connection_config),
                json.dumps(field_mapping) if field_mapping else None,
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"创建数据源实例失败: {e}")
            return False

    def get_available_data_source_types(self) -> List[Dict[str, Any]]:
        """获取可用的数据源类型"""
        conn = sqlite3.connect(self.config_db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT type_name, display_name, description, connection_template, field_schema, enabled
            FROM data_source_types
            WHERE enabled = 1
        ''')

        types = []
        for row in cursor.fetchall():
            types.append({
                "type_name": row[0],
                "display_name": row[1],
                "description": row[2],
                "connection_template": json.loads(row[3]),
                "field_schema": json.loads(row[4]),
                "enabled": bool(row[5])
            })

        conn.close()
        return types

    def get_custom_fields_for_source(self, source_type: str) -> List[Dict[str, Any]]:
        """获取数据源的自定义字段"""
        conn = sqlite3.connect(self.config_db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT field_name, field_type, display_name, description, 
                   validation_rules, default_value, is_required
            FROM custom_field_definitions
            WHERE source_type = ?
        ''', (source_type,))

        fields = []
        for row in cursor.fetchall():
            fields.append({
                "field_name": row[0],
                "field_type": row[1],
                "display_name": row[2],
                "description": row[3],
                "validation_rules": json.loads(row[4]) if row[4] else {},
                "default_value": row[5],
                "is_required": bool(row[6])
            })

        conn.close()
        return fields

    def get_data_source_instances(self, source_type: str = None) -> List[Dict[str, Any]]:
        """获取数据源实例"""
        conn = sqlite3.connect(self.config_db_path)
        cursor = conn.cursor()

        if source_type:
            cursor.execute('''
                SELECT instance_name, source_type, connection_config, field_mapping, enabled
                FROM data_source_instances
                WHERE source_type = ? AND enabled = 1
            ''', (source_type,))
        else:
            cursor.execute('''
                SELECT instance_name, source_type, connection_config, field_mapping, enabled
                FROM data_source_instances
                WHERE enabled = 1
            ''')

        instances = []
        for row in cursor.fetchall():
            instances.append({
                "instance_name": row[0],
                "source_type": row[1],
                "connection_config": json.loads(row[2]),
                "field_mapping": json.loads(row[3]) if row[3] else {},
                "enabled": bool(row[4])
            })

        conn.close()
        return instances

    def validate_connection_config(self, source_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证连接配置"""
        validation_result = {"valid": True, "errors": []}

        # 根据数据库类型验证配置
        db_type = config.get("database_type", "").lower()

        if db_type in ["mysql", "postgresql", "sqlserver", "oracle"]:
            required_fields = ["host", "port", "database", "username"]
            for field in required_fields:
                if not config.get(field):
                    validation_result["errors"].append(f"缺少必需字段: {field}")

        elif db_type == "ssas":
            required_fields = ["server", "database", "cube"]
            for field in required_fields:
                if not config.get(field):
                    validation_result["errors"].append(f"SSAS缺少必需字段: {field}")

        elif db_type == "mongodb":
            required_fields = ["host", "port", "database", "collection"]
            for field in required_fields:
                if not config.get(field):
                    validation_result["errors"].append(f"MongoDB缺少必需字段: {field}")

        elif db_type == "elasticsearch":
            required_fields = ["host", "port", "index"]
            for field in required_fields:
                if not config.get(field):
                    validation_result["errors"].append(f"Elasticsearch缺少必需字段: {field}")

        if validation_result["errors"]:
            validation_result["valid"] = False

        return validation_result

    def test_connection(self, source_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试数据源连接"""
        try:
            db_type = config.get("database_type", "").lower()
            
            if db_type == "mysql":
                return self._test_mysql_connection(config)
            elif db_type == "sqlserver":
                return self._test_sqlserver_connection(config)
            elif db_type == "ssas":
                return self._test_ssas_connection(config)
            elif db_type == "postgresql":
                return self._test_postgresql_connection(config)
            elif db_type == "oracle":
                return self._test_oracle_connection(config)
            else:
                return {"success": False, "message": f"不支持的数据库类型: {db_type}"}

        except Exception as e:
            return {"success": False, "message": f"连接测试失败: {str(e)}"}

    def _test_mysql_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试MySQL连接"""
        try:
            # 这里应该使用实际的MySQL连接库
            # import mysql.connector
            # connection = mysql.connector.connect(**config)
            # connection.close()
            
            # 模拟连接测试
            return {"success": True, "message": "MySQL连接测试成功"}
        except Exception as e:
            return {"success": False, "message": f"MySQL连接失败: {str(e)}"}

    def _test_sqlserver_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试SQL Server连接"""
        try:
            # 这里应该使用实际的SQL Server连接库
            # import pyodbc
            # connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={config['host']};DATABASE={config['database']};UID={config['username']};PWD={config['password']}"
            # connection = pyodbc.connect(connection_string)
            # connection.close()
            
            # 模拟连接测试
            return {"success": True, "message": "SQL Server连接测试成功"}
        except Exception as e:
            return {"success": False, "message": f"SQL Server连接失败: {str(e)}"}

    def _test_ssas_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试SQL Server Analysis Services连接"""
        try:
            # 这里应该使用SSAS连接库，如pyadomd
            # from pyadomd import Pyadomd
            # connection_string = f"Provider=MSOLAP;Data Source={config['server']};Initial Catalog={config['database']}"
            # connection = Pyadomd(connection_string)
            # connection.close()
            
            # 模拟SSAS连接测试
            return {"success": True, "message": "SQL Server Analysis Services连接测试成功"}
        except Exception as e:
            return {"success": False, "message": f"SSAS连接失败: {str(e)}"}

    def _test_postgresql_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试PostgreSQL连接"""
        try:
            # 这里应该使用PostgreSQL连接库
            # import psycopg2
            # connection = psycopg2.connect(**config)
            # connection.close()
            
            # 模拟连接测试
            return {"success": True, "message": "PostgreSQL连接测试成功"}
        except Exception as e:
            return {"success": False, "message": f"PostgreSQL连接失败: {str(e)}"}

    def _test_oracle_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试Oracle连接"""
        try:
            # 这里应该使用Oracle连接库
            # import cx_Oracle
            # dsn = cx_Oracle.makedsn(config['host'], config['port'], service_name=config['service_name'])
            # connection = cx_Oracle.connect(config['username'], config['password'], dsn)
            # connection.close()
            
            # 模拟连接测试
            return {"success": True, "message": "Oracle连接测试成功"}
        except Exception as e:
            return {"success": False, "message": f"Oracle连接失败: {str(e)}"}


# 全局可扩展数据源服务实例
extensible_data_source_service = ExtensibleDataSourceService()
