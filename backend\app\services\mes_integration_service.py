"""
MES系统集成服务
实现与制造执行系统的实时数据对接
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum

import aiohttp
import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, insert

from ..core.config import settings
from ..models.production import ProductionOrder, WorkOrder, Equipment
from ..models.quality import QualityRecord
from ..core.database import get_db_session

logger = logging.getLogger(__name__)


class MESDataType(Enum):
    """MES数据类型"""
    PRODUCTION_ORDER = "production_order"
    WORK_ORDER = "work_order"
    EQUIPMENT_STATUS = "equipment_status"
    QUALITY_DATA = "quality_data"
    MATERIAL_CONSUMPTION = "material_consumption"
    LABOR_TRACKING = "labor_tracking"


@dataclass
class MESConfig:
    """MES系统配置"""
    base_url: str = "http://localhost:8080/mes/api"
    api_key: str = ""
    timeout: int = 30
    sync_interval: int = 60  # 秒
    retry_attempts: int = 3
    batch_size: int = 100


@dataclass
class ProductionOrderData:
    """生产订单数据"""
    order_id: str
    product_code: str
    quantity: int
    planned_start: datetime
    planned_end: datetime
    actual_start: Optional[datetime] = None
    actual_end: Optional[datetime] = None
    status: str = "PLANNED"
    priority: int = 1
    customer_id: Optional[str] = None


@dataclass
class EquipmentStatusData:
    """设备状态数据"""
    equipment_id: str
    status: str  # RUNNING, IDLE, MAINTENANCE, ERROR
    utilization_rate: float
    oee: float  # Overall Equipment Effectiveness
    temperature: Optional[float] = None
    pressure: Optional[float] = None
    speed: Optional[float] = None
    last_maintenance: Optional[datetime] = None
    next_maintenance: Optional[datetime] = None


class MESIntegrationService:
    """MES系统集成服务"""
    
    def __init__(self, db_session: AsyncSession = None):
        self.config = MESConfig()
        self.db = db_session
        self.session = None
        self.is_running = False
        self.sync_tasks = {}
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载MES配置"""
        self.config.base_url = getattr(settings, 'MES_BASE_URL', self.config.base_url)
        self.config.api_key = getattr(settings, 'MES_API_KEY', self.config.api_key)
        self.config.timeout = getattr(settings, 'MES_TIMEOUT', self.config.timeout)
        self.config.sync_interval = getattr(settings, 'MES_SYNC_INTERVAL', self.config.sync_interval)
    
    async def start_integration(self):
        """启动MES集成服务"""
        if self.is_running:
            logger.warning("MES集成服务已在运行")
            return
        
        self.is_running = True
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout),
            headers={'Authorization': f'Bearer {self.config.api_key}'}
        )
        
        # 启动各种数据同步任务
        self.sync_tasks = {
            'production_orders': asyncio.create_task(self._sync_production_orders()),
            'equipment_status': asyncio.create_task(self._sync_equipment_status()),
            'quality_data': asyncio.create_task(self._sync_quality_data()),
            'material_consumption': asyncio.create_task(self._sync_material_consumption())
        }
        
        logger.info("MES集成服务已启动")
    
    async def stop_integration(self):
        """停止MES集成服务"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 取消所有同步任务
        for task_name, task in self.sync_tasks.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                logger.info(f"已取消{task_name}同步任务")
        
        # 关闭HTTP会话
        if self.session:
            await self.session.close()
        
        logger.info("MES集成服务已停止")
    
    async def _sync_production_orders(self):
        """同步生产订单数据"""
        while self.is_running:
            try:
                # 获取MES生产订单数据
                orders_data = await self._fetch_mes_data('/production-orders')
                
                if orders_data:
                    await self._process_production_orders(orders_data)
                
                await asyncio.sleep(self.config.sync_interval)
                
            except Exception as e:
                logger.error(f"同步生产订单失败: {e}")
                await asyncio.sleep(self.config.sync_interval)
    
    async def _sync_equipment_status(self):
        """同步设备状态数据"""
        while self.is_running:
            try:
                # 获取MES设备状态数据
                equipment_data = await self._fetch_mes_data('/equipment/status')
                
                if equipment_data:
                    await self._process_equipment_status(equipment_data)
                
                await asyncio.sleep(self.config.sync_interval // 2)  # 设备状态更频繁更新
                
            except Exception as e:
                logger.error(f"同步设备状态失败: {e}")
                await asyncio.sleep(self.config.sync_interval // 2)
    
    async def _sync_quality_data(self):
        """同步质量数据"""
        while self.is_running:
            try:
                # 获取MES质量数据
                quality_data = await self._fetch_mes_data('/quality/records')
                
                if quality_data:
                    await self._process_quality_data(quality_data)
                
                await asyncio.sleep(self.config.sync_interval * 2)  # 质量数据更新频率较低
                
            except Exception as e:
                logger.error(f"同步质量数据失败: {e}")
                await asyncio.sleep(self.config.sync_interval * 2)
    
    async def _sync_material_consumption(self):
        """同步物料消耗数据"""
        while self.is_running:
            try:
                # 获取MES物料消耗数据
                material_data = await self._fetch_mes_data('/materials/consumption')
                
                if material_data:
                    await self._process_material_consumption(material_data)
                
                await asyncio.sleep(self.config.sync_interval)
                
            except Exception as e:
                logger.error(f"同步物料消耗失败: {e}")
                await asyncio.sleep(self.config.sync_interval)
    
    async def _fetch_mes_data(self, endpoint: str, params: Dict = None) -> Optional[List[Dict]]:
        """从MES系统获取数据"""
        if not self.session:
            return None
        
        url = f"{self.config.base_url}{endpoint}"
        
        for attempt in range(self.config.retry_attempts):
            try:
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('data', [])
                    else:
                        logger.warning(f"MES API返回错误状态: {response.status}")
                        
            except Exception as e:
                logger.error(f"获取MES数据失败 (尝试 {attempt + 1}): {e}")
                if attempt < self.config.retry_attempts - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
        
        return None
    
    async def _process_production_orders(self, orders_data: List[Dict]):
        """处理生产订单数据"""
        try:
            for order_data in orders_data:
                production_order = ProductionOrderData(
                    order_id=order_data['order_id'],
                    product_code=order_data['product_code'],
                    quantity=order_data['quantity'],
                    planned_start=datetime.fromisoformat(order_data['planned_start']),
                    planned_end=datetime.fromisoformat(order_data['planned_end']),
                    actual_start=datetime.fromisoformat(order_data['actual_start']) if order_data.get('actual_start') else None,
                    actual_end=datetime.fromisoformat(order_data['actual_end']) if order_data.get('actual_end') else None,
                    status=order_data.get('status', 'PLANNED'),
                    priority=order_data.get('priority', 1),
                    customer_id=order_data.get('customer_id')
                )
                
                await self._upsert_production_order(production_order)
                
            logger.info(f"已处理 {len(orders_data)} 个生产订单")
            
        except Exception as e:
            logger.error(f"处理生产订单数据失败: {e}")
    
    async def _process_equipment_status(self, equipment_data: List[Dict]):
        """处理设备状态数据"""
        try:
            for eq_data in equipment_data:
                equipment_status = EquipmentStatusData(
                    equipment_id=eq_data['equipment_id'],
                    status=eq_data['status'],
                    utilization_rate=eq_data.get('utilization_rate', 0.0),
                    oee=eq_data.get('oee', 0.0),
                    temperature=eq_data.get('temperature'),
                    pressure=eq_data.get('pressure'),
                    speed=eq_data.get('speed'),
                    last_maintenance=datetime.fromisoformat(eq_data['last_maintenance']) if eq_data.get('last_maintenance') else None,
                    next_maintenance=datetime.fromisoformat(eq_data['next_maintenance']) if eq_data.get('next_maintenance') else None
                )
                
                await self._update_equipment_status(equipment_status)
                
            logger.info(f"已处理 {len(equipment_data)} 个设备状态")
            
        except Exception as e:
            logger.error(f"处理设备状态数据失败: {e}")
    
    async def _process_quality_data(self, quality_data: List[Dict]):
        """处理质量数据"""
        try:
            for q_data in quality_data:
                # 处理质量记录
                await self._insert_quality_record(q_data)
                
            logger.info(f"已处理 {len(quality_data)} 个质量记录")
            
        except Exception as e:
            logger.error(f"处理质量数据失败: {e}")
    
    async def _process_material_consumption(self, material_data: List[Dict]):
        """处理物料消耗数据"""
        try:
            for m_data in material_data:
                # 处理物料消耗记录
                await self._insert_material_consumption(m_data)
                
            logger.info(f"已处理 {len(material_data)} 个物料消耗记录")
            
        except Exception as e:
            logger.error(f"处理物料消耗数据失败: {e}")
    
    async def _upsert_production_order(self, order: ProductionOrderData):
        """插入或更新生产订单"""
        if not self.db:
            return
        
        try:
            # 检查订单是否存在
            stmt = select(ProductionOrder).where(ProductionOrder.order_id == order.order_id)
            result = await self.db.execute(stmt)
            existing_order = result.scalar_one_or_none()
            
            if existing_order:
                # 更新现有订单
                update_stmt = update(ProductionOrder).where(
                    ProductionOrder.order_id == order.order_id
                ).values(
                    status=order.status,
                    actual_start=order.actual_start,
                    actual_end=order.actual_end,
                    updated_at=datetime.utcnow()
                )
                await self.db.execute(update_stmt)
            else:
                # 创建新订单
                new_order = ProductionOrder(
                    order_id=order.order_id,
                    product_code=order.product_code,
                    quantity=order.quantity,
                    planned_start=order.planned_start,
                    planned_end=order.planned_end,
                    actual_start=order.actual_start,
                    actual_end=order.actual_end,
                    status=order.status,
                    priority=order.priority,
                    customer_id=order.customer_id
                )
                self.db.add(new_order)
            
            await self.db.commit()
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新生产订单失败: {e}")
    
    async def _update_equipment_status(self, equipment: EquipmentStatusData):
        """更新设备状态"""
        if not self.db:
            return
        
        try:
            update_stmt = update(Equipment).where(
                Equipment.equipment_id == equipment.equipment_id
            ).values(
                status=equipment.status,
                utilization_rate=equipment.utilization_rate,
                oee=equipment.oee,
                temperature=equipment.temperature,
                pressure=equipment.pressure,
                speed=equipment.speed,
                last_maintenance=equipment.last_maintenance,
                next_maintenance=equipment.next_maintenance,
                updated_at=datetime.utcnow()
            )
            await self.db.execute(update_stmt)
            await self.db.commit()
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新设备状态失败: {e}")
    
    async def _insert_quality_record(self, quality_data: Dict):
        """插入质量记录"""
        if not self.db:
            return
        
        try:
            quality_record = QualityRecord(
                order_id=quality_data['order_id'],
                product_code=quality_data['product_code'],
                inspection_time=datetime.fromisoformat(quality_data['inspection_time']),
                inspector=quality_data.get('inspector'),
                result=quality_data['result'],
                defect_rate=quality_data.get('defect_rate', 0.0),
                notes=quality_data.get('notes')
            )
            self.db.add(quality_record)
            await self.db.commit()
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"插入质量记录失败: {e}")
    
    async def _insert_material_consumption(self, material_data: Dict):
        """插入物料消耗记录"""
        # 实现物料消耗记录插入逻辑
        pass
    
    async def get_real_time_production_status(self) -> Dict[str, Any]:
        """获取实时生产状态"""
        try:
            # 获取当前生产订单状态
            orders_stmt = select(ProductionOrder).where(
                ProductionOrder.status.in_(['RUNNING', 'PLANNED', 'PAUSED'])
            )
            orders_result = await self.db.execute(orders_stmt)
            orders = orders_result.scalars().all()
            
            # 获取设备状态
            equipment_stmt = select(Equipment)
            equipment_result = await self.db.execute(equipment_stmt)
            equipment = equipment_result.scalars().all()
            
            return {
                'production_orders': [
                    {
                        'order_id': order.order_id,
                        'product_code': order.product_code,
                        'status': order.status,
                        'progress': self._calculate_order_progress(order)
                    }
                    for order in orders
                ],
                'equipment_status': [
                    {
                        'equipment_id': eq.equipment_id,
                        'status': eq.status,
                        'utilization_rate': eq.utilization_rate,
                        'oee': eq.oee
                    }
                    for eq in equipment
                ],
                'overall_oee': sum(eq.oee or 0 for eq in equipment) / len(equipment) if equipment else 0,
                'active_orders': len([o for o in orders if o.status == 'RUNNING']),
                'last_updated': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取实时生产状态失败: {e}")
            return {}
    
    def _calculate_order_progress(self, order: ProductionOrder) -> float:
        """计算订单进度"""
        if not order.actual_start:
            return 0.0
        
        if order.actual_end:
            return 100.0
        
        # 基于时间计算进度
        total_duration = (order.planned_end - order.planned_start).total_seconds()
        elapsed_duration = (datetime.utcnow() - order.actual_start).total_seconds()
        
        progress = min(elapsed_duration / total_duration * 100, 99.0) if total_duration > 0 else 0.0
        return round(progress, 1)


# 全局MES集成服务实例
mes_integration_service = MESIntegrationService()
