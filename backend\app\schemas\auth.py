"""
认证相关的Pydantic模式
"""

from typing import Optional, List
from pydantic import BaseModel, EmailStr, validator
import re


class Token(BaseModel):
    """令牌响应模式"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user_info: Optional[dict] = None


class TokenData(BaseModel):
    """令牌数据模式"""
    username: Optional[str] = None
    user_id: Optional[str] = None
    permissions: List[str] = []


class UserLogin(BaseModel):
    """用户登录模式"""
    username: str
    password: str
    
    @validator('username')
    def validate_username(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('用户名不能为空')
        return v.strip()
    
    @validator('password')
    def validate_password(cls, v):
        if not v or len(v) < 6:
            raise ValueError('密码长度不能少于6位')
        return v


class UserRegister(BaseModel):
    """用户注册模式"""
    username: str
    email: EmailStr
    full_name: str
    password: str
    confirm_password: str
    phone: Optional[str] = None
    department: Optional[str] = None
    position: Optional[str] = None
    
    @validator('username')
    def validate_username(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('用户名不能为空')
        
        # 用户名只能包含字母、数字、下划线
        if not re.match(r'^[a-zA-Z0-9_]+$', v):
            raise ValueError('用户名只能包含字母、数字和下划线')
        
        if len(v) < 3 or len(v) > 50:
            raise ValueError('用户名长度必须在3-50个字符之间')
        
        return v.strip()
    
    @validator('full_name')
    def validate_full_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('姓名不能为空')
        
        if len(v.strip()) > 100:
            raise ValueError('姓名长度不能超过100个字符')
        
        return v.strip()
    
    @validator('password')
    def validate_password(cls, v):
        if not v or len(v) < 8:
            raise ValueError('密码长度不能少于8位')
        
        # 密码强度检查：至少包含字母和数字
        if not re.search(r'[a-zA-Z]', v) or not re.search(r'\d', v):
            raise ValueError('密码必须包含字母和数字')
        
        return v
    
    @validator('confirm_password')
    def validate_confirm_password(cls, v, values):
        if 'password' in values and v != values['password']:
            raise ValueError('两次输入的密码不一致')
        return v
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('请输入有效的手机号码')
        return v


class PasswordChange(BaseModel):
    """密码修改模式"""
    current_password: str
    new_password: str
    confirm_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if not v or len(v) < 8:
            raise ValueError('新密码长度不能少于8位')
        
        if not re.search(r'[a-zA-Z]', v) or not re.search(r'\d', v):
            raise ValueError('新密码必须包含字母和数字')
        
        return v
    
    @validator('confirm_password')
    def validate_confirm_password(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('两次输入的新密码不一致')
        return v


class PasswordReset(BaseModel):
    """密码重置模式"""
    email: EmailStr
    
    
class PasswordResetConfirm(BaseModel):
    """密码重置确认模式"""
    token: str
    new_password: str
    confirm_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if not v or len(v) < 8:
            raise ValueError('新密码长度不能少于8位')
        
        if not re.search(r'[a-zA-Z]', v) or not re.search(r'\d', v):
            raise ValueError('新密码必须包含字母和数字')
        
        return v
    
    @validator('confirm_password')
    def validate_confirm_password(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('两次输入的新密码不一致')
        return v


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模式"""
    refresh_token: str
