#!/usr/bin/env python3
"""
最终验证脚本 - 检查所有"Smart APS"引用是否已完全替换为"Smart Planning"
"""

import os
import re
from pathlib import Path
from typing import List, Tuple, Dict

def search_smart_aps_references(directory: Path) -> Dict[str, List[Tuple[int, str]]]:
    """搜索所有Smart APS引用"""
    references = {}
    
    # 要检查的文件扩展名
    extensions = {'.py', '.md', '.yml', '.yaml', '.json', '.txt', '.sql', '.sh', '.bat', '.env'}
    
    # 要排除的目录和文件
    exclude_dirs = {'.git', '__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv'}
    exclude_files = {'final_verification.py', 'final_name_check.py', 'FINAL_SYSTEM_CHECK_REPORT.md'}
    
    # 搜索模式
    patterns = [
        r'Smart\s*APS',
        r'smart[-_]aps',
        r'smartaps',
        r'SMART[-_]APS',
        r'SmartAPS'
    ]
    
    for root, dirs, files in os.walk(directory):
        # 排除特定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            # 排除特定文件
            if file in exclude_files:
                continue
                
            file_path = Path(root) / file
            
            # 只检查指定扩展名的文件
            if file_path.suffix.lower() not in extensions:
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    
                file_references = []
                for line_num, line in enumerate(lines, 1):
                    for pattern in patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            file_references.append((line_num, line.strip()))
                            break
                
                if file_references:
                    references[str(file_path)] = file_references
                            
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
                
    return references

def main():
    """主函数"""
    print("🔍 Smart Planning 系统最终验证")
    print("=" * 60)
    print("检查所有'Smart APS'引用是否已完全替换为'Smart Planning'...")
    print()
    
    # 项目根目录
    project_root = Path(__file__).parent.parent
    
    # 搜索所有引用
    references = search_smart_aps_references(project_root)
    
    if not references:
        print("🎉 恭喜！未发现任何Smart APS引用")
        print("✅ 系统名称已完全统一为Smart Planning！")
        print()
        print("📊 验证结果:")
        print("- 前端页面: ✅ 已统一")
        print("- 后端代码: ✅ 已统一") 
        print("- 配置文件: ✅ 已统一")
        print("- 文档系统: ✅ 已统一")
        print("- 数据库配置: ✅ 已统一")
        print()
        print("🚀 系统已准备好部署！")
        return True
    
    print(f"⚠️  发现 {len(references)} 个文件中仍有Smart APS引用:")
    print("=" * 60)
    
    total_references = 0
    for file_path, file_refs in references.items():
        print(f"\n📁 {file_path}")
        print("-" * 40)
        for line_num, line in file_refs:
            print(f"  行 {line_num}: {line}")
            total_references += 1
    
    print(f"\n📊 统计:")
    print(f"- 涉及文件: {len(references)} 个")
    print(f"- 引用总数: {total_references} 处")
    
    print(f"\n🔧 需要手动修复的文件:")
    for file_path in references.keys():
        print(f"  - {file_path}")
    
    return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
