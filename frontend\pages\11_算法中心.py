"""
统一算法中心 - 整合所有算法功能的统一界面
包含算法执行、性能监控、学习管理、结果分析等功能
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from datetime import datetime, timedelta
import asyncio
import sys
import os

# 添加服务路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

try:
    from unified_algorithm_service import (
        unified_algorithm_service,
        AlgorithmRequest,
        AlgorithmType,
        OptimizationObjective
    )
    from data_integration_service import data_integration_service
except ImportError as e:
    st.error(f"无法导入算法服务模块: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="算法中心",
    page_icon="🧮",
    layout="wide"
)

st.title("🧮 智能算法中心")
st.markdown("### 统一的算法执行、监控和管理平台")

# 侧边栏 - 算法控制面板
with st.sidebar:
    st.markdown("### 🎛️ 算法控制面板")
    
    # 获取算法状态
    algorithm_status = unified_algorithm_service.get_algorithm_status()
    
    # 算法状态显示
    st.markdown("#### 📊 算法状态")
    
    active_count = algorithm_status["active_algorithms"]
    total_executions = algorithm_status["total_executions"]
    
    if active_count > 0:
        st.success(f"🟢 {active_count} 个算法正在运行")
    else:
        st.info("⏸️ 所有算法待机中")
    
    st.metric("总执行次数", total_executions)
    
    # 算法开关
    st.markdown("#### ⚙️ 算法开关")
    
    algo_status = algorithm_status["algorithm_status"]
    for algo_name, status in algo_status.items():
        algo_display_name = {
            "genetic_algorithm": "🧬 遗传算法",
            "simulated_annealing": "🔥 模拟退火",
            "greedy_algorithm": "⚡ 贪心算法",
            "reinforcement_learning": "🧠 强化学习",
            "machine_learning": "🤖 机器学习",
            "hybrid_algorithm": "🔀 混合算法"
        }.get(algo_name, algo_name)
        
        enabled = st.checkbox(algo_display_name, value=status, key=f"algo_{algo_name}")
        if enabled != status:
            # 这里可以添加算法开关逻辑
            st.info(f"算法状态已更新: {algo_display_name}")
    
    st.markdown("---")
    
    # 快速操作
    st.markdown("#### 🚀 快速操作")
    
    if st.button("🧮 综合算法分析", type="primary", use_container_width=True):
        st.session_state.run_comprehensive_analysis = True
        st.rerun()
    
    if st.button("📊 性能基准测试", use_container_width=True):
        st.session_state.run_benchmark = True
        st.rerun()
    
    if st.button("🔄 清理执行历史", use_container_width=True):
        st.session_state.clear_history = True
        st.rerun()

# 主要内容区域
tab1, tab2, tab3, tab4, tab5 = st.tabs([
    "🧮 算法执行", 
    "📊 性能监控", 
    "🎯 结果分析", 
    "📈 学习管理", 
    "⚙️ 系统配置"
])

with tab1:
    st.markdown("#### 🧮 算法执行中心")
    
    # 算法选择和配置
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("##### 🎯 算法配置")
        
        # 算法类型选择
        algorithm_type = st.selectbox(
            "算法类型",
            options=[
                ("genetic_algorithm", "🧬 遗传算法"),
                ("simulated_annealing", "🔥 模拟退火"),
                ("greedy_algorithm", "⚡ 贪心算法"),
                ("reinforcement_learning", "🧠 强化学习"),
                ("machine_learning", "🤖 机器学习"),
                ("hybrid_algorithm", "🔀 混合算法")
            ],
            format_func=lambda x: x[1],
            index=0
        )
        
        # 优化目标选择
        optimization_objective = st.selectbox(
            "优化目标",
            options=[
                ("minimize_makespan", "最小化完工时间"),
                ("maximize_efficiency", "最大化效率"),
                ("minimize_cost", "最小化成本"),
                ("maximize_quality", "最大化质量"),
                ("balanced_multi_objective", "平衡多目标")
            ],
            format_func=lambda x: x[1],
            index=4
        )
        
        # 算法参数配置
        st.markdown("##### ⚙️ 算法参数")
        
        col_a, col_b = st.columns(2)
        
        with col_a:
            time_horizon = st.slider("时间范围(天)", 1, 30, 7)
            if algorithm_type[0] == "genetic_algorithm":
                generations = st.slider("迭代次数", 50, 500, 100)
                population_size = st.slider("种群大小", 20, 100, 50)
            elif algorithm_type[0] == "reinforcement_learning":
                episodes = st.slider("训练回合", 50, 500, 100)
                epsilon = st.slider("探索率", 0.01, 0.5, 0.1)
        
        with col_b:
            if algorithm_type[0] == "genetic_algorithm":
                crossover_rate = st.slider("交叉率", 0.1, 1.0, 0.8)
                mutation_rate = st.slider("变异率", 0.01, 0.3, 0.1)
            elif algorithm_type[0] == "simulated_annealing":
                initial_temp = st.slider("初始温度", 100, 2000, 1000)
                cooling_rate = st.slider("降温率", 0.8, 0.99, 0.95)
        
        # 约束条件
        st.markdown("##### ⚠️ 约束条件")
        constraints_text = st.text_area(
            "约束描述",
            placeholder="请描述生产约束条件，如设备限制、时间窗口、资源约束等...",
            height=100
        )
    
    with col2:
        st.markdown("##### 📊 数据概览")
        
        # 获取集成数据
        try:
            data_context = data_integration_service.get_comprehensive_data_context()
            summary = data_context.get("summary", {})
            
            # 数据摘要卡片
            st.metric("设备数据", f"{summary.get('equipment_status', {}).get('total', 0)}台")
            st.metric("PCI物料", f"{summary.get('pci_status', {}).get('total_fs_items', 0)}个")
            st.metric("约束条件", f"{len(data_context.get('constraints', []))}个")
            
            # 数据质量指示器
            st.markdown("**数据质量**")
            data_quality = summary.get("data_quality", 85)
            st.progress(data_quality / 100)
            st.caption(f"数据完整度: {data_quality}%")
            
        except Exception as e:
            st.error(f"数据获取失败: {e}")
        
        st.markdown("---")
        
        # 执行按钮
        if st.button("🚀 执行算法", type="primary", use_container_width=True):
            st.session_state.execute_algorithm = {
                "algorithm_type": algorithm_type[0],
                "optimization_objective": optimization_objective[0],
                "parameters": {
                    "time_horizon_days": time_horizon,
                    **({"generations": generations, "population_size": population_size, 
                        "crossover_rate": crossover_rate, "mutation_rate": mutation_rate} 
                       if algorithm_type[0] == "genetic_algorithm" else {}),
                    **({"episodes": episodes, "epsilon": epsilon} 
                       if algorithm_type[0] == "reinforcement_learning" else {}),
                    **({"initial_temperature": initial_temp, "cooling_rate": cooling_rate} 
                       if algorithm_type[0] == "simulated_annealing" else {})
                },
                "constraints": {"description": constraints_text} if constraints_text else None
            }
            st.rerun()
    
    # 算法执行结果显示
    if st.session_state.get('execute_algorithm'):
        st.markdown("---")
        st.markdown("#### 🔄 算法执行中...")
        
        # 显示执行进度
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # 模拟算法执行过程
        execution_steps = [
            ("初始化算法环境", 20),
            ("加载数据和约束", 40),
            ("执行算法优化", 70),
            ("生成结果报告", 90),
            ("完成执行", 100)
        ]
        
        for step_name, progress in execution_steps:
            status_text.text(f"正在执行: {step_name}...")
            progress_bar.progress(progress)
            # time.sleep(0.5)  # 在实际应用中这里会是真实的算法执行
        
        status_text.text("✅ 算法执行完成！")
        
        # 模拟算法结果
        algo_config = st.session_state.execute_algorithm
        
        # 创建模拟结果
        mock_result = {
            "success": True,
            "algorithm_type": algo_config["algorithm_type"],
            "optimization_objective": algo_config["optimization_objective"],
            "execution_time": np.random.uniform(1.0, 10.0),
            "result_data": {
                "plan_data": {
                    "schedule": [
                        {"order_id": f"ORD{i:03d}", "equipment": f"L{(i%4)+1:02d}", 
                         "start_time": f"{i*2}:00", "duration": f"{np.random.randint(2,6)}h"}
                        for i in range(5)
                    ],
                    "performance_metrics": {
                        "equipment_utilization": np.random.uniform(80, 95),
                        "total_orders": 5,
                        "on_time_delivery_rate": np.random.uniform(90, 98)
                    }
                },
                "evaluation": {
                    "overall_score": np.random.uniform(80, 95),
                    "metrics": {
                        "efficiency_score": np.random.uniform(85, 95),
                        "cost_score": np.random.uniform(80, 90),
                        "delivery_score": np.random.uniform(90, 98),
                        "quality_score": np.random.uniform(85, 95)
                    }
                }
            },
            "performance_metrics": {
                "result_quality": np.random.uniform(0.8, 0.95),
                "algorithm_efficiency": np.random.uniform(0.7, 0.9),
                "resource_utilization": np.random.uniform(0.8, 0.95),
                "constraint_satisfaction": np.random.uniform(0.9, 1.0)
            }
        }
        
        st.session_state.algorithm_result = mock_result
        st.session_state.execute_algorithm = None
        
        st.success("🎉 算法执行成功！")
        st.balloons()

with tab2:
    st.markdown("#### 📊 算法性能监控")
    
    # 获取性能摘要
    performance_summary = unified_algorithm_service.get_performance_summary()
    
    if "algorithm_performance" in performance_summary:
        # 性能概览
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "总执行次数",
                performance_summary["total_executions"],
                delta="累计"
            )
        
        with col2:
            success_rate = performance_summary["overall_success_rate"] * 100
            st.metric(
                "总体成功率",
                f"{success_rate:.1f}%",
                delta="优秀" if success_rate > 90 else "良好" if success_rate > 80 else "需改进"
            )
        
        with col3:
            algo_count = len(performance_summary["algorithm_performance"])
            st.metric(
                "活跃算法",
                f"{algo_count}个",
                delta="多样化"
            )
        
        with col4:
            # 计算平均执行时间
            avg_times = [stats["avg_time"] for stats in performance_summary["algorithm_performance"].values()]
            avg_time = sum(avg_times) / len(avg_times) if avg_times else 0
            st.metric(
                "平均执行时间",
                f"{avg_time:.1f}s",
                delta="高效" if avg_time < 5 else "正常"
            )
        
        # 算法性能对比图表
        st.markdown("##### 📈 算法性能对比")
        
        algo_performance = performance_summary["algorithm_performance"]
        
        # 准备图表数据
        algorithms = []
        success_rates = []
        avg_times = []
        total_executions = []
        
        for algo_name, stats in algo_performance.items():
            algorithms.append(algo_name)
            success_rates.append(stats["success_rate"] * 100)
            avg_times.append(stats["avg_time"])
            total_executions.append(stats["total_executions"])
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 成功率对比
            fig_success = px.bar(
                x=algorithms,
                y=success_rates,
                title="算法成功率对比",
                labels={'x': '算法类型', 'y': '成功率 (%)'},
                color=success_rates,
                color_continuous_scale="Viridis"
            )
            fig_success.update_layout(height=300)
            st.plotly_chart(fig_success, use_container_width=True)
        
        with col2:
            # 执行时间对比
            fig_time = px.bar(
                x=algorithms,
                y=avg_times,
                title="平均执行时间对比",
                labels={'x': '算法类型', 'y': '执行时间 (秒)'},
                color=avg_times,
                color_continuous_scale="Reds"
            )
            fig_time.update_layout(height=300)
            st.plotly_chart(fig_time, use_container_width=True)
        
        # 详细性能表格
        st.markdown("##### 📋 详细性能统计")
        
        performance_data = []
        for algo_name, stats in algo_performance.items():
            performance_data.append({
                "算法": algo_name,
                "总执行次数": stats["total_executions"],
                "成功次数": stats["successful_executions"],
                "成功率": f"{stats['success_rate']*100:.1f}%",
                "平均执行时间": f"{stats['avg_time']:.2f}s",
                "总执行时间": f"{stats['total_time']:.2f}s"
            })
        
        df_performance = pd.DataFrame(performance_data)
        st.dataframe(df_performance, use_container_width=True, hide_index=True)
    
    else:
        st.info("📊 暂无性能数据，请先执行算法以生成性能统计")

with tab3:
    st.markdown("#### 🎯 算法结果分析")
    
    # 显示最新算法结果
    if st.session_state.get('algorithm_result'):
        result = st.session_state.algorithm_result
        
        if result["success"]:
            # 基本信息
            st.markdown("##### 📋 执行信息")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("算法类型", result["algorithm_type"])
            with col2:
                st.metric("优化目标", result["optimization_objective"])
            with col3:
                st.metric("执行时间", f"{result['execution_time']:.2f}s")
            with col4:
                overall_score = result["result_data"]["evaluation"]["overall_score"]
                st.metric("综合评分", f"{overall_score:.1f}", delta="优秀" if overall_score > 90 else "良好")
            
            # 性能指标雷达图
            st.markdown("##### 📊 性能指标分析")
            
            metrics = result["result_data"]["evaluation"]["metrics"]
            categories = ['效率', '成本', '交期', '质量']
            values = [
                metrics["efficiency_score"],
                metrics["cost_score"],
                metrics["delivery_score"],
                metrics["quality_score"]
            ]
            
            fig_radar = go.Figure()
            
            fig_radar.add_trace(go.Scatterpolar(
                r=values,
                theta=categories,
                fill='toself',
                name='算法结果',
                line_color='blue'
            ))
            
            # 添加基准线
            baseline_values = [85, 80, 90, 85]
            fig_radar.add_trace(go.Scatterpolar(
                r=baseline_values,
                theta=categories,
                fill='toself',
                name='基准水平',
                line_color='red',
                line_dash='dash'
            ))
            
            fig_radar.update_layout(
                polar=dict(
                    radialaxis=dict(
                        visible=True,
                        range=[0, 100]
                    )),
                showlegend=True,
                title="性能指标雷达图",
                height=400
            )
            
            st.plotly_chart(fig_radar, use_container_width=True)
            
            # 详细结果
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("##### 📅 生产计划")
                schedule = result["result_data"]["plan_data"]["schedule"]
                df_schedule = pd.DataFrame(schedule)
                st.dataframe(df_schedule, use_container_width=True, hide_index=True)
            
            with col2:
                st.markdown("##### 📈 关键指标")
                perf_metrics = result["result_data"]["plan_data"]["performance_metrics"]
                
                st.metric("设备利用率", f"{perf_metrics['equipment_utilization']:.1f}%")
                st.metric("订单数量", perf_metrics["total_orders"])
                st.metric("按时交付率", f"{perf_metrics['on_time_delivery_rate']:.1f}%")
                
                # 算法性能指标
                st.markdown("**算法性能**")
                algo_metrics = result["performance_metrics"]
                st.metric("结果质量", f"{algo_metrics['result_quality']:.1%}")
                st.metric("算法效率", f"{algo_metrics['algorithm_efficiency']:.1%}")
                st.metric("约束满足度", f"{algo_metrics['constraint_satisfaction']:.1%}")
        
        else:
            st.error(f"❌ 算法执行失败: {result.get('error_message', '未知错误')}")
    
    else:
        st.info("🎯 请先在'算法执行'标签页中执行算法以查看结果分析")

with tab4:
    st.markdown("#### 📈 学习管理")
    
    # 这里可以集成学习引擎的功能
    st.info("🚧 学习管理功能正在开发中...")
    
    # 学习状态概览
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("学习样本", "1,247", delta="+23")
    
    with col2:
        st.metric("模型准确率", "94.2%", delta="+2.1%")
    
    with col3:
        st.metric("学习进度", "85%", delta="进行中")

with tab5:
    st.markdown("#### ⚙️ 系统配置")
    
    # 算法服务配置
    config = algorithm_status["config"]
    
    st.markdown("##### 🔧 服务配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        max_concurrent = st.slider(
            "最大并发算法数",
            1, 10,
            config["max_concurrent_algorithms"]
        )
        
        timeout = st.slider(
            "算法超时时间(秒)",
            60, 1800,
            config["algorithm_timeout"]
        )
    
    with col2:
        auto_learning = st.checkbox(
            "启用自动学习",
            config["auto_learning_enabled"]
        )
        
        result_caching = st.checkbox(
            "启用结果缓存",
            config["result_caching"]
        )
        
        performance_monitoring = st.checkbox(
            "启用性能监控",
            config["performance_monitoring"]
        )
    
    if st.button("💾 保存配置"):
        st.success("配置已保存")
    
    st.markdown("---")
    
    # 系统维护
    st.markdown("##### 🛠️ 系统维护")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🧹 清理缓存", use_container_width=True):
            st.success("缓存已清理")
    
    with col2:
        if st.button("📊 导出日志", use_container_width=True):
            st.success("日志已导出")
    
    with col3:
        if st.button("🔄 重启服务", use_container_width=True):
            st.success("服务已重启")

# 处理特殊操作
if st.session_state.get('run_comprehensive_analysis'):
    st.session_state.run_comprehensive_analysis = False
    
    with st.spinner("🧮 正在执行综合算法分析..."):
        # 这里会调用综合分析功能
        st.success("✅ 综合分析完成！请查看结果分析标签页")

if st.session_state.get('run_benchmark'):
    st.session_state.run_benchmark = False
    
    with st.spinner("📊 正在执行性能基准测试..."):
        # 这里会执行基准测试
        st.success("✅ 基准测试完成！请查看性能监控标签页")

if st.session_state.get('clear_history'):
    st.session_state.clear_history = False
    st.success("🗑️ 执行历史已清理")

# 页面底部信息
st.markdown("---")
st.markdown("##### ℹ️ 算法中心说明")

with st.expander("📖 统一算法服务详解"):
    st.markdown("""
    ### 🧮 统一算法中心特性
    
    #### 🎯 核心功能
    - **多算法支持**: 遗传算法、强化学习、机器学习等6种算法
    - **统一管理**: 一个界面管理所有算法功能
    - **性能监控**: 实时监控算法执行状态和性能
    - **结果分析**: 深度分析算法结果和性能指标
    - **自动学习**: 从执行结果中持续学习和优化
    
    #### 🚀 技术优势
    1. **统一架构**: 所有算法使用统一的请求/响应格式
    2. **并发执行**: 支持多算法并发执行
    3. **智能调度**: 自动选择最适合的算法
    4. **性能优化**: 持续监控和优化算法性能
    5. **扩展性强**: 易于添加新的算法类型
    
    #### 📊 应用场景
    - 复杂生产排程优化
    - 多目标决策问题
    - 大规模资源分配
    - 动态环境适应
    - 智能预测分析
    """)
