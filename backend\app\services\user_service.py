"""
用户服务
"""

from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func
from sqlalchemy.orm import selectinload
import logging

from app.models.user import User, Role, Permission
from app.schemas.user import UserCreate, UserUpdate
from app.schemas.auth import UserRegister
from app.core.security import password_manager
from datetime import datetime

logger = logging.getLogger(__name__)


class UserService:
    """用户服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_by_id(self, user_id: str) -> Optional[User]:
        """根据ID获取用户"""
        try:
            stmt = select(User).options(
                selectinload(User.roles).selectinload(Role.permissions)
            ).where(
                User.id == user_id,
                User.is_deleted == False
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取用户失败: {str(e)}")
            return None
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        try:
            stmt = select(User).options(
                selectinload(User.roles).selectinload(Role.permissions)
            ).where(
                User.username == username,
                User.is_deleted == False
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取用户失败: {str(e)}")
            return None
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        try:
            stmt = select(User).where(
                User.email == email,
                User.is_deleted == False
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取用户失败: {str(e)}")
            return None
    
    async def create_user(self, user_data: UserRegister) -> User:
        """创建用户"""
        try:
            # 创建用户对象
            user = User(
                username=user_data.username,
                email=user_data.email,
                full_name=user_data.full_name,
                password_hash=password_manager.get_password_hash(user_data.password),
                phone=user_data.phone,
                department=user_data.department,
                position=user_data.position,
                is_active=True,
                is_superuser=False
            )
            
            self.db.add(user)
            await self.db.flush()  # 获取user.id
            
            # 分配默认角色（查看者）
            default_role = await self._get_role_by_code("viewer")
            if default_role:
                user.roles = [default_role]
            
            await self.db.commit()
            await self.db.refresh(user)
            
            return user
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建用户失败: {str(e)}")
            raise
    
    async def update_user(self, user_id: str, user_data: UserUpdate) -> Optional[User]:
        """更新用户"""
        try:
            user = await self.get_by_id(user_id)
            if not user:
                return None
            
            # 更新用户信息
            update_data = user_data.dict(exclude_unset=True)
            
            # 处理角色更新
            if 'role_ids' in update_data:
                role_ids = update_data.pop('role_ids')
                if role_ids is not None:
                    roles = await self._get_roles_by_ids(role_ids)
                    user.roles = roles
            
            # 更新其他字段
            for field, value in update_data.items():
                if hasattr(user, field):
                    setattr(user, field, value)
            
            user.updated_at = datetime.utcnow()
            
            await self.db.commit()
            await self.db.refresh(user)
            
            return user
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新用户失败: {str(e)}")
            raise
    
    async def delete_user(self, user_id: str) -> bool:
        """删除用户（软删除）"""
        try:
            user = await self.get_by_id(user_id)
            if not user:
                return False
            
            user.soft_delete()
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除用户失败: {str(e)}")
            return False
    
    async def get_users(
        self, 
        page: int = 1, 
        page_size: int = 20,
        search: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> tuple[List[User], int]:
        """获取用户列表"""
        try:
            # 构建查询
            stmt = select(User).options(
                selectinload(User.roles)
            ).where(User.is_deleted == False)
            
            # 搜索条件
            if search:
                search_pattern = f"%{search}%"
                stmt = stmt.where(
                    (User.username.like(search_pattern)) |
                    (User.full_name.like(search_pattern)) |
                    (User.email.like(search_pattern))
                )
            
            # 状态过滤
            if is_active is not None:
                stmt = stmt.where(User.is_active == is_active)
            
            # 获取总数
            count_stmt = select(func.count(User.id)).where(User.is_deleted == False)
            if search:
                search_pattern = f"%{search}%"
                count_stmt = count_stmt.where(
                    (User.username.like(search_pattern)) |
                    (User.full_name.like(search_pattern)) |
                    (User.email.like(search_pattern))
                )
            if is_active is not None:
                count_stmt = count_stmt.where(User.is_active == is_active)
            
            total_result = await self.db.execute(count_stmt)
            total = total_result.scalar()
            
            # 分页
            offset = (page - 1) * page_size
            stmt = stmt.offset(offset).limit(page_size).order_by(User.created_at.desc())
            
            result = await self.db.execute(stmt)
            users = result.scalars().all()
            
            return list(users), total
            
        except Exception as e:
            logger.error(f"获取用户列表失败: {str(e)}")
            return [], 0
    
    async def update_login_info(self, user_id: str, ip_address: str):
        """更新登录信息"""
        try:
            user = await self.get_by_id(user_id)
            if user:
                user.update_login_info(ip_address)
                await self.db.commit()
        except Exception as e:
            logger.error(f"更新登录信息失败: {str(e)}")
            await self.db.rollback()
    
    async def _get_role_by_code(self, code: str) -> Optional[Role]:
        """根据代码获取角色"""
        try:
            stmt = select(Role).where(
                Role.code == code,
                Role.is_active == True,
                Role.is_deleted == False
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取角色失败: {str(e)}")
            return None
    
    async def _get_roles_by_ids(self, role_ids: List[str]) -> List[Role]:
        """根据ID列表获取角色"""
        try:
            stmt = select(Role).where(
                Role.id.in_(role_ids),
                Role.is_active == True,
                Role.is_deleted == False
            )
            result = await self.db.execute(stmt)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"获取角色列表失败: {str(e)}")
            return []
